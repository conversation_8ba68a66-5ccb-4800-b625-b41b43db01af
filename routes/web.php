<?php

use Illuminate\Support\Facades\Route;
// use Illuminate\Database\Eloquent\Builder;
// use Intervention\Image\ImageManagerStatic as Image;
// use Illuminate\Support\Facades\File;
// use Illuminate\Filesystem\Filesystem;
// use Spatie\ImageOptimizer\OptimizerChainFactory;
use App\Http\Controllers\IakController;
use App\Http\Controllers\AjaxController;
use App\Http\Controllers\JobsController;
use App\Http\Controllers\TestController;
use App\Http\Controllers\InfosController;
use App\Http\Controllers\SharpController;
// use App\Http\Controllers\ApiController;
use App\Http\Controllers\ManualController;
// use App\Http\Controllers\SystemController;
use App\Http\Controllers\OrdersController;
use App\Http\Controllers\SystemController;
use App\Http\Controllers\AccountController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\InvoicesController;
use App\Http\Controllers\PurchaseController;
use App\Http\Controllers\AccurateApiController;
use App\Http\Controllers\StockopnameController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\SocialchatApiController;
use App\Http\Controllers\OperatingCostsController;
use App\Http\Controllers\Auth\AuthenticatedSessionController;
// use App\Models\OperatingCost;
// use PhpOffice\PhpSpreadsheet\Calculation\Engine\Operands\Operand;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// use App\Models\Order;
// Route::get('test', function () {
//     return Order::all()->unique('store_id')->pluck("store_id", "id");
// });

Route::redirect('/', url('/login'));

Route::fallback(function () {
    return redirect('/login');
});

// Route::post('/customers/store/{store_id}', function ($store_id) {
//     $customers = \App\Models\Customer::whereHas('addresses', function (Builder $query) use ($store_id) {
//         $query->where('store_id', $store_id);
//     })->where("name", "like", "%" . request("query") . "%")->limit(10)->get();

//     return [
//         "data" => collect($customers)
//             ->map(function ($customer) {
//                 return [
//                     "id" => $customer->id,
//                     "name" => $customer->name
//                 ];
//             })
//             ->values()
//     ];
// });

Route::get('/customersaddresses/store/{store_id}', [SharpController::class, 'customersaddresses']);
Route::get('/area/store/{store_id}', [SharpController::class, 'areabystore']);
Route::get('/search/customers', [SharpController::class, 'searchcustomers']);

// Route::post('/products/store/{store_id}', function ($store_id) {
//     $productstores = \App\Models\ProductStore::where("store_id", $store_id)->with(['product'])->get();

//     return collect($productstores)
//             ->map(function ($productstore) {
//                 return [
//                     "id" => $productstore->product->id,
//                     "code" => $productstore->product->code,
//                     "name" => $productstore->product->name,
//                     "price" => $productstore->local_price ? $productstore->local_price : $productstore->product->price
//                 ];
//             })
//             ->values();
// });

// Route::get('/', function () {
//     return view('welcome');
// });

Route::get('/logout', [AuthenticatedSessionController::class, 'destroy'])->name('do-logout');
Route::get('/after-login', [AccountController::class, 'index'])->middleware(['auth'])->name('after-login');

Route::prefix('manager')->group(function () {
    Route::get('/calendar/{store_slug}', [JobsController::class, 'calendar'])->middleware(['auth'])->name('calendar');
    Route::get('/report/{store_slug}', [JobsController::class, 'report'])->middleware(['auth'])->name('report');
    Route::get('/report/{store_slug}/driver', [JobsController::class, 'reportdriverinsentivelist'])->middleware(['auth'])->name('report.driver.list');
    Route::get('/report/{store_slug}/driver/{ids}', [JobsController::class, 'reportdriverinsentive'])->middleware(['auth'])->name('report.driver');
    Route::get('/report/{store_slug}/lt2', [JobsController::class, 'reportlt2list'])->middleware(['auth'])->name('report.lt2.list');
    Route::get('/report/{store_slug}/lt2/{ids}', [JobsController::class, 'reportlt2insentive'])->middleware(['auth'])->name('report.lt2');
    // Route::get('/report/{store_slug}/driver', [JobsController::class, 'reportdriver'])->middleware(['auth'])->name('report.driver');
    Route::get('/report/{store_slug}/bbro', [JobsController::class, 'reportbbro'])->middleware(['auth'])->name('report.bbro');
    Route::get('/report/{store_slug}/product', [JobsController::class, 'reportproductselect'])->middleware(['auth'])->name('report.product.select');
    Route::get('/report/{store_slug}/product/{ids}', [JobsController::class, 'reportproduct'])->middleware(['auth'])->name('report.product');
    Route::get('/report/{store_slug}/deposit', [JobsController::class, 'reportdeposit'])->middleware(['auth'])->name('report.deposit');
    // ? Stock Opname
    Route::get('/report/{store_slug}/stockopname', [StockopnameController::class, 'index'])->middleware(['auth'])->name('report.stockopname.detail');
    // Route::get('/report/{store_slug}/stockopname/{date}', [StockopnameController::class, 'show'])->middleware(['auth'])->name('report.stockopname.detail');
    // Route::get('/report/{store_slug}/stockopname/{date}/detail', [StockopnameController::class, 'edit'])->middleware(['auth'])->name('report.stockopname.detail');
    Route::post('/stockopname/accstock/product', [StockopnameController::class, 'accstockproduct'])->middleware(['auth'])->name('stockopname.accstock.product');
    Route::put('/stockopname/accsummary/product', [StockopnameController::class, 'accstocksummary'])->middleware(['auth'])->name('stockopname.accstocksummary.product');
    Route::post('/stockopname/create', [StockopnameController::class, 'store'])->middleware(['auth'])->name('report.stockopname.create');
    Route::put('/stockopname/update/{id}', [StockopnameController::class, 'update'])->middleware(['auth'])->name('report.stockopname.update');
    Route::put('/stockopname/confirm/{id}', [StockopnameController::class, 'confirm'])->middleware(['auth'])->name('report.stockopname.confirm');
    // ? -----------------
    Route::post('/generate/report/{store_id}/{date}', [AjaxController::class, 'generatereportdeposit'])->middleware(['auth'])->name('generate.deposit');
    Route::get('/jobs/{store_slug}', [JobsController::class, 'jobs'])->middleware(['auth'])->name('jobs');
    Route::get('/operatingcost-list/{store_slug}', [OperatingCostsController::class, 'index'])->middleware(['auth'])->name('operatingcost-list');
    Route::get('/job-list/{store_slug}', [JobsController::class, 'joblist'])->middleware(['auth'])->name('job-list');
    Route::get('/order-add/{store_slug}', [JobsController::class, 'addjob'])->middleware(['auth'])->name('order-add');
    Route::post('/order-add/{store_slug}', [JobsController::class, 'postjob'])->middleware(['auth'])->name('order-post');
    Route::get('/order-add-vue/{store_slug}', [OrdersController::class, 'create'])->middleware(['auth'])->name('order-add-vue');
    Route::post('/order-post-vue', [OrdersController::class, 'store'])->middleware(['auth'])->name('order-post-vue');
    // Route::get('/order-post-vue', [OrdersController::class, 'store'])->middleware(['auth'])->name('order-store-vue');
    Route::post('/change-sdm-store', [AccountController::class, 'changesdmstore'])->middleware(['auth'])->name('change-sdm-store');
    Route::post('/change-armada-sdm', [AccountController::class, 'changearmadasdm'])->middleware(['auth'])->name('change-armada-sdm');

    Route::get('/operatingcost-add/{store_slug}', [OperatingCostsController::class, 'addoperatingcost'])->middleware(['auth'])->name('operatingcost-add');
    Route::post('/operatingcost-post/{store_slug}', [OperatingCostsController::class, 'postoperatingcost'])->middleware(['auth'])->name('operatingcost-post');
    Route::get('/insentif/driver', [OperatingCostsController::class, 'getInsentifJob'])->middleware(['auth'])->name('get-insentif-driver');

    Route::get('/purchase-add/{store_slug}', [PurchaseController::class, 'addpurchase'])->middleware(['auth'])->name('purchase-add');
    Route::post('/purchase-post/{store_id}', [PurchaseController::class, 'postpurchase'])->middleware(['auth'])->name('purchase-post');

    Route::get('/invoice-add/{store_slug}', [InvoicesController::class, 'addinvoice'])->middleware(['auth'])->name('invoice-add');
    Route::get('/invoice-get-customers/{store_id}', [InvoicesController::class, 'getcustomers'])->middleware(['auth'])->name('invoice-get-customers');
    Route::get('/invoice-get-orders/{customer_id}', [InvoicesController::class, 'getorders'])->middleware(['auth'])->name('invoice-get-orders');
    Route::post('/invoice-post/{store_slug}', [InvoicesController::class, 'postinvoice'])->middleware(['auth'])->name('invoice-post');
    Route::get('/invoice-download/{invoice_id}', [InvoicesController::class, 'downloadinvoice'])->middleware(['auth'])->name('invoice-download');
    Route::get('/invoice-download-manual', [InvoicesController::class, 'downloadinvoicemanual'])->middleware(['auth'])->name('invoice-download-manual');
    Route::post('/invoice-confirm', [InvoicesController::class, 'confirminvoice'])->middleware(['auth'])->name('invoice-confirm');
    Route::get('/invoice-cancel/{invoice_id}', [InvoicesController::class, 'cancelinvoice'])->middleware(['auth'])->name('invoice-cancel');
    // Route::get('/invoice-list/{store_slug}', [InvoicesController::class, 'invoicelist'])->middleware(['auth'])->name('invoice-list');

    Route::get(
        '/order-edit',
        function () {
            return view('manager.order-edit');
        }
    )->middleware(['auth'])->name('order-edit');
    Route::get(
        '/drivers/{store_slug}',
        function () {
            return view('manager.drivers');
        }
    )->middleware(['auth'])->name('drivers');
    Route::get(
        '/driver-jobs/{store_slug}',
        function () {
            return view('manager.driver-jobs');
        }
    )->middleware(['auth'])->name('driver-jobs');
    Route::get('/account/{store_slug}', [AccountController::class, 'show'])->middleware(['auth'])->name('account');
    Route::get('/account/{store_slug}/sdm/{employee_id}', [AccountController::class, 'sdm'])->middleware(['auth'])->name('sdm');
    Route::get('/account/{store_slug}/armada/{armada_id}', [AccountController::class, 'armada'])->middleware(['auth'])->name('armada');
    Route::post('/ajax/order-set-driver', [AjaxController::class, 'ordersetdriver'])->middleware(['auth'])->name('order-set-driver');
    Route::post('/ajax/marketing-set-driver', [AjaxController::class, 'marketingsetdriver'])->middleware(['auth'])->name('marketing-set-driver');
    // Route::post('/ajax/order-set-status', [AjaxController::class, 'ordersetstatus'])->middleware(['auth'])->name('order-set-status');
    Route::post('/ajax/submit-finish-job', [AjaxController::class, 'orderfinish'])->middleware(['auth'])->name('submit-finish-job');
    Route::post('/ajax/submit-offline-jobs', [AjaxController::class, 'offlineordersubmit'])->middleware(['auth'])->name('submit-offline-jobs');
    Route::post('/ajax/submit-cancel-job', [AjaxController::class, 'ordercancel'])->middleware(['auth'])->name('submit-cancel-job');
    Route::post('/ajax/submit-cancel-job-marketing', [AjaxController::class, 'marketingcancel'])->middleware(['auth'])->name('submit-cancel-job-marketing');
    Route::post('/ajax/submit-ganti-tanggal-job', [AjaxController::class, 'orderchangedate'])->middleware(['auth'])->name('submit-ganti-tanggal-job');
    Route::post('/ajax/submit-confirm-job', [AjaxController::class, 'orderconfirm'])->middleware(['auth'])->name('submit-confirm-job');
    Route::post('/ajax/submit-checkin-job-marketing', [AjaxController::class, 'ordercheckinmarketing'])->middleware(['auth'])->name('submit-checkin-job-marketing');
    Route::post('/ajax/submit-bbro', [AjaxController::class, 'submitbbro'])->middleware(['auth'])->name('submit-bbro');
    Route::post('/ajax/delete-photo-marketing', [AjaxController::class, 'deletephotomarketing'])->middleware(['auth'])->name('delete-photo-marketing');
    Route::post('/ajax/upload-photo-marketing', [AjaxController::class, 'uploadphotomarketing'])->middleware(['auth'])->name('upload-photo-marketing');
    Route::post('/ajax/submit-upload-paymentproof', [AjaxController::class, 'uploadpaymentproof'])->middleware(['auth'])->name('submit-upload-paymentproof');
    Route::post('/ajax/submit-upload-address', [AjaxController::class, 'uploadaddress'])->middleware(['auth'])->name('submit-upload-address');
    Route::get('/ajax/search-customer', [AjaxController::class, 'searchcustomer'])->middleware(['auth'])->name('search-customer');
    Route::get('/ajax/search-product', [AjaxController::class, 'searchproduct'])->middleware(['auth'])->name('search-product');
    Route::get('/ajax/search-area', [AjaxController::class, 'searcharea'])->middleware(['auth'])->name('search-area');
    Route::get('/ajax/get-drivers', [AjaxController::class, 'getdrivers'])->middleware(['auth'])->name('get-drivers');
    Route::get('/ajax/get-coordinate-gmap', [AjaxController::class, 'getcoordinategmap'])->middleware(['auth'])->name('get-coordinate-gmap');
    Route::post('/ajax/validate-customer', [AjaxController::class, 'validatecustomer'])->middleware(['auth'])->name('validate-customer');
    Route::get('/ajax/blash-is-send', [AjaxController::class, 'blashissend'])->middleware(['auth'])->name('blash-is-send');
    Route::post('/ajax/change-store', [AjaxController::class, 'changestore'])->middleware(['auth'])->name('changestore');

    // Route::get('/update/system', [SystemController::class, 'updatestoreproduct'])->middleware(['auth'])->name('update-system');
    // ? Superadmin & Owner
    Route::get('/generate/deposit/{customer_id}', [ManualController::class, 'generatedeposit'])->middleware(['auth'])->name('manual.generate.deposit');
    Route::post('/jobs/resyncacc', [OrdersController::class, 'reSyncAcc'])->middleware(['auth'])->name('jobs.resync.acc');
});

Route::get('/socialchat-test', [SocialchatApiController::class, 'test'])->middleware(['auth'])->name('socialchat-test');
Route::get('/socialchat/get/conversation', [SocialchatApiController::class, 'getConversationId'])->middleware(['auth'])->name('socialchat-get-conversation-id');

Route::get('/accurate-auth', [AccurateApiController::class, 'accurateAuthorize'])->middleware(['auth'])->name('accurate-auth');
Route::get('/accurate-callback', [AccurateApiController::class, 'accurateCallback'])->middleware(['auth'])->name('accurate-callback');
Route::get('/accurate-sync', [AccurateApiController::class, 'accurateSyncStep1'])->middleware(['auth'])->name('accurate-sync-index');
Route::post('/accurate-sync-step-2', [AccurateApiController::class, 'accurateSyncProcess1'])->middleware(['auth'])->name('accurate-sync-process-1');
Route::post('/accurate-sync-step-3', [AccurateApiController::class, 'accurateSyncProcess2'])->middleware(['auth'])->name('accurate-sync-process-2');
Route::get('/accurate-sync-step-4/{store_id}', [AccurateApiController::class, 'accurateSyncProcess3'])->middleware(['auth'])->name('accurate-sync-process-3');
Route::post('/accurate-sync-step-5', [AccurateApiController::class, 'accurateSyncProcess4'])->middleware(['auth'])->name('accurate-sync-process-4');
// Route::post('/accurate-sync-step-5', [AccurateApiController::class, 'accurateSyncProcess4'])->middleware(['auth'])->name('accurate-sync-process-4');
Route::get('/accurate-sync-init', [AccurateApiController::class, 'accurateSyncInitDb'])->middleware(['auth'])->name('accurate-sync-store');
Route::any('/accurate-sync-stock', [AccurateApiController::class, 'accurateSyncStockDb'])
    // ->middleware(['auth'])
    // ->name('accurate-sync-stock')
;
// Route::post('/accurate-sync-stock', [AccurateApiController::class, 'accurateSyncStockDb'])
//     // ->middleware(['auth'])
//     // ->name('accurate-sync-stock')
// ;
Route::get('/accurate-test', [AccurateApiController::class, 'accurateTest'])->middleware(['auth'])->name('accurate-test');

Route::get('/info/payment/{store_slug}/tf', [InfosController::class, 'payment'])->name('info.payment');
// Route::get('/info/notif', [InfosController::class, 'notif'])->name('info.notif');

Route::get('/test', [TestController::class, 'index'])->middleware(['auth']);
Route::get('/set-last-order-customers', [TestController::class, 'setLastOrderCustomers'])->middleware(['auth']);
Route::get('/test-notif-reminder/template/{template}', [TestController::class, 'testNotifReminder'])->middleware(['auth']);
Route::get('/rdc', [CustomerController::class, 'removeDuplicateCustomers'])->middleware(['auth'])->name('remove-duplicate-customers');
// Route::get('/test-send-notif', [ApiController::class, 'testSendNotifQueue'])->middleware(['auth']);
// Route::get('/test-send-notif-prior', [ApiController::class, 'testSendNotifQueuePrior'])->middleware(['auth']);
// Route::get('/test/env', function () {
//     return env("APP_ENV");
// })->middleware(['auth']);

Route::get('/search/vhbrands', [SharpController::class, 'searchVhBrands'])
    ->name('search.vhbrands')
    ->middleware(['auth']);
Route::get('/search/vhmodels/{brand_id}', [
    SharpController::class,
    'searchVhModels',
])
    ->name('search.vhmodels')
    ->middleware(['auth']);
Route::get('/search/costcategory/{parent_cost_category_id}', [
    SharpController::class,
    'searchCostCategory',
])
    ->name('search.costcategory')
    ->middleware(['auth']);

Route::get('search/customer/{customer_id}/product/{product_id}/ppobkey', [CustomerController::class, 'getCustomerPpobKey'])
    ->middleware(['auth'])
    ->name('search.customer.ppob.key');

Route::get('iak/prepaid/check-balance', [IakController::class, 'checkBalance'])
    ->middleware(['auth'])
    ->name('iak.prepaid.check-balance');
Route::get('iak/prepaid/inquiry/pln/{ppob_key}/{customer_id?}/{product_id?}', [IakController::class, 'getPrepaidInquiryPln'])
    ->middleware(['auth'])
    ->name('iak.prepaid.inquiry.pln');
Route::get('iak/postpaid/inquiry/pdam/{ppob_key}/{product_code}/{ref_id}/{customer_id?}/{product_id?}', [IakController::class, 'getPostpaidInquiryPdam'])
    ->middleware(['auth'])
    ->name('iak.postpaid.inquiry.pdam');
Route::get('iak/postpaid/pricelist/{type?}', [IakController::class, 'getPostpaidPricelist'])
    ->middleware(['auth'])
    ->name('iak.postpaid.pricelist');
Route::get('iak/prepaid/pricelist/{type?}/{operator?}', [IakController::class, 'getPrepaidPricelist'])
    ->middleware(['auth'])
    ->name('iak.prepaid.pricelist');
Route::get('iak/prepaid/check-status/{ref_id}', [IakController::class, 'prepaidCheckStatus'])
    ->middleware(['auth'])
    ->name('iak.prepaid.check-status');
Route::post('orderproduct/{order_product_id}/mark-notif-as-sent', [OrdersController::class, 'markNotifAsSent'])
    ->middleware(['auth'])
    ->name('orderproduct.marknotifassent');
Route::get('iak/pricelist/{product_id}', [IakController::class, 'pricelistOfProduct'])
    ->middleware(['auth'])
    ->name('iak.pricelist');

Route::get('fb/{notification_log_id}/{feedback_key}', [NotificationController::class, 'storeFeedback'])
    // ->middleware(['auth'])
    ->name('notificationreminder.feedback.submit');
Route::get('us/{notification_log_id}', [NotificationController::class, 'unsubscribe'])
    // ->middleware(['auth'])
    ->name('notificationreminder.unsubscribe.submit');
Route::get('rv/{customer_id_store_id}', [NotificationController::class, 'reviewInvitation'])
    // ->middleware(['auth'])
    ->name('notification.review.invitation');

Route::get('acc/vendor/list', [AccurateApiController::class, 'getVendorListFromAccurate'])
    ->middleware(['auth'])
    ->name('acc.vendor.list');

require __DIR__ . '/auth.php';
