<?php

use App\Http\Controllers\CalendarController;
use App\Http\Controllers\JobsController;
use App\Http\Controllers\AccountController;
use App\Http\Controllers\ReportController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return redirect()->route('calendar');
})->name('home');

Route::get('dashboard', function () {
    return Inertia::render('Dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('calendar', [CalendarController::class, 'index'])->name('calendar');
    Route::get('jobs', [JobsController::class, 'index'])->name('jobs');
    Route::get('account', [AccountController::class, 'index'])->name('account');
    Route::get('report', [ReportController::class, 'index'])->name('report');
});

require __DIR__ . '/settings.php';
require __DIR__ . '/auth.php';
