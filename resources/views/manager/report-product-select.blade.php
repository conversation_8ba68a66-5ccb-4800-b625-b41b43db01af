<x-app-layout>
  <x-slot name="store_slug">
    {{ $store_slug }}
  </x-slot>

  <x-slot name="header">
    <div class="absolute inset-0 flex px-4 items-center bg-yellow-500">
      <h2 class="font-bold text-base flex items-center">
        <a href="{{ route('report', ['store_slug' => $store_slug]) }}" class="flex -ml-3 items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"
            stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
          </svg>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 -ml-0.5 mr-1 -mt-0.5" viewBox="0 0 20 20"
            fill="currentColor">
            <path fill-rule="evenodd"
              d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z"
              clip-rule="evenodd" />
          </svg>
          Produk
        </a>
        <select class="_js-input-store text-base inline font-bold ml-1 bg-transparent border-none p-0">
          @foreach ($stores as $store)
          <option
            value="{{ route('report.product.select', ['store_slug' => $store->slug, 'month' => $_GET['month'] ?? '']) }}"
            {{ $store->slug == $store_slug ?
            "selected" : null }}>
            {{ $store->name }}</option>
          @endforeach
        </select>
      </h2>
    </div>
  </x-slot>

  <div class="pt-16 pb-52">
    @foreach ($products as $product)
    <label for="{{ $product->code }}" class="flex items-center px-4 text-sm h-11 border-solid border-gray-200 border-b">
      <input type="checkbox" class="mr-2 _js-checkbox" id="{{ $product->code }}" name="{{ $product->code }}"
        value="{{ $product->id }}">
      <strong class="mr-2">{{ $product->code }}</strong> <span
        class="overflow-hidden overflow-ellipsis whitespace-nowrap">{{ $product->name }}</span>
    </label>
    @endforeach
  </div>

  <div class="fixed left-0 right-0 bottom-16 ">
    <div
      class="max-w-xl mx-auto pt-3 pb-6 px-4 bg-white z-20 flex flex-col justify-center items-center border-solid border-t-2 border-gray-300 shadow ">
      <h5 class="text-sm text-gray-400 font-bold">Pilih MAX 6 Produk</h5>
      <x-link href="#" class="w-full bg-green-600 mt-3 opacity-50 pointer-events-none _js-btn-report">
        Lihat Performa Produk 📦
      </x-link>
    </div>
  </div>

  <x-slot name="js">
    <script>
      $( document ).ready(function() {
        const ids = [];

        $('._js-checkbox').change(function() {
          if (ids.length == 6) {
            $(this).prop('checked', false);
            alert('Pilih MAX 6 Produk');
            return;
          }
          if(this.checked) {
              // console.log('check');
              // console.log(this.value);
              ids.push(this.value);
            } else {
            // console.log('uncheck');
            // console.log(this.value);
            ids.splice (ids.indexOf(this.value), 1);
          }
          if (ids.length == 0) {
            $('._js-btn-report').addClass('opacity-50 pointer-events-none');
          } else {
            $('._js-btn-report').removeClass('opacity-50 pointer-events-none');
          }
          const url = window.location.href + '/' + ids.join('-');
          $('._js-btn-report').attr('href', url);
        });
      });
    </script>
  </x-slot>

</x-app-layout>