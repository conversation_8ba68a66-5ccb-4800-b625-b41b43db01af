<x-app-layout>
    <x-slot name="store_slug">
        {{ $store_slug }}
    </x-slot>

    <x-slot name="header">
        <a class="flex items-center -ml-3" href="{{ route('jobs', ['store_slug' => $store_slug]) }}"><svg
                class="w-9 h-9" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
            <h2 class="font-bold text-lg">
                Add Purchasing
            </h2>
        </a>
        {{-- <button type="button" class="focus:outline-none w-9 ml-auto text-center _js-btn-submit"><svg
                xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path
                    d="M7.707 10.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V6h5a2 2 0 012 2v7a2 2 0 01-2 2H4a2 2 0 01-2-2V8a2 2 0 012-2h5v5.586l-1.293-1.293zM9 4a1 1 0 012 0v2H9V4z" />
            </svg>
            <span class="-mt-1 text-xs block">Save</span></button> --}}
    </x-slot>

    <div class="pt-20 pb-20 px-5">
        <form action="{{ route('purchase-post', ['store_id' => $store->id]) }}" class="_js-form" method="POST">
            @csrf
            <purchase-add :stores="{{ $stores }}" :products="{{ $products }}" :current_store_id="{{ $store->id }}"
                :vendors="{{ $vendors }}" url_base_temp={{ route('purchase-add', ['store_slug'=> 'xxx'])}} />
        </form>
    </div>
</x-app-layout>