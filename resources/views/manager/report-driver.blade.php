<x-app-layout>
  <x-slot name="store_slug">
    {{ $store_slug }}
  </x-slot>

  <x-slot name="header">
    <div class="absolute inset-0 flex px-4 items-center bg-yellow-500">
      <h2 class="font-bold text-base flex items-center">
        <a href="{{ route('report', ['store_slug' => $store_slug]) }}" class="flex -ml-3 items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"
            stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
          </svg>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 -ml-0.5 mr-1 -mt-0.5" viewBox="0 0 20 20"
            fill="currentColor">
            <path fill-rule="evenodd"
              d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z"
              clip-rule="evenodd" />
          </svg>
          Delman
        </a>
        <select class="_js-input-store text-base inline font-bold ml-1 bg-transparent border-none p-0">
          @foreach ($stores as $store)
          <option value="{{ route('report.driver', ['store_slug' => $store->slug, 'month' => $_GET['month'] ?? '']) }}"
            {{ $store->slug == $store_slug ?
            "selected" : null }}>
            {{ $store->name }}</option>
          @endforeach
        </select>
      </h2>
    </div>
  </x-slot>

  <x-slot name="subheader">
    <div class="mt-2 flex justify-center items-center h-10 text-yellow-500">
      <a class="w-6 h-6" href="{{ route('report.driver', ['store_slug' => $store_slug, 'month' => $month_prev]) }}"><svg
          xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd"
            d="M10 18a8 8 0 100-16 8 8 0 000 16zm.707-10.293a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L9.414 11H13a1 1 0 100-2H9.414l1.293-1.293z"
            clip-rule="evenodd" />
        </svg></a>
      <input type="month" class="mx-3 font-bold text-lg w-56 border-none p-0 text-center focus:ring-0 _js-input-month"
        max="{{ date('Y-m') }}" value="{{ $month_now }}">
      @if ($month_next)
      <a class="w-6 h-6" href="{{ route('report.driver', ['store_slug' => $store_slug, 'month' => $month_next]) }}"><svg
          xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd"
            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 1.414L10.586 9H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z"
            clip-rule="evenodd" />
        </svg></a>
      @endif
    </div>
    <div class="flex items-center font-bold text-xs text-center">
      <span class="capitalize text-gray-500 py-2 w-16">Tgl.</span>
      <span class="text-blue-700 py-2 w-16">Total</span>
      <span class="text-red-700 py-2 w-16">>1jam</span>
      <div
        class="flex items-center border-l-2 scrollbar-hide border-gray-200 bg-yellow-50 flex-1 overflow-x-auto _js-scroll">
        @foreach ($drivers as $driver)
        @if ($driver->total > 0)
        <span class="overflow-hidden overflow-ellipsis whitespace-nowrap text-blue-500 py-2 px-1 w-20 flex-shrink-0">{{
          $driver->name }}</span>
        @endif
        @endforeach
      </div>
    </div>
    <div class="flex items-center font-bold text-xs text-center">
      <span class="capitalize text-gray-500 py-2 w-16">TOTAL</span>
      <span class="text-blue-700 py-2 w-16">{{ $sum_total_all }}</span>
      <span class="text-red-700 py-2 w-16">{{ $sum_total_all_overtime }}</span>
      <div
        class="flex items-center border-l-2 scrollbar-hide border-gray-200 bg-yellow-50 flex-1 overflow-x-auto _js-scroll">
        @foreach ($drivers as $driver)
        @if ($driver->total > 0)
        <span class="text-blue-500 py-2 w-20 px-1 flex-shrink-0">{{ $driver->total }}</span>
        @endif
        @endforeach
      </div>
    </div>
  </x-slot>

  <div class="pt-44 pb-20">
    @foreach ($data as $i => $item)
    <a href="{{ route('job-list', ['store_slug' => $store_slug, 'date' => $item['datetime'], 'status' => 'total', 'search' => '>1jam']) }}"
      target="_blank"
      class="flex items-center font-bold text-sm text-center border-solid border-gray-200 border-b {{ $month_now == date('Y-m') && $item['date'] == date('d') ? 'bg-yellow-100' : null }}">
      <div class="text-gray-500 h-11 w-16 flex flex-col justify-center items-center leading-none">{{
        $item['date'] }}<span class="mt-0 text-xs font-light">{{ $item['day'] }}</span></div>
      <span class="flex justify-center items-center text-blue-700 h-11 py-2 w-16">{{ $item['day_total_delivery']
        }}</span>
      <span class="flex justify-center items-center text-red-700 h-11 py-2 w-16">{{ $item['day_total_delivery_overtime']
        }}</span>
      <div class="flex items-center border-l-2 scrollbar-hide border-gray-200 flex-1 overflow-x-auto _js-scroll">
        @foreach ($drivers as $driver)
        @if ($driver->total > 0)
        <span class="flex justify-center items-center text-blue-500 h-11 py-2 px-1 w-20 flex-shrink-0">{{
          $item['driver_total_delivery'][$driver->id]['total']
          }}</span>
        @endif
        @endforeach
      </div>
    </a>
    @endforeach
  </div>



  <x-slot name="js">
    <script>
      $( document ).ready(function() {
            // Change Date
            // --------------------------------------------------
            $('._js-input-month').change(function() {
                const url = "{{ url()->current() }}";
                const month = this.value;
                console.log(url+'?month='+month);
                window.location.replace(url+'?month='+month);
            });
            $('._js-input-store').change(function() {
                const url = this.value;
                window.location.replace(url);
            });

            // Bind Multiple Scroll
            // --------------------------------------------------
            // $('._js-scroll').scrollsync()
            $('._js-scroll').scroll(function(e){
              $('._js-scroll').scrollLeft(e.target.scrollLeft);
            });
            // var scrollers = document.getElementsByClassName('_js-scroll');
            // var scrollerDivs = Array.prototype.filter.call(scrollers, function(testElement) {
            //   return testElement.nodeName === 'DIV';
            // });
            // function scrollAll(scrollLeft) {
            //   scrollerDivs.forEach(function(element, index, array) {
            //     element.scrollLeft = scrollLeft;
            //   });
            // }
            // scrollerDivs.forEach(function(element, index, array) {
            //   element.addEventListener('scroll', function(e) {
            //     scrollAll(e.target.scrollLeft);
            //   });
            // });
        });
    </script>
  </x-slot>
</x-app-layout>