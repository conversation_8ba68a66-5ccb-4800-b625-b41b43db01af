<x-app-layout>
    <x-slot name="store_slug">
        {{ $store_slug }}
    </x-slot>

    <x-slot name="header">
        <a class="flex items-center -ml-3" href="{{ URL::previous() }}"><svg class="w-9 h-9"
                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
            <h2 class="text-lg font-bold">
                Data SDM
            </h2>
        </a>
        <a href="/cs/form/employee/{{$sdm->id}}" target="_blank" class="ml-auto font-bold">✏️ EDIT ↗︎</a>
    </x-slot>

    <div class="px-3 pt-16 pb-20 prose">
        <table class="w-full table-auto">
            <tbody>
                <tr>
                    <th class="text-left">Nama</th>
                    <td class="text-left">{{$sdm->full_name}}</td>
                </tr>
                <tr>
                    <th class="text-left">Akun</th>
                    <td class="flex flex-col text-left">{{$sdm->user->name}}
                        <span class="text-xs text-gray-500">{{$sdm->user->email}}</span>
                    </td>
                </tr>
                @if($sdm->dob)
                <tr>
                    <th class="text-left whitespace-nowrap">Tgl. Lahir</th>
                    <td class="text-left">{{$sdm->dob}}</td>
                </tr>
                @endif
                @if($sdm->address)
                <tr>
                    <th class="text-left">Alamat</th>
                    <td class="text-left">{{$sdm->address}}</td>
                </tr>
                @endif
                @if($sdm->profilephoto_url)
                <tr>
                    <th class="text-left whitespace-nowrap">Foto Selfie</th>
                    <td class="text-left"><img data-zoomable src="{{$sdm->profilephoto_url}}" /></td>
                </tr>
                @endif
                @if($sdm->ktp_number)
                <tr>
                    <th class="text-left whitespace-nowrap">KTP Number</th>
                    <td class="text-left">{{$sdm->ktp_number}}</td>
                </tr>
                @endif
                @if($sdm->ktpphoto_url)
                <tr>
                    <th class="text-left whitespace-nowrap">Foto KTP</th>
                    <td class="text-left"><img data-zoomable src="{{$sdm->ktpphoto_url}}" /></td>
                </tr>
                @endif
                @if($sdm->kk_number)
                <tr>
                    <th class="text-left whitespace-nowrap">KK Number</th>
                    <td class="text-left">{{$sdm->kk_number}}</td>
                </tr>
                @endif
                @if($sdm->kkphoto_url)
                <tr>
                    <th class="text-left whitespace-nowrap">Foto KK</th>
                    <td class="text-left"><img data-zoomable src="{{$sdm->kkphoto_url}}" /></td>
                </tr>
                @endif
                @if($sdm->sim_number)
                <tr>
                    <th class="text-left whitespace-nowrap">SIM Number</th>
                    <td class="flex flex-col text-left">{{$sdm->sim_number}}
                        @if($sdm->sim_valid_until)
                        <span class="text-xs text-gray-500">Valid until: {{$sdm->sim_valid_until}}</span>
                        @endif
                    </td>
                </tr>
                @endif
                @if($sdm->simphoto_url)
                <tr>
                    <th class="text-left whitespace-nowrap">Foto SIM</th>
                    <td class="text-left"><img data-zoomable src="{{$sdm->simphoto_url}}" /></td>
                </tr>
                @endif
                @if($sdm->start_work_at)
                <tr>
                    <th class="text-left whitespace-nowrap">Mulai Kerja</th>
                    <td class="text-left">{{$sdm->start_work_at}}</td>
                </tr>
                @endif
                @if($sdm->quit_work_at)
                <tr>
                    <th class="text-left whitespace-nowrap">Berhenti Kerja</th>
                    <td class="text-left">{{$sdm->quit_work_at}}</td>
                </tr>
                @endif
                @if($sdm->quit_note)
                <tr>
                    <th class="text-left whitespace-nowrap">Alasan Berhenti</th>
                    <td class="text-left">{{$sdm->quit_note}}</td>
                </tr>
                @endif
                @if($sdm->note)
                <tr>
                    <th class="text-left whitespace-nowrap">Catatan Lain</th>
                    <td class="text-left">{{$sdm->note}}</td>
                </tr>
                @endif
                @if($sdm->armada)
                <tr>
                    <th class="text-left whitespace-nowrap">Armada</th>
                    <td class="flex flex-col text-left">{{$sdm->armada->licence_number}}
                        <span class="text-xs text-gray-500">
                            {{$sdm->armada->brand ? $sdm->armada->brand->name : ''}}
                            {{$sdm->armada->model ? $sdm->armada->model->name : ''}}
                            {{$sdm->armada->year ? $sdm->armada->year : ''}}
                        </span>
                    </td>
                </tr>
                @endif
            </tbody>
        </table>
    </div>
    <x-slot name="js">
        <script>
            $(document).ready(function() {
                mediumZoom('[data-zoomable]');
            });
        </script>
    </x-slot>
</x-app-layout>