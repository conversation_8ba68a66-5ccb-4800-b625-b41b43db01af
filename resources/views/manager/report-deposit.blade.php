<x-app-layout>
    <x-slot name="store_slug">
        {{ $store_slug }}
    </x-slot>

    <x-slot name="header">
        <div class="absolute inset-0 flex px-4 items-center bg-yellow-500">
            <h2 class="font-bold text-base flex items-center">
                <a href="{{ route('report', ['store_slug' => $store_slug]) }}" class="flex -ml-3 items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
                    </svg>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 -ml-0.5 mr-1 -mt-0.5" viewBox="0 0 20 20"
                        fill="currentColor">
                        <path fill-rule="evenodd"
                            d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd" />
                    </svg>
                    Deposit
                </a>
                <select class="_js-input-store text-base inline font-bold ml-1 bg-transparent border-none p-0">
                    @foreach ($stores as $store)
                    <option
                        value="{{ route('report.deposit', ['store_slug' => $store->slug, 'date' => $_GET['date'] ?? '']) }}"
                        {{ $store->slug == $store_slug ?
                        "selected" : null }}>
                        {{ $store->name }}</option>
                    @endforeach
                </select>
            </h2>
        </div>
    </x-slot>

    <x-slot name="subheader">
        <div class="mt-2 flex justify-center items-center h-10 text-yellow-500">
            <a class="w-6 h-6"
                href="{{ route('report.deposit', ['store_slug' => $store_slug, 'date' => $date_prev]) }}"><svg
                    xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm.707-10.293a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L9.414 11H13a1 1 0 100-2H9.414l1.293-1.293z"
                        clip-rule="evenodd" />
                </svg></a>
            <input type="date"
                class="mx-3 font-bold text-lg w-56 border-none p-0 text-center focus:ring-0 _js-input-date"
                max="{{ date('Y-m-d') }}" value="{{ $date_now }}">
            @if ($date_next)
            <a class="w-6 h-6"
                href="{{ route('report.deposit', ['store_slug' => $store_slug, 'date' => $date_next]) }}"><svg
                    xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 1.414L10.586 9H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z"
                        clip-rule="evenodd" />
                </svg></a>
            @endif
        </div>
        <p class="text-gray-400 w-full text-center pb-6 text-sm">{{ $day }}</p>
        <div
            class="grid grid-cols-2 gap-0 pt-2 pb-2 text-xs font-bold border-solid border-gray-200 border-b px-4 bg-yellow-50">
            <div class="text-black flex justify-start items-start leading-none col-span-1">Job & Pelanggan</div>
            <div class="text-yellow-500 flex justify-end items-start leading-none col-span-1">Deposit</div>
        </div>
    </x-slot>

    <div class="pt-44 pb-20">
        @if ($deposits->count() > 0)
        <div class="sticky left-0 right-0 top-44 z-10">
            <div class="max-w-xl mx-auto">
                <div
                    class="grid grid-cols-2 gap-0 pt-3 pb-2 text-xs font-bold border-solid border-gray-200 border-b px-4 bg-gray-100">
                    <div class="text-gray-400 flex justify-start items-start leading-none col-span-1">Deposit Transaksi
                    </div>
                    <div class="text-yellow-500 flex justify-end items-start leading-none col-span-1">
                        TOTAL 💰 Rp{{number_format($deposits->sum('balance'), 0,'', '.') }}</div>
                </div>
            </div>
        </div>
        @endif
        @foreach ($deposits as $deposit)
        <a href="{{ URL::to('/') }}/cs/show/customer/{{ $deposit->customer_id }}" target="_blank"
            class="grid grid-cols-2 gap-0 py-2 text-sm font-bold border-solid border-gray-200 border-b px-4">
            <div class="text-black flex flex-col justify-center items-start col-span-1">
                <span class="text-sm">{{ $deposit->customer->name}}</span>
                <span class="text-xs text-gray-400 line-clamp-1">{{ $deposit->order->address->address }}</span>
                <span class="text-red-900 text-xs">{{ $deposit->order->code }}</span>
            </div>
            <div
                class="{{ $deposit->balance > 0 ? 'text-green-500' : '' }} {{ $deposit->balance < 0 ? 'text-red-500' : '' }} flex flex-col justify-start items-end text-xs col-span-1">
                {{
                $deposit->balance < 0 ? '-' : '' }}💵 Rp{{ number_format(abs($deposit->balance), 0,
                    '', '.') }}
                    <span class="opacity-40" style="font-size: 10px;">{{
                        $deposit->amount < 0 ? '-' : '' }}Rp{{ number_format(abs($deposit->amount), 0,
                            '', '.') }}</span></div>
        </a>
        @endforeach
        @if ($customers_plus->count() > 0)
        @php
        $total_deposit_plus = $customers_plus->sum('deposit_amount');
        @endphp
        <div class="sticky left-0 right-0 top-44 z-10">
            <div class="max-w-xl mx-auto">
                <div
                    class="grid grid-cols-2 gap-0 pt-3 pb-2 text-xs font-bold border-solid border-gray-200 border-b px-4 bg-gray-100">
                    <div class="text-gray-400 flex justify-start items-start leading-none col-span-1">Deposit
                        Pelanggan
                    </div>
                    <div class="text-yellow-500 flex justify-end items-start leading-none col-span-1">
                        TOTAL 💰 Rp{{number_format($total_deposit_plus, 0,'', '.') }}</div>
                </div>
            </div>
        </div>
        @endif
        @foreach ($customers_plus as $customer)
        <a href="{{ URL::to('/') }}/cs/show/customer/{{ $customer->id }}" target="_blank"
            class="grid grid-cols-2 gap-0 py-2 text-sm font-bold border-solid border-gray-200 border-b px-4">
            <div class="text-black flex flex-col justify-center items-start col-span-1">
                <span class="text-sm">{{ $customer->name}}</span>
            </div>
            <div
                class="{{ $customer->deposit_amount > 0 ? 'text-green-500' : '' }} {{ $customer->deposit_amount < 0 ? 'text-red-500' : '' }} flex flex-col justify-start items-end text-xs col-span-1">
                {{
                $customer->deposit_amount < 0 ? '-' : '' }}💵 Rp{{ number_format(abs($customer->deposit_amount),
                    0,
                    '', '.') }}</div>
        </a>
        @endforeach

        @if ($customers_minus->count() > 0)
        @php
        $total_deposit_minus = $customers_minus->sum('deposit_amount');
        @endphp
        <div class="sticky left-0 right-0 top-44 z-10">
            <div class="max-w-xl mx-auto">
                <div
                    class="grid grid-cols-2 gap-0 pt-3 pb-2 text-xs font-bold border-solid border-gray-200 border-b px-4 bg-gray-100">
                    <div class="text-gray-400 flex justify-start items-start leading-none col-span-1">Kekurangan
                        Pelanggan
                    </div>
                    <div class="text-yellow-500 flex justify-end items-start leading-none col-span-1">
                        TOTAL 💰 Rp{{number_format($total_deposit_minus, 0,'', '.') }}</div>
                </div>
            </div>
        </div>
        @endif
        @foreach ($customers_minus as $customer)
        <a href="{{ URL::to('/') }}/cs/show/customer/{{ $customer->id }}" target="_blank"
            class="grid grid-cols-2 gap-0 py-2 text-sm font-bold border-solid border-gray-200 border-b px-4">
            <div class="text-black flex flex-col justify-center items-start col-span-1">
                <span class="text-sm">{{ $customer->name}}</span>
            </div>
            <div
                class="{{ $customer->deposit_amount > 0 ? 'text-green-500' : '' }} {{ $customer->deposit_amount < 0 ? 'text-red-500' : '' }} flex flex-col justify-start items-end text-xs col-span-1">
                {{
                $customer->deposit_amount < 0 ? '-' : '' }}💵 Rp{{ number_format(abs($customer->deposit_amount),
                    0,
                    '', '.') }}</div>
        </a>
        @endforeach
        <div class="px-8 mt-5">
            <x-button type="button"
                class="w-full flex justify-center items-center bg-yellow-500 relative _js-btn-refresh">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2.5" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round"
                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg> Refresh Data
            </x-button>
        </div>
    </div>

    <div class="fixed z-10 inset-0 _js-loading transition-opacity pointer-events-none opacity-0">
        <div class="bg-white w-full h-screen mx-auto max-w-xl bg-opacity-80 flex justify-center items-center">
            <svg class="animate-spin h-12 w-12 text-yellow-500" xmlns="http://www.w3.org/2000/svg" fill="none"
                viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                </path>
            </svg>
        </div>
    </div>

    <x-slot name="js">
        <script>
            $( document ).ready(function() {
                $('._js-input-date').change(function() {
                    const url = "{{ url()->current() }}";
                    const date = this.value;
                    // console.log(url+'?date='+date);
                    window.location.replace(url+'?date='+date);
                });
                $('._js-input-store').change(function() {
                    const url = this.value;
                    window.location.replace(url);
                });

                $('._js-btn-refresh').click(function() {
                    $('._js-loading').removeClass('opacity-0 pointer-events-none');
                    axios.post("{{ route('generate.deposit', ['store_id' => $store_id, 'date' => $date_now]) }}")
                        .then(function(response) {
                            console.log("🚀 ~ response:", response)
                            window.location.reload();
                        })
                        .catch(function(error) {
                            console.log("🚀 ~ error:", error)
                            // console.log(error);
                            mdtoast('REFRESH DATA GAGAL', { duration: 1500, type: mdtoast.ERROR });
                        });
                });
            });
        </script>
    </x-slot>
</x-app-layout>