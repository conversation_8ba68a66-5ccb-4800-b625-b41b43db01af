<x-app-layout>
    <x-slot name="store_slug">
        {{ $store_slug }}
    </x-slot>

    <x-slot name="header">
        <a class="flex items-center -ml-3 whitespace-nowrap"
            href="{{ route('jobs', ['store_slug' => $store_slug]) }}"><svg class="w-9 h-9"
                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
            <h2 class="text-lg font-bold">
                Add Pengeluaran Toko
            </h2>
        </a>
        <span class="ml-auto text-sm line-clamp-2 pl-2.5">{{$store->name}}</span>
        {{-- <button type="button" class="ml-auto text-center focus:outline-none w-9 _js-btn-submit"><svg
                xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path
                    d="M7.707 10.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V6h5a2 2 0 012 2v7a2 2 0 01-2 2H4a2 2 0 01-2-2V8a2 2 0 012-2h5v5.586l-1.293-1.293zM9 4a1 1 0 012 0v2H9V4z" />
            </svg>
            <span class="block -mt-1 text-xs">Save</span></button> --}}
    </x-slot>

    <div class="px-5 pt-20 pb-20">
        @if ((int)$totalkonsumsitoko > 0)
        <div class="bg-green-400 rounded-lg py-2 px-3 mb-4 flex items-center">
            TOTAL Konsumsi Toko: <strong class="ml-auto">Rp{{ number_format($totalkonsumsitoko, 0, '', '.') }}</strong>
        </div>
        @endif
        <form action="{{ route('operatingcost-post', ['store_slug' => $store_slug]) }}" enctype="multipart/form-data"
            class="_js-form" method="POST">
            @csrf
            <operatingcost-add :role_id="{{ auth()->user()->role_id }}"
                :current_user="{{auth()->user()->with(['employee'])->first()}}" :armadas="{{$armadas}}"
                :stores="{{ $stores }}" :current_store_id="{{ $store->id }}" :cost_categories="{{$cost_categories}}"
                :sdm="{{$sdm}}" />
        </form>
    </div>
</x-app-layout>