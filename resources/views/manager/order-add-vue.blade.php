<x-layout-addjob>
    <x-slot name="store_slug">
        {{ $store_slug }}
    </x-slot>

    <x-slot name="header">
        <a class="flex items-center -ml-3" href="{{ route('jobs', ['store_slug' => $store_slug]) }}"><svg
                class="w-9 h-9" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
            <h2 class="text-lg font-bold">
                Add Job
            </h2>
        </a>
        {{-- <select class="inline p-0 ml-auto text-sm font-bold bg-transparent border-none _js-select-store">
            @foreach ($stores as $item)
            <option value="{{ route('order-add-vue', ['store_slug' => $item->slug]) }}" {{ $item->
                slug
                == $store_slug ?
                "selected" : null }}>
                {{ $item->name }}</option>
            @endforeach
        </select> --}}
        {{-- <button type="button" class="ml-auto text-center focus:outline-none w-9 _js-btn-submit"><svg
                xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path
                    d="M7.707 10.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V6h5a2 2 0 012 2v7a2 2 0 01-2 2H4a2 2 0 01-2-2V8a2 2 0 012-2h5v5.586l-1.293-1.293zM9 4a1 1 0 012 0v2H9V4z" />
            </svg>
            <span class="block -mt-1 text-xs">Save</span></button> --}}
    </x-slot>

    <div
        class="fixed z-0 top-0 left-0 right-0 flex items-center justify-center h-screen text-5xl font-bold text-gray-300 _js-loading">
        Loading...
    </div>
    <form action="{{ route('order-post', ['store_slug' => $store_slug]) }}" class="_js-form" method="POST">
        @csrf
        <order-add :current_role_id="{{ auth()->user()->role_id }}"
            :current_store_is_notif_wa="{{ $store->is_notif_wa }}" current_store_open_hour="{{ $store->open_hour }}"
            current_store_close_hour="{{ $store->close_hour }}" current_store_id="{{ $store->id }}" url_jobs_temp={{
            route('order-add-vue', ['store_slug'=> 'xxx'])}}
            :stores="{{ $stores->toJson() }}"
            :drivers="{{ $drivers->toJson() }}"
            />
    </form>
</x-layout-addjob>