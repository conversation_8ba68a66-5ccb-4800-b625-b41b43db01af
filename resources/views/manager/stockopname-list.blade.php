<x-app-layout>
  <x-slot name="store_slug">
    {{ $store_slug }}
  </x-slot>

  <x-slot name="header">
    <div class="absolute inset-0 flex px-4 items-center bg-yellow-500">
      <h2 class="text-lg font-bold flex items-center">
        <a href="{{ route('report', ['store_slug' => $store_slug]) }}" class="flex -ml-3 items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"
            stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
          </svg>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 -ml-0.5 mr-1 -mt-0.5" viewBox="0 0 20 20"
            fill="currentColor">
            <path fill-rule="evenodd"
              d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z"
              clip-rule="evenodd" />
          </svg>
          LTT
        </a>
        <select class="inline p-0 ml-1.5 text-lg font-bold bg-transparent border-none _js-input-store">
          @foreach ($stores as $item)
          <option
            value="{{ route('report.stockopname.detail', ['store_slug' => $item->slug, 'date' => $_GET['date'] ?? '']) }}"
            {{ $item->
            slug
            == $store_slug ?
            "selected" : null }}>
            {{ $item->name }}</option>
          @endforeach
        </select>
      </h2>
    </div>
  </x-slot>

  <x-slot name="subheader">
    <div class="flex items-center justify-center h-6 mt-2 text-red-500">
      <a class="w-6 h-6"
        href="{{ route('report.stockopname.detail', ['store_slug' => $store_slug, 'date' => $date_prev]) }}"><svg
          xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd"
            d="M10 18a8 8 0 100-16 8 8 0 000 16zm.707-10.293a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L9.414 11H13a1 1 0 100-2H9.414l1.293-1.293z"
            clip-rule="evenodd" />
        </svg></a>
      <input type="date" class="w-56 p-0 mx-3 text-lg font-bold text-center border-none focus:ring-0 _js-input-date"
        max="{{ date('Y-m-d') }}" value="{{ $date_now }}">
      @if ($date_next)
      <a class="w-6 h-6"
        href="{{ route('report.stockopname.detail', ['store_slug' => $store_slug, 'date' => $date_next]) }}"><svg
          xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd"
            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 1.414L10.586 9H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z"
            clip-rule="evenodd" />
        </svg></a>
      @endif
    </div>
    <p class="w-full pb-1.5 text-sm text-center text-gray-400">{{ $day }}</p>
  </x-slot>

  <div
    class="fixed top-0 left-0 right-0 z-auto flex pt-40 justify-center h-screen text-sm font-bold text-blue-500 _js-loading">
    <svg class="animate-spin h-4 w-4 mr-1.5 inline-block" xmlns="http://www.w3.org/2000/svg" fill="none"
      viewBox="0 0 24 24">
      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
      <path class="opacity-75" fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
      </path>
    </svg>
    Loading...
  </div>
  <stockopname currentuserraw={{ json_encode([ 'id'=> auth()->user()->id,
    'role_id' => auth()->user()->role_id,
    ]) }} datenow="{{ $date_now }}" currentstoreraw='@json($store)'
    productsraw='@json($products)'
    stockopnameraw='@json($stockopname)'
    splitcashordersraw='@json($splitcash_orders)'
    base_url="{{ URL::to('/') }}"
    store_slug="{{ $store_slug }}"
    >
  </stockopname>

  <x-slot name="js">
    <script>
      $( document ).ready(function() {
        $('._js-input-store').change(function() {
            const url = this.value;
            window.location.replace(url);
        });
        $('._js-input-date').change(function() {
            const url = "{{ url()->current() }}";
            const date = this.value;
            // console.log(url+'?date='+date);
            window.location.replace(url+'?date='+date);
        });
      });
    </script>
  </x-slot>
</x-app-layout>