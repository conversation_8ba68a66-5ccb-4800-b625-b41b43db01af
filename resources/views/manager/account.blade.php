<x-app-layout>
    <x-slot name="store_slug">
        {{ $store_slug }}
    </x-slot>

    <x-slot name="header">
        <h2 class="text-lg font-bold">
            {{ __('Account') }}
        </h2>
    </x-slot>

    <div class="px-5 pt-20 pb-20">
        <div class="grid grid-cols-1 gap-6">
            <h2 class="text-2xl font-bold text-gray-700">{{ Auth::user()->name }}</h2>
            {{-- only for Super Admin, Owner and CS --}}
            @if (Auth::user()->role_id <= 3) <label class="block">
                <span class="font-bold text-gray-700">Toko</span>
                <select
                    class="block w-full mt-1 border-gray-300 rounded-md shadow-sm _js-input-store focus:border-blue-300 focus:ring focus:ring-red-200 focus:ring-opacity-50">
                    @foreach ($stores as $store)
                    <option value="{{ route('account', ['store_slug' => $store->slug]) }}" {{ $store->slug ==
                        $store_slug ? "selected" : null }}>
                        {{ $store->name }}</option>
                    @endforeach
                </select>
                </label>
                @endif
        </div>
        @if (Auth::user()->role_id == 3)
        <hr class="my-10">
        {{-- <x-link class="relative w-full bg-yellow-500" :href="route('report', ['store_slug' => $store_slug])">
            Performa
            <svg xmlns="http://www.w3.org/2000/svg" class="absolute top-0 bottom-0 w-6 h-full ml-auto right-2"
                fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round"
                    d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
            </svg>
        </x-link> --}}
        @endif
        <hr class="my-10">
        @if (Auth::user()->role_id <= 2 || Auth::user()->email === '<EMAIL>' || Auth::user()->email ===
            '<EMAIL>') <button type="button"
                class="px-4 py-3 inline-block text-center rounded-md font-bold text-base shadow-xl text-white uppercase tracking-widest hover:opacity-90 active:opacity-90 focus:outline-none disabled:opacity-25 transition ease-in-out duration-150 relative w-full mb-5 bg-blue-500 _js-btn-sync-stock">
                Update Stock
            </button>
            @endif
            @if (Auth::user()->role_id <= 1) <x-link class="relative w-full mb-5 bg-blue-400" target="_blank"
                href="/accurate-auth">
                1. Get Accurate API Access
                </x-link>
                <x-link class="relative w-full mb-5 bg-blue-500" target="_blank" href="/accurate-sync-init">
                    2. Sync Accurate Initial DB
                </x-link>
                <x-link class="relative w-full mb-5 bg-blue-600" target="_blank" href="/accurate-sync">
                    3. Sync Accurate Customer
                </x-link>
                @endif
                @if (Auth::user()->role_id <= 3) <x-link class="relative w-full bg-green-600"
                    href="/cs/list/customer?filter_stores%5B0%5D={{ $store_id }}&page=1&filter_last_order=%3E3">
                    List Pelanggan Tidak Aktif
                    </x-link>
                    <hr class="my-10">
                    @endif
                    @if (Auth::user()->role_id <= 4 && $sdms) <div class="flex flex-col gap-2">
                        <h2 class="text-lg font-bold text-gray-700">SDM 👉 Toko</h2>
                        @foreach ($sdms as $sdm)
                        <form method="POST" action="{{ route('change-sdm-store') }}"
                            class="flex items-center _js-form-change-sdm-store">
                            @csrf
                            <input type="hidden" name="user_id" value="{{ $sdm->id }}" />
                            @if($sdm->employee->count() > 0)
                            <a href="{{Auth::user()->role_id <= 3 ? route('sdm', ['store_slug' => $store_slug, 'employee_id' => $sdm->employee[0]->id]) : '#'}}"
                                class="flex flex-col mr-1 text-sm font-semibold text-red-700">
                                <span class="underline">{{$sdm->name }} ↗︎</span>
                                <span class="text-xs text-gray-400">{{$sdm->role->title }}</span>
                            </a>
                            @else
                            <a href="{{Auth::user()->role_id <= 3 ? url('cs/form/employee?user_id='.$sdm->id) : '#'}}"
                                class="flex flex-col mr-1 text-sm font-semibold">
                                <p class="flex flex-col">{{$sdm->name }} <span class="text-yellow-500 underline">➕
                                        Data
                                        SDM</span>
                                </p>
                                <span class="text-xs text-gray-400">{{$sdm->role->title }}</span>
                            </a>
                            @endif
                            <span class="ml-auto mr-2 text-sm">👉</span>
                            <select data-current_store_id="{{ $sdm->store_id }}" data-sdm_id="{{ $sdm->id }}"
                                class="block mt-1 text-sm border-gray-300 rounded-md shadow-sm _js-input-change-store focus:border-blue-300 focus:ring focus:ring-red-200 focus:ring-opacity-50"
                                name="store_id" id="store_id">
                                @if (Auth::user()->role_id <= 3) <option value="0">Kosong</option>
                                    @endif
                                    @foreach ($stores as $store)
                                    <option value="{{ $store->id }}" {{ (int)$store->id === (int)$sdm->store_id ?
                                        'selected'
                                        :
                                        ''
                                        }}>{{
                                        $store->name }}</option>
                                    @endforeach
                            </select>
                        </form>
                        @endforeach
    </div>
    <hr class="my-10">
    @endif
    @if (Auth::user()->role_id <= 3 && $armadas) <div class="flex flex-col gap-2">
        <h2 class="text-lg font-bold text-gray-700">Armada 👉 SDM</h2>
        @foreach ($armadas as $armada)
        <form method="POST" action="" class="flex items-center _js-form-change-armada-sdm">
            @csrf
            <input type="hidden" name="sdm_id" value="{{ $armada->id }}" />
            <a href="{{route('armada', ['store_slug' => $store_slug, 'armada_id' => $armada->id])}}"
                class="mr-1 text-sm font-semibold text-red-700 underline">{{
                $armada->licence_number }} ↗︎</a>
            <span class="ml-auto mr-2 text-sm">👉</span>
            <select data-current_store_id="{{ $armada->store_id }}" data-armada_id="{{ $armada->id }}"
                class="block mt-1 text-sm border-gray-300 rounded-md shadow-sm _js-input-change-employee focus:border-blue-300 focus:ring focus:ring-red-200 focus:ring-opacity-50"
                name="employee_id" id="store_id">
                <option value="0">Cadangan</option>
                @foreach ($sdms as $sdm)
                @if($sdm->employee->count() > 0)
                <option value="{{ $sdm->employee[0]->id }}" {{ (int)$sdm->
                    employee[0]->id === (int)$armada->employee_id
                    ?
                    'selected' : ''
                    }}>{{
                    $sdm->employee[0]->full_name }}</option>
                @endif
                @endforeach
            </select>
        </form>
        @endforeach
        </div>
        <hr class="my-10">
        @endif
        <form method="POST" action="{{ route('logout') }}">
            @csrf

            <x-link class="relative w-full bg-red-600" :href="route('logout')" onclick="event.preventDefault();
                                this.closest('form').submit();">
                Log Out
                <svg class="absolute top-0 bottom-0 w-6 h-full ml-auto right-2" xmlns="http://www.w3.org/2000/svg"
                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
            </x-link>
        </form>
        <p class="w-full mt-3 text-xs italic text-center text-gray-400">Gasplus Manager
            v{{ env('APP_VERSION', '1.0') }}
        </p>
        </div>

        <x-slot name="js">
            <script>
                $( document ).ready(function() {
                    $('._js-input-store').change(function() {
                        const url = this.value;
                        window.location.replace(url);
                    });

                    const stores = @json($stores);
                    const sdms = @json($sdms);
                    $('._js-input-change-store').change(function() {
                        console.log('change store');
                        const selectedStoreId = $(this).val() ? $(this).val() : 0;
                        const currentStoreId = $(this).data('current_store_id');
                        const sdmId = $(this).data('sdm_id');
                        const selectedStore = stores.find((item) => parseInt(item.id) === parseInt(selectedStoreId));
                        const currentStore = stores.find((item) => parseInt(item.id) === parseInt(currentStoreId));
                        const sdm = sdms.find((item) => parseInt(item.id) === parseInt(sdmId));
                        console.log('selectedStore', selectedStore);
                        console.log('currentStore', currentStore);
                        console.log('sdm', sdm);
                        if (confirm(`Pindah 🛵 ${sdm.name}: 🛍 ${currentStore ? currentStore.name : 'Kosong'} ➡️ ke toko 🛍 ${selectedStore ? selectedStore.name : 'Kosong'}?`)) {
                            // setTimeout(() => {
                                $(this).parent('._js-form-change-sdm-store').submit();
                            // }, 250);
                        } else {
                            $(this).val(currentStoreId);
                        }
                    });

                    $('._js-btn-sync-stock').click(function() {
                        Swal.fire({
                            // title: "Updating stock from Accurate",
                            text: "Updating stock from Accurate...",
                            // icon: "question"
                            allowOutsideClick: false,
                            didOpen: () => {
                                Swal.showLoading();
                                axios
                                    .get("/accurate-sync-stock")
                                    .then(function (res) {
                                        console.log("🚀 ~ res:", res)
                                        if (res.data) {
                                            Swal.fire(res.data, "", "success");
                                        } else {
                                            Swal.fire("Gagal sync", "", "error");    
                                        }
                                    })
                                    .catch(function (err) {
                                        console.log("err", err);
                                        Swal.fire("Gagal sync", "", "error");
                                    })
                                    .finally(function() {
                                        Swal.hideLoading();
                                    });
                            }
                        });
                    })
                });
            </script>
        </x-slot>
</x-app-layout>