<x-app-layout>
    <x-slot name="store_slug">
        {{ $store_slug }}
    </x-slot>

    <x-slot name="head">
        <link rel="stylesheet" href="https://unpkg.com/leaflet@1.8.0/dist/leaflet.css"
            integrity="sha512-hoalWLoI8r4UszCkZ5kL8vayOGVae1oxXe/2A4AO6J9+580uKHDO3JdHb7NzwwzK5xr/Fs0W40kiNHxM9vyTtQ=="
            crossorigin="" />
        <script src="https://unpkg.com/leaflet@1.8.0/dist/leaflet.js"
            integrity="sha512-BB3hKbKWOc9Ez/TAwyWxNXeoV9c1v6FIeYiBieIWkpLjauysF18NzgR1MBNBXf8/KABdlkX68nAhlwcDFLGPCQ=="
            crossorigin=""></script>
    </x-slot>

    <x-slot name="header">
        <a class="flex items-center -ml-3"
            href="{{ route('jobs', ['store_slug' => $store_slug, 'date' => $date_now]) }}"><svg class="w-9 h-9"
                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
            <h2 class="text-lg font-bold">
                {{-- {{ __('Job List') }} --}}
                <span class="capitalize">{{ $store_slug }}</span>
            </h2>
        </a>
    </x-slot>

    <div class="pb-20 pt-28">
        <div
            class="fixed top-0 left-0 right-0 z-auto flex items-center justify-center h-screen text-5xl font-bold text-gray-400">
            Notif Send ✅
        </div>
    </div>

    <x-slot name="js">
        <script type="module">
            @php
            function Get($index, $defaultValue = '') {
                return isset($_GET[$index]) ? $_GET[$index] : $defaultValue;
            }
            @endphp
            const msg = `@php echo urldecode(Get("msg")) @endphp`;
            const to = '@php echo Get("to") @endphp';
            if (msg && to) {
                console.log("🚀 ~ msg:", msg)
                console.log("🚀 ~ to:", to)
                const url = `https://wa.me/${to}?text=${encodeURIComponent(msg.replaceAll('<br>', '\n'))}`;
                // window.open(url);
                console.log("🚀 ~ url:", url)
                // setTimeout(() => {
                    window.location.href = url;
                // }, 500);
                // history.pushState({}, null, url);
            }
        </script>
    </x-slot>
</x-app-layout>