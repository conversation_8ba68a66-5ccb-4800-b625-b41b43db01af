<!DOCTYPE html>
<html>

<head>
  <title>Pengiriman Baru 🏍 {{ $order['code'] }}<</title> </head> <body>
      <h1>Pengiriman Baru 🏍 {{ $order['code'] }}</h1>
      <br>
      <h3>Pelanggan</h3>
      <hr>
      <p><strong>Nama:</strong> {{ $order['customer']['name'] }}</p>
      <p><strong>HP:</strong> <a
          href="https://wa.me/{{ $order['receiver_phone'] ? $order['receiver_phone'] : $order['customer']['phone'] }}"
          target="_blank">WA 📲
          {{ $order['receiver_phone'] ? $order['receiver_phone'] : $order['customer']['phone'] }}</a></p>
      <p><strong>Alamat:</strong> {{ $order['address']['address'] }}</p>
      @if ($order['address']['latlng'])
      <p><strong>Koordinat:</strong> <a href="http://www.google.com/maps/place/{{ $order['address']['latlng'] }}}"
          target="_blank">GMAP 📍 {{ $order['address']['latlng'] }}</a></p>
      @endif

      <h3>Produk</h3>
      <hr>
      @foreach ($order['products'] as $product)
      <p>{{ $product['pivot']['qty'] }}x {{ $product['name'] }}
        @Rp{{ number_format($product['pivot']['price'], 0, '', '.') }}
      </p>
      @endforeach
      @if($order['note'])
      <p><strong>Catatan: </strong> <mark>{{ $order['note'] }}</mark></p>
      @endif

      <h3>Pembayaran</h3>
      <hr>
      @if ($order['payment'] == 'cash')
      <p><strong>Tunai</strong> (Rp{{ $order['amount_will_pay'] }})</p>
      <p><em>*bisa juga transfer ke:</em></p>
      @else
      <p><strong>Transfer</strong></p>
      @endif
      @foreach ($order['store']['banks'] as $bank)
      <p><strong>{{ $bank['bank_name'] }} {{ $bank['account_number'] }} 🏧</strong><br>a.n. {{ $bank['holder_name'] }}
      </p>
      @endforeach
      @if ($order['payment'] == 'cash')
      <p><strong>Jika bayar transfer, harap mengirimkan foto <mark>BUKTI TRANSFER</mark></strong></p>
      @else
      <p><strong>Harap mengirimkan foto <mark>BUKTI TRANSFER</mark></strong></p>
      @endif
      @php
      $total_qty = 0;
      foreach ($order['products'] as $product) {
      $total_qty = $total_qty + intval($product['pivot']['qty']);
      }
      @endphp
      <p><strong>Total QTY:</strong> {{ $total_qty }}</p>
      <p><strong>Total Harga:</strong> Rp{{ number_format($order['total'], 0, '', '.') }}</p>
      <p>Ongkos Kirim: <strong>FREE</strong></p>
      <p>Ekspedisi: <strong>Kurir Gasplus</strong></p>
      <p><strong>TOTAL BAYAR: Rp{{ number_format($order['total'], 0, '', '.') }}</strong></p>
      </body>

</html>