@extends('layouts.offline')

@section('header')
<h2 class="font-bold text-lg relative w-full">
    Job List <span class="text-red-400">(Offline)</span>
    <div class="absolute z-10 top-1 right-1 text-right">
        <p style="font-size: 7px; line-height: 2;" class="text-red-600 font-light">v1.6</p>
        {{-- <p style="font-size: 7px; line-height: 2;" class="text-red-600 font-light _js-latlng">latlng</p>
        <p style="font-size: 7px; line-height: 2;" class="text-red-600 font-light _js-latlng-accuracy">accuracy</p> --}}
    </div>
</h2>
<button class="ml-auto text-3xl focus:outline-none" @click="onOpenSearch">🔍</button>
<template v-if="searchIsShow">
    <div :class="['absolute left-0 inset-0 bg-red-700 items-center z-50 flex']">
        <input v-model="search"
            class="bg-red-700 placeholder-red-200 text-2xl min-w-0 focus:outline-none px-4 flex-1 mr-5 _js-input-search"
            placeholder="Cari..." />
        <button class="ml-auto text-3xl mr-3 focus:outline-none" @click="onCloseSearch">╳</button>
    </div>
</template>
@endsection

@section('subheader')
<div class="flex flex-col p-4 justify-center h-14 text-white bg-blue-800">
    <p class="font-bold text-sm capitalize">{{ date('d F Y') }}: Job Diambil</p>
    {{-- @if ($orders->count()) --}}
    <p class="font-light text-xs">Updated <span class="_js-orders-saved"></span></p>
    {{-- @endif --}}
</div>
@endsection

@section('content')
<div class="pt-28 pb-20">
    <div class="_js-loading fixed top-28 left-0 right-0 bottom-16 z-50 flex justify-center items-center">
        <div class="max-w-xl flex w-full h-full justify-center items-center text-5xl font-bold text-gray-300 bg-white">
            Loading...
        </div>
    </div>
    <order-item :search="search">
    </order-item>
    {{-- @foreach ($orders as $order)
    @include('components.order-item', ['order'=> $order, 'date_now' => $date_now])
    @endforeach --}}
</div>

<div class="_js-modal-selesai h-full w-full fixed left-0 top-0 z-50 flex justify-center items-center bg-black bg-opacity-50 px-3 py-4"
    style="display: none;">
    <!-- modal -->
    <div class="bg-white rounded shadow-lg flex flex-col h-full w-full max-w-md">
        <!-- modal header -->
        <div class="border-b px-4 py-2 flex justify-between items-center">
            <h3 class="text-2xl font-bold">Selesai Job</h3>
            <button class="text-red-600 font-bold text-3xl _js-close-modal-selesai">&cross;</button>
        </div>
        <!-- modal body -->
        <form class="_js-form-selesai-job flex-1 p-3 overflow-y-auto flex flex-col">
            @csrf
            <input type="hidden" id="order_id" name="order_id" class="_js-input-order-id">
            {{-- <input type="hidden" id="received_latlng" name="received_latlng" class="_js-input-received-latlng"> --}}
            {{-- <input type="hidden" id="received_latlng_accuracy" name="received_latlng_accuracy"
                class="_js-input-received-latlng-accuracy"> --}}
            <label class="block mb-5 mt-1" for="received_by">
                <div class="text-gray-700 font-bold">Nama Penerima <span class="text-red-600">*</span>
                </div>
                <input type="text" id="received_by" name="received_by"
                    class="_js-input-received-by mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-red-200 focus:ring-opacity-50"
                    value="" placeholder="">
            </label>
            <label class="block mb-5" for="driver_note">
                <div class="text-gray-700 font-bold">Catatan
                </div>
                <textarea id="driver_note" name="driver_note"
                    class="_js-input-driver-note mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-red-200 focus:ring-opacity-50"
                    rows="2"></textarea>
            </label>
            <div class="mb-1 flex-1 flex flex-col pb-3 h-1 overflow-hidden max-h-1 opacity-0 pointer-events-none">
                <div class="text-gray-700 font-bold">Foto Penyelesaian</div>
                <label
                    class="_js-btn-photo mt-1 w-full flex flex-1 flex-col justify-center items-center px-4 py-6 bg-white text-gray-400 rounded-lg shadow tracking-wide uppercase border border-gray-300 cursor-pointer">
                    <svg class="w-8 h-8" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd"
                            d="M4 5a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V7a2 2 0 00-2-2h-1.586a1 1 0 01-.707-.293l-1.121-1.121A2 2 0 0011.172 3H8.828a2 2 0 00-1.414.586L6.293 4.707A1 1 0 015.586 5H4zm6 9a3 3 0 100-6 3 3 0 000 6z"
                            clip-rule="evenodd" />
                    </svg>
                    <span class="mt-2 text-base font-bold leading-normal">Foto</span>
                    <input type='file' accept="image/*" id="photo" name="photo" class="hidden _js-input-photo" />
                </label>
                <div class="flex flex-col flex-1 mt-1 relative _js-wrp-preview" style="display: none;">
                    <img class="_js-image-preview shadow border border-gray-300 object-cover h-auto rounded-lg"
                        src="/public/gasplus/img/stores/1/Toko Palagan.jpg" alt="">
                    <button type="button"
                        class="_js-btn-remove-photo flex justify-center items-center px-2 py-1.5 focus:outline-none rounded-md font-bold shadow-lg border-2 border-red-400 uppercase tracking-widest bg-red-600 text-white absolute top-2 right-2 text-xs">
                        <svg class="h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                            fill="currentColor">
                            <path fill-rule="evenodd"
                                d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                                clip-rule="evenodd" />
                        </svg> Hapus Foto</button>
                </div>
            </div>
        </form>
        <div class="flex justify-end items-center w-100 border-t p-3">
            {{-- <button
                 class="bg-red-600 hover:bg-red-700 px-3 py-1 rounded text-white mr-1 close-modal">SELESAI JOB</button> --}}
            <button type="button"
                class="bg-green-500 py-2 shadow-lg px-3 rounded text-white w-full font-bold _js-btn-submit-selesai-job">SELESAIKAN
                JOB</button>
        </div>
    </div>
</div>

<div class="_js-modal-batal h-full w-full fixed left-0 top-0 z-50 flex justify-center items-center bg-black bg-opacity-50 px-3 py-4"
    style="display: none;">
    <!-- modal -->
    <div class="bg-white rounded shadow-lg flex flex-col h-full w-full max-w-md">
        <!-- modal header -->
        <div class="border-b px-4 py-2 flex justify-between items-center">
            <h3 class="text-2xl font-bold">Batal Job <span class="text-red-600">*</span></h3>
            <button class="text-red-600 font-bold text-3xl _js-close-modal-batal">&cross;</button>
        </div>
        <!-- modal body -->
        <form class="_js-form-batal-job flex-1 p-3 overflow-y-auto flex flex-col">
            @csrf
            <input type="hidden" id="order_id" name="order_id" class="_js-input-order-id">
            {{-- <input type="hidden" id="received_latlng" name="received_latlng" class="_js-input-received-latlng"> --}}
            {{-- <input type="hidden" id="received_latlng_accuracy" name="received_latlng_accuracy"
                class="_js-input-received-latlng-accuracy"> --}}
            <label class="block mb-5" for="driver_note">
                <div class="text-gray-700 font-bold">Alasan Pembatalan
                </div>
                <textarea id="driver_note" name="driver_note"
                    class="_js-input-driver-note mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-red-200 focus:ring-opacity-50"
                    rows="2"></textarea>
            </label>
        </form>
        <div class="flex justify-end items-center w-100 border-t p-3">
            {{-- <button
                 class="bg-red-600 hover:bg-red-700 px-3 py-1 rounded text-white mr-1 close-modal">SELESAI JOB</button> --}}
            <button type="button"
                class="bg-red-500 py-2 shadow-lg px-3 rounded text-white w-full font-bold _js-btn-submit-batal-job">BATALKAN
                JOB</button>
        </div>
    </div>
</div>
@endsection

@section('js')
<script>
    function parseJson(str) {
        try {
            const value = JSON.parse(str);
            return value;
        } catch (e) {
            return str;
        }
    }
    let latlng = null;
    let latlng_accuracy = null;
    let runGetLocation;
    function success(pos) {
        var crd = pos.coords;
        // console.log("Your current position is:");
        // console.log(`Latitude : ${crd.latitude}`);
        // console.log(`Longitude: ${crd.longitude}`);
        // console.log(`More or less ${crd.accuracy} meters.`);
        latlng = crd.latitude+','+crd.longitude;
        latlng_accuracy = crd.accuracy;
        $('._js-loading').fadeOut('fast');
        // $('._js-latlng').html(latlng);
        // $('._js-latlng-accuracy').html(latlng_accuracy)
        // $('.position').html(`Your current position is:<br>
        // Latitude : ${crd.latitude}<br>
        // Longitude: ${crd.longitude}<br>
        // More or less ${crd.accuracy} meters.`);
    }
    function error(err) {
        console.warn("ERROR(" + err.code + "): " + err.message);
        $('._js-loading').fadeOut('fast');
    }
    const options = {
        enableHighAccuracy: true,
        timeout: 5000,
        maximumAge: 0
    };
    function getLocation() {
        navigator.geolocation.getCurrentPosition(
            success,
            error,
            options
        );
    }
    $( document ).ready(function() {
        console.log('test offline');
        let gp_orders_finished = localStorage.getItem('gp_orders_finished') ? parseJson(parseJson(localStorage.getItem('gp_orders_finished'))) : [];
        let gp_orders_canceled = localStorage.getItem('gp_orders_canceled') ? parseJson(parseJson(localStorage.getItem('gp_orders_canceled'))) : [];
        const gp_orders_saved_at = localStorage.getItem('gp_orders_saved_at');
        const gp_user_id = localStorage.getItem('gp_user_id');
        const gp_current_orders = localStorage.getItem('gp_current_orders');
        let currentOrders = gp_current_orders ? parseJson(parseJson(gp_current_orders)) : [];
        console.log('currentOrders', currentOrders);
        console.log('gp_orders_finished', gp_orders_finished);
        console.log('gp_orders_canceled', gp_orders_canceled);
        $('._js-orders-saved').html(gp_orders_saved_at);
        setTimeout(() => {
            if (navigator.geolocation) {
                clearInterval(runGetLocation);
                runGetLocation = setInterval(getLocation, 1000);
            } else {
                // x.innerHTML = "Geolocation is not supported by this browser.";
                console.log("Geolocation is not supported by this browser.");
                $('._js-loading').fadeOut('fast');
            }
        }, 500);

        // Job Selsai
        // ====================================================
        const $modalSelesai = $('._js-modal-selesai');

        const $btnOpenModalSelesai = $('._js-btn-selesai');
        const $btnCloseModalSelesai = $('._js-close-modal-selesai');

        $btnOpenModalSelesai.on('click', function() {
            $modalSelesai.fadeIn('fast');
            const $this = $(this);
            const orderId = $this.data('order_id');
            $('._js-input-order-id').val(orderId);
            if (navigator.geolocation) {
                runGetLocation = setInterval(getLocation, 1000);
            } else {
                // x.innerHTML = "Geolocation is not supported by this browser.";
                console.log("Geolocation is not supported by this browser.");
            }

        });
        $btnCloseModalSelesai.on('click', function (){
            $modalSelesai.fadeOut('fast');
            $('._js-input-order-id').val('');
            $('._js-input-received-by').val('');
            $('._js-input-driver-note').val('');
            $('._js-btn-photo').show();
            $("._js-image-preview").attr("src", '');
            $('._js-input-photo').val('');
            $('._js-wrp-preview').hide();
            clearInterval(runGetLocation);
        });

        $('._js-input-photo').on('change', function() {
            if (this.files && this.files[0]) {
            // if (this.files[0].size > 3 * 1024 * 1024) {
            //     // Check the constraint
            //     this.setCustomValidity(
            //     "The selected file must not be larger than 3 MB"
            //     );
            // } else {
            //     this.setCustomValidity("");
            // }
                var reader = new FileReader();
                reader.readAsDataURL(this.files[0]);
                reader.onload = function (e) {
                    $("._js-image-preview").attr("src", e.target.result);
                    $('._js-btn-photo').hide();
                    $('._js-wrp-preview').show();
                };
            }
        });

        $('._js-btn-remove-photo').on('click', function() {
            $('._js-btn-photo').show();
            $("._js-image-preview").attr("src", '');
            $('._js-input-photo').val('');
            $('._js-wrp-preview').hide();
        });

        $('._js-btn-submit-selesai-job').on('click', function() {
            // $('._js-input-received-latlng').val(latlng);
            // $('._js-input-received-latlng-accuracy').val(latlng_accuracy);
            const $form = $('._js-form-selesai-job');
            const inputs = new FormData($form[0]);
            const orderId = inputs.get('order_id');

            if (!inputs.get('received_by')) {
                mdtoast('"Nama Penerima" harus diisi!', { duration: 2000, type: mdtoast.ERROR });
            // } else if (!inputs.get('photo').name) {
            //     mdtoast('"Foto Penyelesaian" harus diisi!', { duration: 2000, type: mdtoast.ERROR });
            } else {
                if (confirm('Selesaikan job?')) {
                    mdtoast(
                        'LOADING...', {
                            // type: mdtoast.INFO,
                            interaction: true, 
                            actionText: 'CLOSE',
                            action: function(){ this.hide(); }
                        });
                    const newDate = new Date();
                    const newTime = newDate.toLocaleTimeString('it-IT');
                    const currentDateTime = moment().format('YYYY-MM-DD') + ' ' + newTime;
                    const dataPenyelesaian = {
                        order_id: inputs.get('order_id'),
                        driver_id: gp_user_id,
                        received_latlng: latlng,
                        received_latlng_accuracy: latlng_accuracy,
                        received_by: inputs.get('received_by'),
                        driver_note: inputs.get('driver_note'),
                        payment_method_ask: $('._js-input-payment-method-ask-'+orderId).val(),
                        payment_note: $('._js-input-payment-note-'+orderId).val(),
                        received_at: currentDateTime
                    }
                    console.log('dataPenyelesaian', dataPenyelesaian);
                    gp_orders_finished.push(dataPenyelesaian);
                    localStorage.setItem('gp_orders_finished', JSON.stringify(gp_orders_finished));
                    // currentOrders = currentOrders.filter(obj => obj.id !== parseInt(dataPenyelesaian.order_id))
                    // localStorage.setItem('gp_current_orders', JSON.stringify(currentOrders));
                    $modalSelesai.fadeOut('fast');
                    $('._js-input-order-id').val('');
                    $('._js-input-received-by').val('');
                    $('._js-input-driver-note').val('');
                    $('._js-btn-photo').show();
                    $("._js-image-preview").attr("src", '');
                    $('._js-input-photo').val('');
                    $('._js-wrp-preview').hide();
                    $('._js-order-item-'+orderId).addClass('animate__bounceOut');
                    setTimeout(() => {
                        $('._js-order-item-'+orderId).remove();
                        mdtoast('JOB BERHASIL DISELESAIKAN', { duration: 3000, type: mdtoast.SUCCESS });
                    }, 1000);
                }
            }
        });


        // Job Batal
        // ====================================================
        const $modalBatal = $('._js-modal-batal');

        const $btnOpenModalBatal = $('._js-btn-batal');
        const $btnCloseModalBatal = $('._js-close-modal-batal');

        $btnOpenModalBatal.on('click', function() {
            $modalBatal.fadeIn('fast');
            const $this = $(this);
            const orderId = $this.data('order_id');
            $('._js-input-order-id').val(orderId);
            if (navigator.geolocation) {
                runGetLocation = setInterval(getLocation, 1000);
            } else {
                // x.innerHTML = "Geolocation is not supported by this browser.";
                console.log("Geolocation is not supported by this browser.");
            }

        });
        $btnCloseModalBatal.on('click', function (){
            $modalBatal.fadeOut('fast');
            $('._js-input-order-id').val('');
            $('._js-input-driver-note').val('');
            clearInterval(runGetLocation);
        });

        $('._js-btn-submit-batal-job').on('click', function() {
            // $('._js-input-received-latlng').val(latlng);
            // $('._js-input-received-latlng-accuracy').val(latlng_accuracy);
            const $form = $('._js-form-batal-job');
            const inputs = new FormData($form[0]);
            const orderId = inputs.get('order_id');

            if (!inputs.get('driver_note')) {
                mdtoast('"Alasan Pembatalan" harus diisi!', { duration: 2000, type: mdtoast.ERROR });
            // } else if (!inputs.get('photo').name) {
            //     mdtoast('"Foto Penyelesaian" harus diisi!', { duration: 2000, type: mdtoast.ERROR });
            } else {
                if (confirm('Batalkan job?')) {
                    console.log('inputs', inputs);
                    mdtoast(
                        'LOADING...', {
                            // type: mdtoast.INFO,
                            interaction: true, 
                            actionText: 'CLOSE',
                            action: function(){ this.hide(); }
                        });
                    const newDate = new Date();
                    const newTime = newDate.toLocaleTimeString('it-IT');
                    const currentDateTime = moment().format('YYYY-MM-DD') + ' ' + newTime;
                    const dataPembatalan = {
                        order_id: inputs.get('order_id'),
                        driver_id: gp_user_id,
                        received_latlng: latlng,
                        received_latlng_accuracy: latlng_accuracy,
                        driver_note: inputs.get('driver_note'),
                        received_at: currentDateTime
                    }
                    console.log('dataPembatalan', dataPembatalan);
                    gp_orders_canceled.push(dataPembatalan);
                    localStorage.setItem('gp_orders_canceled', JSON.stringify(gp_orders_canceled));
                    // currentOrders = currentOrders.filter(obj => obj.id !== parseInt(dataPembatalan.order_id))
                    // localStorage.setItem('gp_current_orders', JSON.stringify(currentOrders));
                    $modalBatal.fadeOut('fast');
                    $('._js-input-order-id').val('');
                    $('._js-input-driver-note').val('');
                    $('._js-order-item-'+orderId).addClass('animate__bounceOut');
                    setTimeout(() => {
                        $('._js-order-item-'+orderId).remove();
                        mdtoast('JOB BERHASIL DIBATALKAN', { duration: 3000, type: mdtoast.SUCCESS });
                    }, 1000);
                }
            }
        });
    });
</script>
@endsection