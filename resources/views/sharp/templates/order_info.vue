<a
    v-if="
        typeof customer_latlng !== 'undefined' &&
            typeof received_latlng !== 'undefined'
    "
    :href="
        'https://www.google.com/maps/dir/' +
            customer_latlng +
            '/' +
            received_latlng
    "
    target="_blank"
    class="SharpButton SharpButton--secondary SharpButton--sm"
    style="margin-bottom: 10px">Bedakan Koordinat 📍 ➡️</a>
<div
    v-if="typeof received_latlng !== 'undefined'"
><small><strong>Koordinat Penyelesaian</strong> <a :href="'http://www.google.com/maps/place/'+received_latlng" target="_blank">{{ received_latlng }}</a></small></div>
<div
    v-if="typeof received_latlng_accuracy !== 'undefined'"
><small><strong>Akurasi GPS</strong> {{ received_latlng_accuracy }}m</small></div>
<div
    v-if="typeof distance_customer_received !== 'undefined'"
><small><strong>Jara<PERSON></strong> {{ distance_customer_received }}m</small><strong v-if="is_offline == 'y'" style="margin-left: 6px;">(OFFLINE)</strong></div>
<div
    v-if="typeof distance_store_customer !== 'undefined'"
><small><strong>Jarak Tempuh</strong> {{ distance_store_customer }}m</small></div>
<div
    v-if="typeof total !== 'undefined'"
><small><strong>Total</strong> Rp{{ total }}</small></div>
