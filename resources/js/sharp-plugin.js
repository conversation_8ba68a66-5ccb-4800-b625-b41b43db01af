import <PERSON> from "sharp-plugin";
// import TextIcon from "./components/TextIcon";
// import Title from "./components/Title";
// import NumberPrepend from "./components/NumberPrepend";
// import TextPrependSlugify from "./components/TextPrependSlugify";
// import TextSlugify from "./components/TextSlugify";
import TextCustom from "./components/TextCustom";
import TextCoordinate from "./components/TextCoordinate";
import TextLocation from "./components/TextLocation";
import TextLocationAccuracy from "./components/TextLocationAccuracy";
import $ from "jquery";

Vue.use(Sharp, {
    customFields: {
        // numberPrepend: NumberPrepend,
        // textPrependSlugify: TextPrependSlugify,
        // textSlugify: TextSlugify,
        textCustom: TextCustom,
        textCoordinate: TextCoordinate,
        textLocation: TextLocation,
        textLocationAccuracy: TextLocationAccuracy
    }
});

function triggerResize() {
    if (typeof Event === "function") {
        window.dispatchEvent(new Event("resize"));
    } else {
        const evt = document.createEvent("UIEvents");
        evt.initUIEvent("resize", true, false, window, 0);
        window.dispatchEvent(evt);
    }
}

// force update maps components on tab active
$("body").on("click", ".SharpGeolocation .SharpButton", function() {
    if (typeof triggerResize !== "undefined") triggerResize();
    console.log("resize");
});
