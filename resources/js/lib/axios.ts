import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios';

// Helper function to get CSRF token
function getCsrfToken(): string | null {
    const metaTag = document.querySelector('meta[name="csrf-token"]') as HTMLMetaElement;
    return metaTag ? metaTag.content : null;
}

// Create axios instance with default configuration
const axiosInstance: AxiosInstance = axios.create({
    baseURL: '/',
    timeout: 30000,
    withCredentials: true,
    headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
    },
});

// Request interceptor to add CSRF token
axiosInstance.interceptors.request.use(
    (config: AxiosRequestConfig) => {
        const csrfToken = getCsrfToken();
        if (csrfToken && config.headers) {
            config.headers['X-CSRF-TOKEN'] = csrfToken;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// Response interceptor for error handling
axiosInstance.interceptors.response.use(
    (response: AxiosResponse) => {
        return response;
    },
    (error) => {
        // Handle authentication errors specifically
        if (error.response?.status === 401) {
            const errorMessage = 'Authentication required. Please log in to access calendar data.';
            error.message = errorMessage;
        } else if (error.response?.data?.message) {
            error.message = error.response.data.message;
        } else if (error.response?.data?.error) {
            error.message = error.response.data.error;
        } else if (error.response) {
            error.message = `HTTP ${error.response.status}: ${error.response.statusText}`;
        }
        
        return Promise.reject(error);
    }
);

export default axiosInstance;
