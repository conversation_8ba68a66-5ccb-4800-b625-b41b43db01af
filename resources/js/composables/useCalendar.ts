import axiosInstance from '@/lib/axios';
import type { AxiosResponse } from 'axios';
import useSWRV from 'swrv';
import { computed, ref, watch, type Ref } from 'vue';

export interface CalendarData {
    date: string;
    day: string;
    url: string;
    total: number;
    bebas: number;
    diambil: number;
    terkirim: number;
    selesai: number;
    batal: number;
    overtime: number;
    bbro: number;
}

export interface CalendarApiResponse {
    data: CalendarData[];
    sum_total_job: number;
    sum_total_bebas: number;
    sum_total_diambil: number;
    sum_total_terkirim: number;
    sum_total_selesai: number;
    sum_total_batal: number;
    sum_total_overtime: number;
    sum_total_bbro: number;
}

export interface UseCalendarOptions {
    revalidateInterval?: number;
}

export interface UseCalendarReturn {
    data: Ref<CalendarApiResponse | null>;
    loading: Ref<boolean>;
    error: Ref<string | null>;
    revalidate: () => Promise<void>;
}

/**
 * Calendar data fetcher function for SWRV
 */
const fetchCalendarData = async (url: string): Promise<CalendarApiResponse> => {
    try {
        const response: AxiosResponse<CalendarApiResponse> = await axiosInstance.get(url);
        return response.data;
    } catch (error: any) {
        // Enhanced error handling for authentication and other errors
        if (error.response?.status === 401) {
            throw new Error('Authentication required. Please log in to access calendar data.');
        }

        // Use the error message from axios interceptor or fallback
        throw new Error(error.message || 'An error occurred while fetching calendar data');
    }
};

/**
 * SWRV-based calendar composable with Axios HTTP client
 *
 * @param store_slug - Store identifier
 * @param month - Month in YYYY-MM format
 * @param options - Configuration options
 * @returns Calendar data, loading state, error state, and revalidate function
 */
export function useCalendar(store_slug: string, month: string, options: UseCalendarOptions = {}): UseCalendarReturn {
    const { revalidateInterval = 3000 } = options;

    // Generate the API endpoint URL
    const apiUrl = computed(() => `/api/calendar/${store_slug}/${month}`);

    // Configure SWRV options
    const swrvOptions = {
        refreshInterval: revalidateInterval > 0 ? revalidateInterval : 0,
        revalidateOnFocus: true,
        revalidateOnReconnect: true,
        shouldRetryOnError: false, // We'll handle retries manually if needed
        errorRetryCount: 1,
        errorRetryInterval: 1000,
        dedupingInterval: 2000, // Prevent duplicate requests within 2 seconds
    };

    // Use SWRV for data fetching
    const { data: swrvData, error: swrvError, mutate } = useSWRV(apiUrl, fetchCalendarData, swrvOptions);

    // Create reactive references for backward compatibility
    const data = ref<CalendarApiResponse | null>(null);
    const loading = ref<boolean>(false);
    const error = ref<string | null>(null);

    // Watch SWRV data and sync with our reactive refs
    watch(
        swrvData,
        (newData) => {
            data.value = newData || null;
        },
        { immediate: true },
    );

    // Watch SWRV error and sync with our reactive refs
    watch(
        swrvError,
        (newError) => {
            error.value = newError?.message || null;
        },
        { immediate: true },
    );

    // Watch SWRV loading state (derived from data and error states)
    watch(
        [swrvData, swrvError],
        ([newData, newError]) => {
            // SWRV doesn't provide a loading state directly, so we derive it
            const isLoading = !newData && !newError;
            loading.value = isLoading;
        },
        { immediate: true },
    );

    // Revalidate function that triggers SWRV mutate
    const revalidate = async (): Promise<void> => {
        try {
            loading.value = true;
            error.value = null;
            await mutate();
        } catch (err: any) {
            error.value = err.message || 'An error occurred during revalidation';
            console.error('Calendar revalidation error:', err);
        } finally {
            loading.value = false;
        }
    };

    return {
        data,
        loading,
        error,
        revalidate,
    };
}
