require("./bootstrap");
// require("vue-underscore");

// require("alpinejs");

// window.safeParseInt = function(val) {
//     const parsed = parseInt(val);
//     if (isNaN(parsed)) {
//         return 0;
//     }
//     return parsed;
// };

window.Vue = require("vue");
Vue.component("v-select", require("vue-select").default);
// Vue.component("multiselect", require("vue-multiselect").default);
// Vue.component("button-map", require("./components/ButtonMap.vue").default);
Vue.component("order-list", require("./components/OrderList.vue").default);
Vue.component("order-add", require("./components/OrderAdd.vue").default);
Vue.component(
    "operatingcost-list",
    require("./components/OperatingcostList.vue").default
);
Vue.component("stockopname", require("./components/Stockopname.vue").default);
Vue.component("invoice-add", require("./components/InvoiceAdd.vue").default);
Vue.component("purchase-add", require("./components/PurchaseAdd.vue").default);
Vue.component(
    "operatingcost-add",
    require("./components/OperatingcostAdd.vue").default
);
Vue.mixin({
    methods: {
        safeParseInt: function (val) {
            const parsed = parseInt(val);
            if (isNaN(parsed)) {
                return 0;
            }
            return parsed;
        },
    },
});
const app = new Vue({
    el: "#app",
    data: {
        mapIsShow: false,
        searchIsShow: false,
        search: "",
        filter_payment_method: "",
    },
    mounted() {
        const searchQuery = $("#search_query").val();
        // console.log("searchQuery", searchQuery);
        if (searchQuery) {
            this.searchIsShow = true;
            this.search = searchQuery;
        }
    },
    methods: {
        secondsToHms: function (order) {
            if (!order.duration)
                return {
                    hours: 0,
                    minutes: 0,
                    seconds: 0,
                };
            let inp = Number(order.duration);
            const hh = Math.floor(inp / 60 / 60);
            inp -= hh * 60 * 60;
            const mm = Math.floor(inp / 60);
            inp -= mm * 60;
            const ss = Math.floor(inp);
            inp -= ss;
            const tm = {
                hours: hh,
                minutes: mm,
                seconds: ss,
            };
            // console.log("tm", tm);
            return tm;
        },
        onOpenMap: function (e) {
            console.log("e", e.target);
            const orders = $(e.target).data("orders");
            const marketings = $(e.target).data("marketings");
            const store = $(e.target).data("store");
            console.log("orders", orders);
            console.log("store", store);
            this.mapIsShow = true;
            Vue.nextTick(() => {
                const storeLatLng = store.latlng
                    ? store.latlng.split(",")
                    : [-7.4504896, 110.1215301];
                const zoom = store.latlng ? 14 : 10;
                const map = L.map("map").setView(storeLatLng, zoom);
                L.tileLayer("https://tile.openstreetmap.org/{z}/{x}/{y}.png", {
                    maxZoom: 19,
                    attribution: store.name,
                }).addTo(map);
                const markerStore = L.marker(storeLatLng).addTo(map).bindPopup(`
                        <div style="margin-bottom: 2px;"><strong>${store.name}</strong></div>
                        <div style="margin-bottom: 0px;">${store.address}</div>
                    `);
                markerStore._icon.classList.add("huechange-store");
                orders
                    .sort(
                        (a, b) =>
                            new Date(a.created_at) - new Date(b.created_at)
                    )
                    .forEach((order, key) => {
                        const address = order.address;
                        const passLatlng =
                            /^[-+]?([1-8]?\d(\.\d+)?|90(\.0+)?),\s*[-+]?(180(\.0+)?|((1[0-7]\d)|([1-9]?\d))(\.\d+)?)$/.test(
                                address.latlng
                            );
                        if (passLatlng) {
                            const orderCreated = moment(order.created_at);
                            const nowTime = moment();
                            const diffOrderMinute = nowTime.diff(
                                orderCreated,
                                "minutes"
                            );

                            const latlng = address.latlng
                                ? address.latlng.split(",")
                                : null;
                            // if (latlng) {
                            let content = `
                            <div style="margin-bottom: 2px;"><strong>${order.customer.name}</strong></div>
                            <div style="margin-bottom: 8px;">${address.address}</div>
                        `;
                            order.products.forEach((product) => {
                                content += `<div style="margin-bottom: 2px;"><strong>🟢 ${product.code} ${product.name} (${product.pivot.qty}x)</strong></div>`;
                            });

                            content += order.driver_id
                                ? `<div style="margin-top: 6px;"><strong>🛵 ${order.driver.name}</strong></div>`
                                : "";
                            content += order.note_for_driver
                                ? `<div style="margin-top: 6px; color: red;"><strong>📝 ${order.note_for_driver}</strong></div>`
                                : "";

                            let duration = "";
                            if (
                                parseInt(order.duration) >= 5400 ||
                                parseInt(order.duration) <= -5400
                            ) {
                                duration = `<span class="ml-2 text-red-500">🔴 ${
                                    this.secondsToHms(order).hours > 0
                                        ? this.secondsToHms(order).hours + ":"
                                        : ""
                                }${this.secondsToHms(order).minutes}</span>`;
                            } else if (
                                parseInt(order.duration) >= 1800 ||
                                parseInt(order.duration) <= -1800
                            ) {
                                duration = `<span class="ml-2 text-yellow-500">🟡 ${
                                    this.secondsToHms(order).hours > 0
                                        ? this.secondsToHms(order).hours + ":"
                                        : ""
                                }${this.secondsToHms(order).minutes}</span>`;
                            } else {
                                duration = `<span class="ml-2 text-green-500">🟢 ${
                                    this.secondsToHms(order).minutes
                                }</span>`;
                            }
                            let distance = "";
                            if (
                                this.safeParseInt(order.distance_store_customer)
                            ) {
                                distance = `<span class="ml-1 text-gray-500"> ${this.safeParseInt(
                                    order.distance_store_customer
                                )}</span>`;
                            }
                            if (
                                this.safeParseInt(
                                    order.distance_customer_received
                                )
                            ) {
                                distance += `<span class="ml-1 text-gray-500">/ ${this.safeParseInt(
                                    order.distance_customer_received
                                )}</span>`;
                            }
                            content += `<div style="margin-top: 6px;"><strong>🕑 ${moment(
                                order.created_at
                            ).format(
                                "HH:mm"
                            )}${duration}${distance}</strong></div>`;

                            const popup = L.popup({
                                closeOnClick: false,
                                autoClose: false,
                            }).setContent(content);

                            let color = "darkblue";
                            if (parseInt(order.is_urgent)) {
                                color = "darkred";
                            } else if (
                                parseInt(order.status_id) <= 3 &&
                                diffOrderMinute >= 60
                            ) {
                                color = "darkred";
                            } else if (
                                parseInt(order.status_id) <= 3 &&
                                diffOrderMinute >= 30
                            ) {
                                color = "orange";
                            } else if (
                                parseInt(order.status_id) >= 4 &&
                                (parseInt(order.duration) >= 5400 ||
                                    parseInt(order.duration) <= -5400)
                            ) {
                                color = "darkred";
                            } else if (
                                parseInt(order.status_id) >= 4 &&
                                (parseInt(order.duration) >= 1800 ||
                                    parseInt(order.duration) <= -1800)
                            ) {
                                color = "orange";
                            }
                            const jobMarkerIcon = L.AwesomeMarkers.icon({
                                icon: "",
                                markerColor: color,
                                prefix: "not-italic font-bold",
                                html: parseInt(order.is_urgent)
                                    ? key + 1 + "⚠️"
                                    : key + 1,
                            });
                            L.marker(latlng, {
                                icon: jobMarkerIcon,
                            })
                                .addTo(map)
                                .bindPopup(popup);
                            // markers.push(latlng);
                            // }
                        }
                    });
                marketings
                    .sort(
                        (a, b) =>
                            new Date(a.created_at) - new Date(b.created_at)
                    )
                    .forEach((marketing) => {
                        const passLatlng =
                            /^[-+]?([1-8]?\d(\.\d+)?|90(\.0+)?),\s*[-+]?(180(\.0+)?|((1[0-7]\d)|([1-9]?\d))(\.\d+)?)$/.test(
                                marketing.received_latlng
                            );
                        if (passLatlng) {
                            const latlng = marketing.received_latlng
                                ? marketing.received_latlng.split(",")
                                : null;
                            // if (latlng) {
                            let content = `
                            <div style="margin-bottom: 2px;"><strong>🛵 ${marketing.driver.name}</strong></div>
                            <div style="margin-bottom: 8px;">🕑 ${marketing.received_at}</div>
                        `;

                            content += marketing.driver_note
                                ? `<div style="margin-top: 6px;"><strong>📝 ${marketing.driver_note}</strong></div>`
                                : "";

                            const popup = L.popup({
                                closeOnClick: false,
                                autoClose: false,
                            }).setContent(content);
                            // const jobMarkerIcon = L.AwesomeMarkers.icon({
                            //     icon: "",
                            //     markerColor: "green",
                            //     prefix: "not-italic font-bold",
                            //     html: "B" + key + 1,
                            // });
                            const markerBbro = L.marker(latlng)
                                .addTo(map)
                                .bindPopup(popup);
                            markerBbro._icon.classList.add(
                                "huechange-marketing"
                            );
                            // markers.push(latlng);
                            // }
                        }
                    });
            });
        },
        onCloseMap: function () {
            this.mapIsShow = false;
        },
        onOpenSearch: function () {
            // console.log("open search");
            this.searchIsShow = true;
            setTimeout(() => {
                $("._js-input-search").focus();
            }, 1);
        },
        onCloseSearch: function () {
            this.searchIsShow = false;
            this.search = "";
            setTimeout(() => {
                $("._js-input-search").blur();
            }, 1);
        },
        onClickFilterPaymentMethod: function (val) {
            this.filter_payment_method =
                this.filter_payment_method == val ? "" : val;
            // console.log(this.filter_payment_method);
        },
    },
});
