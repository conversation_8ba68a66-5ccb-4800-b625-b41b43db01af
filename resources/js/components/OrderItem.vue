<template>
    <div class="relative">
        <a
            target="_blank"
            :href="
                safeParseInt(roleId) <= 3
                    ? baseUrl + '/cs/form/order/' + order.id
                    : ''
            "
            :class="[
                'absolute inset-0',
                safeParseInt(roleId) <= 3 ? '' : 'pointer-events-none',
            ]"
        ></a>
        <div class="relative pointer-events-none index-10">
            <h4 class="text-lg font-bold">
                {{ order.code
                }}<span class="ml-1 text-xs text-gray-400"
                    >🕐 {{ moment(order.created_at).format("HH:mm") }}</span
                >
                <template v-if="order.received_at">
                    <template
                        v-if="
                            Number(order.duration) > 5400 ||
                            Number(order.duration) < -5400
                        "
                    >
                        <span class="ml-1 text-xs text-red-500"
                            >🔴
                            {{
                                secondsToHms(order).hours > 0
                                    ? secondsToHms(order).hours + ":"
                                    : ""
                            }}{{ secondsToHms(order).minutes }}</span
                        >
                    </template>
                    <template v-else>
                        <span class="ml-1 text-xs text-green-500"
                            >🟢
                            {{
                                secondsToHms(order).hours > 0
                                    ? secondsToHms(order).hours + ":"
                                    : ""
                            }}{{ secondsToHms(order).minutes }}</span
                        >
                    </template>
                </template>
                <template v-if="safeParseInt(order.distance_store_customer)">
                    <span class="ml-1 text-xs text-gray-500">{{
                        safeParseInt(order.distance_store_customer)
                    }}</span>
                </template>
                <template v-if="safeParseInt(order.distance_customer_received)">
                    <span
                        :class="[
                            'text-xs',
                            safeParseInt(order.distance_customer_received) >=
                            200
                                ? 'text-red-700'
                                : 'text-gray-800',
                        ]"
                        >/
                        {{
                            safeParseInt(order.distance_customer_received)
                        }}</span
                    >
                </template>
            </h4>
            <div class="-mt-0.5">
                <p
                    v-if="
                        order.accurate_invoice &&
                        order.accurate_invoice.accurate_no
                    "
                    class="text-xs font-bold text-gray-400"
                >
                    ACC:
                    {{ order.accurate_invoice.accurate_no }}
                </p>
                <p
                    v-if="
                        order.accurate_invoice &&
                        !order.accurate_invoice.accurate_no &&
                        order.accurate_invoice.error_message
                    "
                    class="text-xs font-bold text-red-500"
                >
                    ACC ERROR:
                    {{ order.accurate_invoice.error_message }}
                </p>
            </div>
            <div class="relative clear-both">
                <div class="float-right w-14 ml-1">
                    <div class="w-14">
                        <template v-if="order.address_photo_url">
                            <div class="">
                                <img
                                    :src="order.address_photo_url"
                                    data-zoomable
                                    :id="'order_img_zoom_' + order.id"
                                    class="w-14 h-14 object-cover rounded shadow pointer-events-auto"
                                />
                                <button
                                    :data-order_id="order.id"
                                    type="button"
                                    class="_js-btn-upload-address pointer-events-auto py-1 text-xs w-full rounded overflow-hidden flex justify-center items-center relative shadow border-gray-500 border bg-green-700 text-white text-shadow-xl font-bold mt-1.5"
                                >
                                    RE ⬆️
                                </button>
                            </div>
                        </template>
                        <template v-else>
                            <button
                                :data-order_id="order.id"
                                type="button"
                                class="relative flex items-center justify-center w-full py-1 overflow-hidden text-xs font-bold text-white bg-green-700 border border-gray-500 rounded shadow pointer-events-auto _js-btn-upload-address text-shadow-xl"
                            >
                                UPLOA FOTO RUMAH
                            </button>
                        </template>
                    </div>
                    <button
                        v-if="
                            status === 'total' ||
                            status === 'bebas' ||
                            status === 'diambil' ||
                            status === 'terkirim' ||
                            status === 'selesai'
                        "
                        type="button"
                        @click="onClickPrint"
                        class="mt-2.5 text-xs leading-tight pointer-events-auto rounded shadow right-0 z-10 w-14 h-14 bg-gray-600 text-white flex justify-center items-center font-bold"
                    >
                        🖨<br />PRINT<br />NOTA
                    </button>
                </div>
                <p
                    :class="[
                        'px-2 py-0.5 mb-1 text-xs font-bold rounded inline-block text-white uppercase',
                        '_js-status-item-' + order.id,
                        color[order.status.code],
                    ]"
                >
                    JOB
                    <span :class="['_js-status-text-' + order.id]">{{
                        label[order.status.code]
                    }}</span>
                </p>
                <template v-if="order.driver">
                    <p
                        :class="[
                            'px-2 py-0.5 mb-1 ml-0.5 text-xs bg-blue-500 font-bold rounded inline-block text-white',
                            '_js-driver-item-' + order.id,
                        ]"
                    >
                        💼
                        {{ order.driver.name }}
                        <template
                            v-if="
                                safeParseInt(order.status_id) >= 4 &&
                                !order.received_latlng
                            "
                        >
                            <span class="ml-1">📵 GPSOFF</span>
                        </template>
                    </p>
                </template>
                <template v-if="order.armada">
                    <p
                        :class="[
                            'px-2 py-0.5 mb-1 ml-0.5 text-xs bg-blue-500 font-bold rounded inline-block text-white',
                            '_js-armada-item-' + order.id,
                        ]"
                    >
                        🛵
                        {{ order.armada.licence_number }}
                    </p>
                </template>
                <template v-if="order.received_by">
                    <p
                        :class="[
                            'px-2 py-0.5 mb-1 ml-0.5 text-xs bg-gray-500 font-bold rounded inline-block text-white',
                            '_js-receivedby-item-' + order.id,
                        ]"
                    >
                        🤲
                        {{ order.received_by }}
                    </p>
                </template>
                {{ safeParseInt(order.is_offline) ? "📵OFFLINE" : "" }}
                <template v-if="safeParseInt(order.is_urgent)">
                    <p
                        :class="[
                            'font-black flex px-2 text-white bg-red-600 rounded w-full',
                        ]"
                    >
                        ⚡️⚡️⚡️ 👉 👉 <span class="ml-auto">⚠️ SEGERA!</span>
                    </p>
                </template>
                <template v-if="order.driver_note">
                    <p
                        :class="[
                            'font-bold',
                            safeParseInt(order.status_id) == 5
                                ? 'text-red-600'
                                : '',
                            safeParseInt(order.status_id) != 5
                                ? 'text-blue-600'
                                : '',
                        ]"
                    >
                        ⚠️ {{ order.driver_note }}
                        <template v-if="order.cancelby">
                            <span class="ml-1 text-sm text-gray-300"
                                >(by {{ order.cancelby.name }})</span
                            >
                        </template>
                    </p>
                </template>
                <div class="my-1">
                    <a
                        :href="
                            safeParseInt(roleId) <= 3
                                ? baseUrl +
                                  '/cs/show/customer/' +
                                  order.customer.id
                                : ''
                        "
                        target="_blank"
                        :class="[
                            safeParseInt(roleId) <= 3
                                ? 'pointer-events-auto rounded bg-yellow-500 font-bold text-white px-2 py-0.5'
                                : '',
                        ]"
                        >👤 {{ order.customer.name }}</a
                    >
                </div>
                <p v-if="order.customer.note_special" class="font-bold">
                    📝 {{ order.customer.note_special }}
                </p>
                <div class="flex flex-wrap items-center">
                    <button
                        type="button"
                        @click="
                            onClickGoToChat(
                                toWa(order.customer.phone),
                                order.customer_conversation_id
                            )
                        "
                        class="relative mr-1 text-yellow-600 underline pointer-events-auto"
                    >
                        📲 {{ order.customer.phone }}
                    </button>
                    <template v-if="order.receiver_phone">
                        /
                        <button
                            type="button"
                            @click="
                                onClickGoToChat(
                                    toWa(order.receiver_phone),
                                    order.receiver_conversation_id
                                )
                            "
                            class="relative ml-1 text-green-600 underline pointer-events-auto"
                        >
                            📲 {{ order.receiver_phone }}
                        </button>
                    </template>
                    <a
                        :href="`https://wa.me/${
                            order.notif_to
                        }?text=${encodeURIComponent(
                            order.notif_msg.replaceAll('<br>', '\n')
                        )}`"
                        target="_blank"
                        class="px-1.5 ml-1 pointer-events-auto py-0.5 text-xs font-bold rounded inline-block text-white bg-green-400"
                        >NOTF ➡️</a
                    >
                    <button
                        @click="onClickCopyNotifText"
                        type="button"
                        class="px-1.5 ml-1 gap-0.5 pointer-events-auto py-0.5 text-xs font-bold rounded inline-flex justify-center items-center text-white bg-green-400"
                    >
                        CP
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                            class="w-3.5 h-3.5"
                        >
                            <path
                                d="M7 3.5A1.5 1.5 0 0 1 8.5 2h3.879a1.5 1.5 0 0 1 1.06.44l3.122 3.12A1.5 1.5 0 0 1 17 6.622V12.5a1.5 1.5 0 0 1-1.5 1.5h-1v-3.379a3 3 0 0 0-.879-2.121L10.5 5.379A3 3 0 0 0 8.379 4.5H7v-1Z"
                            />
                            <path
                                d="M4.5 6A1.5 1.5 0 0 0 3 7.5v9A1.5 1.5 0 0 0 4.5 18h7a1.5 1.5 0 0 0 1.5-1.5v-5.879a1.5 1.5 0 0 0-.44-1.06L9.44 6.439A1.5 1.5 0 0 0 8.378 6H4.5Z"
                            />
                        </svg>
                    </button>
                </div>
                <p class="">
                    🏠
                    <span class="font-black text-black"
                        >{{ order.address ? order.address.label : "" }}:</span
                    >
                    {{ order.address ? order.address.address : "" }}
                    <span
                        v-if="
                            order.additionalcosts.some(
                                (ac) => ac.name === 'Biaya antar lantai atas'
                            )
                        "
                        class="px-1.5 ml-1 pointer-events-auto py-0.5 text-xs font-bold rounded inline-block text-black bg-yellow-300"
                        >LANTAI ATAS</span
                    >
                </p>
            </div>
            <template v-if="order.address && order.address.latlng">
                <a
                    :href="
                        order.address
                            ? 'http://www.google.com/maps/place/' +
                              order.address.latlng
                            : ''
                    "
                    target="_blank"
                    class="relative text-yellow-600 underline pointer-events-auto"
                    >📍 {{ order.address ? order.address.latlng : "" }}</a
                >
            </template>
            <p
                v-for="(product, indexProduct) in order.products"
                :key="product.id"
                class="font-bold flex items-center gap-0.5 flex-wrap"
            >
                <template v-if="product.pivot.ppob_ref_id">
                    🟢 {{ product.code + " " + product.name }}&nbsp;-&nbsp;
                    <span
                        class="block w-full -mt-1 ml-5"
                        v-if="product.code === 'EMONEY'"
                        >{{
                            product.ppobcustomerdata.inquiry_data
                                .product_description
                        }}
                        {{
                            product.ppobcustomerdata.inquiry_data
                                .product_nominal
                        }}</span
                    >
                    <span v-else
                        >{{ product.pivot.ppob_nominal / 1000 }}rb</span
                    >
                    <div class="ml-5 -mt-1.5 w-full">
                        <button
                            type="button"
                            @click="onClickCheckPpobStatus(product)"
                            :class="[
                                'px-1.5 gap-0.5 pointer-events-auto py-0.5 text-xs font-bold rounded inline-flex justify-center items-center text-white relative',
                                {
                                    'bg-gray-600':
                                        product.pivot.ppob_status === 'PROCESS',
                                },
                                {
                                    'bg-green-600':
                                        product.pivot.ppob_status === 'SUCCESS',
                                },
                                {
                                    'bg-red-600':
                                        product.pivot.ppob_status === 'FAILED',
                                },
                            ]"
                        >
                            {{
                                product.pivot.ppob_status === "PROCESS"
                                    ? "⏳"
                                    : ""
                            }}
                            {{
                                product.pivot.ppob_status === "SUCCESS"
                                    ? "✅"
                                    : ""
                            }}
                            {{
                                product.pivot.ppob_status === "FAILED"
                                    ? "❌"
                                    : ""
                            }}
                            {{ product.pivot.ppob_status }}
                            <svg
                                :ref="
                                    'ppob-status-spinner-' +
                                    product.pivot.ppob_ref_id
                                "
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 20 20"
                                fill="currentColor"
                                class="w-3 h-3 ml-0.5 stroke-2"
                            >
                                <path
                                    fill-rule="evenodd"
                                    d="M15.312 11.424a5.5 5.5 0 0 1-9.201 2.466l-.312-.311h2.433a.75.75 0 0 0 0-1.5H3.989a.75.75 0 0 0-.75.75v4.242a.75.75 0 0 0 1.5 0v-2.43l.31.31a7 7 0 0 0 11.712-*********** 0 0 0-1.449-.39Zm1.23-3.723a.75.75 0 0 0 .219-.53V2.929a.75.75 0 0 0-1.5 0V5.36l-.31-.31A7 7 0 0 0 3.239 8.188a.75.75 0 1 0 1.448.389A5.5 5.5 0 0 1 13.89 6.11l.311.31h-2.432a.75.75 0 0 0 0 1.5h4.243a.75.75 0 0 0 .53-.219Z"
                                    clip-rule="evenodd"
                                />
                            </svg>
                        </button>
                        <span class="flex text-sm text-gray-500 w-full">
                            AN:
                            {{ product.pivot.ppob_label }} /
                            {{ product.pivot.ppob_key }}
                        </span>
                        <span class="flex text-xs text-gray-400 w-full">
                            Updated:
                            {{
                                moment(product.pivot.updated_at).format(
                                    "YYYY-MM-DD hh:mm:ss"
                                )
                            }}
                        </span>
                        <p
                            v-if="
                                product.pivot.ppob_status === 'FAILED' &&
                                product.ppobresponsedata
                            "
                            class="flex text-xs text-red-600 w-full"
                        >
                            Error: {{ product.ppobresponsedata.error_message }}
                        </p>
                        <p
                            v-if="
                                product.pivot.ppob_status === 'SUCCESS' &&
                                product.ppobresponsedata.response_data.message
                            "
                            class="flex text-xs text-green-600 w-full mt-0.5"
                        >
                            {{ product.ppobresponsedata.response_data.message }}
                        </p>
                        <button
                            v-if="
                                product.ppobresponsedata.response_data.sn &&
                                product.pivot.ppob_status === 'SUCCESS'
                            "
                            type="button"
                            class="px-1.5 gap-0.5 mt-0.5 pointer-events-auto py-0.5 text-sm font-bold rounded inline-flex justify-center items-center text-white bg-green-600"
                            @click="
                                onClickCopyPpobToken(
                                    product.ppobresponsedata.response_data.sn
                                )
                            "
                        >
                            ⚡️ TOKEN:
                            {{
                                product.ppobresponsedata.response_data.sn.split(
                                    "/"
                                )[0]
                            }}
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 20 20"
                                fill="currentColor"
                                class="w-3.5 h-3.5"
                            >
                                <path
                                    d="M7 3.5A1.5 1.5 0 0 1 8.5 2h3.879a1.5 1.5 0 0 1 1.06.44l3.122 3.12A1.5 1.5 0 0 1 17 6.622V12.5a1.5 1.5 0 0 1-1.5 1.5h-1v-3.379a3 3 0 0 0-.879-2.121L10.5 5.379A3 3 0 0 0 8.379 4.5H7v-1Z"
                                />
                                <path
                                    d="M4.5 6A1.5 1.5 0 0 0 3 7.5v9A1.5 1.5 0 0 0 4.5 18h7a1.5 1.5 0 0 0 1.5-1.5v-5.879a1.5 1.5 0 0 0-.44-1.06L9.44 6.439A1.5 1.5 0 0 0 8.378 6H4.5Z"
                                />
                            </svg>
                        </button>
                        <div
                            v-if="product.pivot.ppob_status === 'SUCCESS'"
                            class="flex items-center flex-wrap mt-1 w-full gap-1"
                        >
                            <span
                                v-if="product.pivot.notif_sent_at"
                                target="_blank"
                                class="px-1.5 pointer-events-auto py-0.5 text-xs font-bold rounded inline-block text-green-500 bg-green-100"
                                >NOTIF
                                {{
                                    moment(product.pivot.notif_sent_at).format(
                                        "YYYY-MM-DD hh:mm:ss"
                                    )
                                }}
                                ✅</span
                            >
                            <span
                                v-else
                                target="_blank"
                                class="px-1.5 pointer-events-auto py-0.5 text-xs font-bold rounded inline-block text-red-600 bg-red-100"
                                >NOTIF NOT SENT ⏳</span
                            >
                            <a
                                @click="(e) => onClickMarkAsSent(product, e)"
                                :href="`https://wa.me/${
                                    order.notif_to
                                }?text=${encodeURIComponent(
                                    product.msg_ppob.replaceAll('<br>', '\n')
                                )}`"
                                target="_blank"
                                class="px-1.5 pointer-events-auto py-0.5 text-xs font-bold rounded inline-block text-white bg-green-400"
                                >NOTF ➡️</a
                            >
                            <button
                                @click="onClickCopyNotifPpob(product)"
                                type="button"
                                class="px-1.5 gap-0.5 pointer-events-auto py-0.5 text-xs font-bold rounded inline-flex justify-center items-center text-white bg-green-400"
                            >
                                CP
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    viewBox="0 0 20 20"
                                    fill="currentColor"
                                    class="w-3.5 h-3.5"
                                >
                                    <path
                                        d="M7 3.5A1.5 1.5 0 0 1 8.5 2h3.879a1.5 1.5 0 0 1 1.06.44l3.122 3.12A1.5 1.5 0 0 1 17 6.622V12.5a1.5 1.5 0 0 1-1.5 1.5h-1v-3.379a3 3 0 0 0-.879-2.121L10.5 5.379A3 3 0 0 0 8.379 4.5H7v-1Z"
                                    />
                                    <path
                                        d="M4.5 6A1.5 1.5 0 0 0 3 7.5v9A1.5 1.5 0 0 0 4.5 18h7a1.5 1.5 0 0 0 1.5-1.5v-5.879a1.5 1.5 0 0 0-.44-1.06L9.44 6.439A1.5 1.5 0 0 0 8.378 6H4.5Z"
                                    />
                                </svg>
                            </button>
                        </div>
                    </div>
                </template>
                <template v-else>
                    🟢 {{ product.code + " " + product.name }} ({{
                        product.pivot.qty
                    }}x)
                </template>
            </p>
            <p
                v-for="(
                    additionalcost, indexAdditionalCost
                ) in order.additionalcosts"
                :key="additionalcost.id"
                class="font-bold"
            >
                🟡 {{ additionalcost.name }} (Rp{{
                    additionalcost.total_cost.toLocaleString("id")
                }})
            </p>
            <template v-if="order.note">
                <p class="font-bold text-red-500">
                    📝 {{ order.note }}
                    <span class="text-sm opacity-60">(by Pelanggan)</span>
                </p>
            </template>
            <template v-if="order.note_for_driver">
                <p class="font-bold text-blue-500">
                    🛵 {{ order.note_for_driver }}
                    <span class="text-sm opacity-60">(for Delman)</span>
                </p>
            </template>
            <hr class="my-2" />
            <p
                :class="[
                    'flex justify-between mb-1',
                    {
                        'text-green-600':
                            safeParseInt(order.deposit_balance_before) > 0,
                    },
                    {
                        'text-red-600':
                            0 > safeParseInt(order.deposit_balance_before),
                    },
                ]"
                v-if="safeParseInt(order.deposit_balance_before)"
            >
                <span class="font-bold">🌿 SALDO DEPOSIT:</span
                ><span class="pr-1 font-bold"
                    >{{
                        0 > safeParseInt(order.deposit_balance_before)
                            ? "-"
                            : ""
                    }}
                    Rp{{
                        Math.abs(
                            safeParseInt(order.deposit_balance_before)
                        ).toLocaleString("id")
                    }}</span
                >
            </p>
            <p
                class="flex justify-between"
                v-if="safeParseInt(order.amount_deposit_used)"
            >
                <span class="font-bold">🧾 TOTAL NOTA:</span
                ><span class="pr-1 font-bold"
                    >Rp{{
                        safeParseInt(order.total).toLocaleString("id")
                    }}</span
                >
            </p>
            <p
                :class="[
                    'flex justify-between mt-1',
                    {
                        'text-green-600':
                            safeParseInt(order.amount_deposit_used) > 0,
                    },
                    {
                        'text-red-600':
                            0 > safeParseInt(order.amount_deposit_used),
                    },
                ]"
                v-if="safeParseInt(order.amount_deposit_used)"
            >
                <span class="font-bold"
                    >🌿
                    {{
                        safeParseInt(order.amount_deposit_used) > 0
                            ? "DEPOSIT DIPAKAI"
                            : ""
                    }}{{
                        0 > safeParseInt(order.amount_deposit_used)
                            ? "KURANG BAYAR"
                            : ""
                    }}:</span
                ><span class="pr-1 font-bold"
                    >{{ safeParseInt(order.amount_deposit_used) > 0 ? "-" : ""
                    }}{{
                        0 > safeParseInt(order.amount_deposit_used) ? "+" : ""
                    }}
                    Rp{{
                        Math.abs(
                            safeParseInt(order.amount_deposit_used)
                        ).toLocaleString("id")
                    }}</span
                >
            </p>
            <hr class="mt-1.5 mb-1.5" />
            <p
                v-if="order.deposit_job"
                class="flex justify-between mt-1 text-green-600"
            >
                <span class="font-bold">💵 DEPOSIT BARU:</span
                ><span class="pr-1 font-bold"
                    >Rp{{
                        order.deposit_job !== null
                            ? safeParseInt(order.deposit_job).toLocaleString(
                                  "id"
                              )
                            : safeParseInt(order.total).toLocaleString("id")
                    }}</span
                >
            </p>
            <hr class="mt-1 mb-3" />
            <p
                v-if="order.products.length > 0"
                class="flex justify-between mt-1"
            >
                <span class="font-bold"
                    >💰 TAGIHAN
                    <span v-if="order.payment" class="font-bold uppercase"
                        >({{
                            order.payment == "transfer" ? "TF" : order.payment
                        }})</span
                    >:
                </span>
                <span class="pr-1 font-bold"
                    >Rp{{
                        order.total_after_deposit !== null
                            ? safeParseInt(
                                  order.total_after_deposit
                              ).toLocaleString("id")
                            : safeParseInt(order.total).toLocaleString("id")
                    }}</span
                >
            </p>
            <template v-if="safeParseInt(order.amount_will_pay) && false">
                <p class="">
                    💵 <span class="font-bold">BAYAR:</span> Rp{{
                        safeParseInt(order.amount_will_pay).toLocaleString("id")
                    }}
                </p>
            </template>
            <order-return
                v-if="order.products.length > 0"
                :roleId="roleId"
                :orderId="order.id"
                :transferAmount="safeParseInt(order.amount_pay)"
                :cashAmount="safeParseInt(order.amount_split_to_cash)"
                :payAmount="safeParseInt(order.amount_will_pay)"
                :returnAmount="safeParseInt(order.amount_return)"
                :totalAfterDepositAmount="
                    order.total_after_deposit !== null
                        ? safeParseInt(order.total_after_deposit)
                        : safeParseInt(order.total)
                "
                :depositAmount="safeParseInt(order.total_deposit)"
                :orderStatusId="safeParseInt(order.status_id)"
                :paymentMethodAsk="order.payment_method_ask"
            />
            <template v-if="order.payment_method_ask">
                <p class="mt-1 font-bold text-gray-500 uppercase">
                    🛵 ({{ order.payment_method_ask }})<span
                        class="lowercase"
                        v-if="order.payment_note"
                        >: {{ order.payment_note }}</span
                    >
                </p>
            </template>
            <template
                v-if="
                    order.bank &&
                    (order.payment_method_confirmed === 'transfer' ||
                        order.payment_method_confirmed === 'qris')
                "
            >
                <p class="mt-1 font-bold text-blue-700 uppercase">
                    {{
                        order.payment_method_confirmed === "qris" ? "🔠" : "🏧"
                    }}
                    {{ order.bank.bank_name }} -
                    {{ order.bank.account_number }} -
                    {{ order.bank.holder_name }}
                </p>
            </template>
            <template v-else-if="order.payment_method_confirmed === 'invoice'">
                <p class="mt-1 font-bold text-blue-700 uppercase">
                    🧾 (AR - Account Receivable)
                </p>
            </template>
            <p
                class="text-sm font-bold text-blue-600"
                v-if="order.confirm_note"
            >
                {{ order.confirm_note }}
            </p>
            <p v-if="order.confirmedby" class="text-xs font-bold text-gray-400">
                Confirmed by: {{ order.confirmedby.name }}
            </p>
            <template v-else-if="order.payment_method_confirmed === 'cash'">
                <p class="mt-1 font-bold text-blue-700 uppercase">✅ (CASH)</p>
            </template>
            <template v-if="order.receive_photo_url">
                <div
                    class="flex items-center h-8 mt-1 font-bold text-gray-500 uppercase"
                >
                    <p class="whitespace-nowrap">📸</p>
                    <div
                        class="h-8 w-full ml-1.5 rounded overflow-hidden flex justify-center items-center relative shadow border-gray-500 border"
                    >
                        <img
                            :src="order.receive_photo_url"
                            data-zoomable
                            :id="'order_img_zoom_' + order.id"
                            class="object-cover w-full h-auto pointer-events-auto"
                        />
                        <span
                            class="absolute flex items-center justify-center w-full h-full text-white text-shadow-xl"
                            >LIHAT FOTO PENERIMAAN</span
                        >
                    </div>
                </div>
            </template>
            <template v-if="order.payment_proof_url">
                <div
                    class="flex items-center mt-1 font-bold text-gray-500 uppercase"
                >
                    <p class="whitespace-nowrap">🧾</p>
                    <div class="flex w-full h-8">
                        <div
                            class="w-full ml-1.5 rounded overflow-hidden flex justify-center items-center relative shadow border-gray-500 border h-8"
                        >
                            <img
                                :src="order.payment_proof_url"
                                data-zoomable
                                :id="'order_img_zoom_' + order.id"
                                class="object-cover w-full h-auto pointer-events-auto"
                            />
                            <span
                                class="absolute flex items-center justify-center w-full h-full text-white text-shadow-xl"
                                >LIHAT BUKTI TRANSFER</span
                            >
                        </div>
                        <button
                            :data-order_id="order.id"
                            type="button"
                            class="_js-btn-upload-paymentproof pointer-events-auto w-10 ml-1.5 rounded overflow-hidden flex justify-center items-center relative shadow border-gray-500 border bg-green-700 h-8"
                        >
                            <span
                                class="absolute flex items-center justify-center w-full h-full font-bold text-white text-shadow-xl"
                                >⬆️</span
                            >
                        </button>
                    </div>
                </div>
            </template>
            <template
                v-if="
                    !(
                        safeParseInt(order.status_id) == 1 &&
                        [5].includes(safeParseInt(roleId))
                    ) &&
                    (!order.payment_method_ask ||
                        order.payment_method_ask === 'non-cash' ||
                        order.deposit_job > 0) &&
                    !order.payment_proof_url
                "
            >
                <div
                    class="flex items-center mt-1 font-bold text-gray-500 uppercase"
                >
                    <p class="whitespace-nowrap">⬆️</p>
                    <button
                        :data-order_id="order.id"
                        type="button"
                        class="relative flex items-center justify-center w-full h-8 ml-2 overflow-hidden font-bold text-white bg-green-700 border border-gray-500 rounded shadow pointer-events-auto _js-btn-upload-paymentproof text-shadow-xl"
                    >
                        UPLOAD BUKTI TRANSFER
                    </button>
                </div>
            </template>
            <template
                v-if="
                    dateNow === moment().format('YYYY-MM-DD') &&
                    (safeParseInt(roleId) <= 4 || safeParseInt(roleId) === 6) &&
                    safeParseInt(order.status_id) <= 3 &&
                    driversParsed
                "
            >
                <label class="block mt-3">
                    <select
                        :data-order_id="order.id"
                        :disabled="safeParseInt(userId) === 211"
                        class="relative block w-full mt-1 border-gray-300 rounded-md shadow-sm pointer-events-auto _js-input-driver focus:border-blue-300 focus:ring focus:ring-red-200 focus:ring-opacity-50"
                    >
                        <option value="">Pilih driver..</option>
                        <option
                            v-for="(
                                driver, indexDriver
                            ) in driversParsed.filter(
                                (d) =>
                                    !order.customer.delmanexcludes
                                        .map((de) => de.id)
                                        .includes(d.id)
                            )"
                            :key="driver.id"
                            :value="driver.id"
                            :selected="order.driver_id == driver.id"
                        >
                            {{ driver.name }}
                        </option>
                    </select>
                </label>
            </template>
            <template
                v-if="
                    dateNow === moment().format('YYYY-MM-DD') &&
                    !(
                        safeParseInt(order.status_id) == 1 &&
                        [5].includes(safeParseInt(roleId))
                    ) &&
                    (safeParseInt(roleId) <= 3 ||
                        [5, 6].includes(safeParseInt(roleId))) &&
                    safeParseInt(order.status_id) <= 3
                "
            >
                <hr class="mt-3 mb-2" />
                <order-finish
                    :createdAtHours="moment(order.created_at).format('HH')"
                    :createdAtMinutes="moment(order.created_at).format('mm')"
                    :orderId="order.id"
                    :roleId="safeParseInt(roleId)"
                    :userId="safeParseInt(userId)"
                    :driverId="safeParseInt(order.driver_id)"
                />
            </template>
            <template
                v-if="
                    (safeParseInt(roleId) <= 3 ||
                        ((safeParseInt(roleId) === 4 ||
                            safeParseInt(roleId) === 6) &&
                            order.payment_method_ask === 'cash')) &&
                    (safeParseInt(order.status_id) === 4 ||
                        safeParseInt(order.status_id) === 6) &&
                    ((order.payment_method_ask !== null &&
                        order.payment_method_ask !== '') ||
                        order.deposit_job !== 0)
                "
            >
                <order-confirm
                    :bankId="order.bank_id"
                    :banks="order.store.banks"
                    :orderPaymentMethodAsk="order.payment_method_ask"
                    :orderId="order.id"
                    :orderStatusId="order.status_id"
                    :roleId="roleId"
                />
            </template>
        </div>
        <span
            class="absolute index-10 -top-3 -right-2 opacity-40 text-xs font-bold text-gray-400"
            >{{ order.author ? order.author.name : "PWA" }}</span
        >
    </div>
</template>

<script>
import OrderReturn from "./OrderReturn";
import OrderFinish from "./OrderFinish";
import OrderConfirm from "./OrderConfirm";
import ThermalPrinterEncoder from "./thermal-printer-encoder.esm.js";
import ImageLogoTypeGasplus from "../../../images/logotype-mini.jpeg";

// function parseJson(str) {
//   try {
//     const value = JSON.parse(str);
//     return value;
//   } catch (e) {
//     return str;
//   }
// }
export default {
    components: {
        OrderReturn,
        OrderFinish,
        OrderConfirm,
    },
    props: {
        roleId: String | Number,
        userId: String | Number,
        baseUrl: String,
        dateNow: String,
        status: String,
        username: String,
        orderRaw: Object,
        driversParsed: Array,
    },
    data: function () {
        return {
            order: this.orderRaw,
            popupBatal: null,
            offlineData: null,
            images: null,
            deviceCache: null,
            serverCache: null,
            serviceCache: null,
            characteristicCacheA: null,
            characteristicCacheB: null,
            descriptorCache: null,
            color: {
                order: "bg-yellow-500",
                assigned: "bg-blue-500",
                progress: "bg-blue-500",
                completed: "bg-blue-700",
                canceled: "bg-red-500",
                paid: "bg-green-500",
            },
            label: {
                order: "bebas",
                assigned: "diambil",
                progress: "diambil",
                completed: "terkirim",
                canceled: "batal",
                paid: "selesai",
            },
        };
    },
    created() {
        // const gp_user_roleId = localStorage.getItem("gp_user_roleId");
        // const gp_base_url = localStorage.getItem("gp_base_url");
        // const gp_drivers = localStorage.getItem("gp_drivers");
        // const gp_current_orders = localStorage.getItem("gp_current_orders");
        // const gp_orders_finished = localStorage.getItem("gp_orders_finished");
        // const gp_orders_canceled = localStorage.getItem("gp_orders_canceled");
        // const gp_current_orders_parsed = gp_current_orders
        //   ? parseJson(parseJson(gp_current_orders))
        //   : [];
        // const gp_orders_finished_parsed = gp_orders_finished
        //   ? parseJson(parseJson(gp_orders_finished))
        //   : [];
        // const gp_orders_canceled_parsed = gp_orders_canceled
        //   ? parseJson(parseJson(gp_orders_canceled))
        //   : [];
        // this.offlineData = {
        //   roleId: gp_user_roleId ? this.safeParseInt(gp_user_roleId) : null,
        //   baseUrl: gp_base_url ? gp_base_url : null,
        //   drivers: gp_drivers ? parseJson(parseJson(gp_drivers)) : [],
        //   orders: gp_current_orders_parsed.filter((obj) => {
        //     const findFinished = gp_orders_finished_parsed.findIndex(
        //       (finished) =>
        //         this.safeParseInt(finished.order_id) === this.safeParseInt(obj.id)
        //     );
        //     const findCanceled = gp_orders_canceled_parsed.findIndex(
        //       (canceled) =>
        //         this.safeParseInt(canceled.order_id) === this.safeParseInt(obj.id)
        //     );
        //     return findFinished < 0 && findCanceled < 0;
        //   }),
        // };
    },
    mounted() {
        // console.log("phone", this.formatPhoneNumber(628112676455));
        this.images = mediumZoom("#order_img_zoom_" + this.order.id);
        this.popupBatal = tippy("._js-btn-popup-batal", {
            content(reference) {
                const id = reference.getAttribute("data-popup-batal");
                const template = document.getElementById(id);
                return template.innerHTML;
            },
            allowHTML: true,
            trigger: "click",
        });
        $("body").on("click", "._js-btn-close-popup-batal", () => {
            this.popupBatal.forEach((el) => {
                console.log("close tippy");
                el.hide();
            });
        });
        // ? Check PPOB status on mounted
        // this.order.products.forEach((product) => {
        //     if (
        //         product.pivot.ppob_status === "PROCESS" ||
        //         product.pivot.ppob_status === "FAILED"
        //     ) {
        //         console.log("🚀 ~ AUTO CHECK PPOB STATUS");
        //         this.onClickCheckPpobStatus(product);
        //     }
        // });
    },
    updated() {
        this.images.detach();
        this.images = mediumZoom("#order_img_zoom_" + this.order.id);
        if (this.popupBatal) {
            this.popupBatal.forEach((el) => {
                el.destroy();
            });
            this.popupBatal = tippy("._js-btn-popup-batal", {
                content(reference) {
                    const id = reference.getAttribute("data-popup-batal");
                    const template = document.getElementById(id);
                    return template.innerHTML;
                },
                allowHTML: true,
                trigger: "click",
            });
        }
    },
    // computed: {
    //   roleId: function () {
    //     return this.roleId ? this.roleId : this.offlineData.roleId;
    //   },
    //   baseUrl: function () {
    //     return this.base_url ? this.base_url : this.offlineData.baseUrl;
    //   },
    //   dateNow: function () {
    //     return this.date_now ? this.date_now : moment().format("YYYY-MM-DD");
    //   },
    //   ordersFiltered: function () {
    //     const orders = this.orders
    //       ? parseJson(this.orders)
    //       : this.offlineData.orders;

    //     const data = orders.filter((order) => {
    //       // console.log("this.filter_payment_method", this.filter_payment_method);
    //       // console.log(
    //       //   "order.payment_method_confirmed",
    //       //   order.payment_method_confirmed
    //       // );
    //       const word = this.search ? this.search.toLowerCase() : null;
    //       const customerName =
    //         order && order.customer && order.customer.name
    //           ? order.customer.name.toLowerCase()
    //           : null;
    //       const driverName =
    //         order && order.driver && order.driver.name
    //           ? order.driver.name.toLowerCase()
    //           : null;
    //       const passSearchCustomer = customerName
    //         ? customerName.includes(word)
    //         : false;
    //       const passSearchDriver = driverName ? driverName.includes(word) : false;
    //       const passSearchOrderCode = order.code.includes(
    //         this.search.toUpperCase()
    //       );

    //       const filterSearchWord = word
    //         ? passSearchCustomer || passSearchDriver || passSearchOrderCode
    //         : true;

    //       const filterSearch =
    //         word === ">1jam"
    //           ? this.timeDiff(order).hours > 0 ||
    //             this.timeDiff(order).minutes > 60
    //           : filterSearchWord;

    //       let passFilterPaymentMethod = true;
    //       if (this.status === "selesai") {
    //         const paymentMethod =
    //           order && order.payment_method_confirmed
    //             ? order.payment_method_confirmed
    //             : null;
    //         passFilterPaymentMethod =
    //           paymentMethod === this.filter_payment_method ? true : false;
    //       } else if (
    //         this.status === "bebas" ||
    //         this.status === "batal" ||
    //         this.status === "diambil"
    //       ) {
    //         const paymentMethod = order && order.payment ? order.payment : "cash";
    //         passFilterPaymentMethod =
    //           paymentMethod === this.filter_payment_method ? true : false;
    //       } else if (this.status === "terkirim" || this.status === "total") {
    //         const paymentMethod =
    //           order && order.payment_method_ask ? order.payment_method_ask : null;
    //         const pM = paymentMethod !== "cash" ? "non-tunai" : "cash";
    //         passFilterPaymentMethod =
    //           pM === this.filter_payment_method ? true : false;
    //       }

    //       const filterPayment = this.filter_payment_method
    //         ? passFilterPaymentMethod
    //         : true;

    //       const filterStatus =
    //         this.status === "total"
    //           ? this.safeParseInt(order.status_id) !== 5
    //           : true;

    //       return filterSearch && filterPayment && filterStatus;
    //     });

    //     console.log("data orders", data);
    //     return data;
    //   },
    //   driversParsed: function () {
    //     return this.drivers ? parseJson(this.drivers) : this.offlineData.drivers;
    //   },
    // },
    methods: {
        moment: moment,
        onClickCheckPpobStatus(product) {
            console.log("🚀 ~ product:", product);
            const refId = product.pivot.ppob_ref_id;
            let $spinner = this.$refs["ppob-status-spinner-" + refId];
            if (Array.isArray($spinner)) {
                $spinner = $spinner[0];
            }
            // console.log("🚀 ~ $spinner:", $spinner);
            if ($spinner) {
                $spinner.classList.add("animate-spin");
            }
            axios
                .get(`/iak/prepaid/check-status/${refId}`)
                .then((res) => {
                    console.log("res", res);
                    if (res?.data?.status === "success") {
                        Vue.set(product, "pivot", res?.data?.pivot);
                        Vue.set(
                            product,
                            "ppobresponsedata",
                            res?.data?.ppobresponsedata
                        );
                        mdtoast("UPDATE STATUS SUCCESS", {
                            duration: 1500,
                            type: mdtoast.SUCCESS,
                        });
                    } else {
                        mdtoast("UPDATE STATUS FAILED", {
                            duration: 1500,
                            type: mdtoast.ERROR,
                        });
                    }
                })
                .catch((err) => {
                    const errors = err?.response;
                    console.log("errors", errors);
                    mdtoast("UPDATE STATUS FAILED", {
                        duration: 1500,
                        type: mdtoast.ERROR,
                    });
                })
                .finally(() => {
                    if ($spinner) {
                        $spinner.classList.remove("animate-spin");
                    }
                });
        },
        formatPhoneNumber(phoneNumberString) {
            let phoneFormated = phoneNumberString.toString();
            let position = 5;
            if (phoneFormated.substr(0, 2) == "62") {
                phoneFormated = "0" + phoneFormated.substr(2);
                position = 4;
            }
            phoneFormated = [
                phoneFormated.slice(0, position),
                " ",
                phoneFormated.slice(position),
            ].join("");
            phoneFormated = [
                phoneFormated.slice(0, position + 4),
                " ",
                phoneFormated.slice(position + 4),
            ].join("");
            return phoneFormated;
        },
        async onClickGoToChat(phone, conversationId) {
            const order = this.order;
            let url = "";
            if ([1, 2, 3, "1", "2", "3"].indexOf(this.roleId) > -1) {
                if (conversationId) {
                    url = "socialchat://send?id=" + conversationId;
                    window.open(url, "_blank").focus();
                } else {
                    Swal.fire({
                        title: "Mencari percakapan...",
                        didOpen: async () => {
                            Swal.showLoading();
                            const res = await axios.get(
                                `/socialchat/get/conversation?customer_id=${order.customer.id}&store_id=${order.store.id}&phone=${phone}`
                            );
                            if (
                                res.status === 200 &&
                                res.data.conversation_id
                            ) {
                                Swal.close();
                                url =
                                    "socialchat://send?id=" +
                                    res.data.conversation_id;
                                window.open(url, "_blank").focus();
                            } else {
                                Swal.hideLoading();
                                Swal.fire({
                                    title: "Ups...",
                                    text: "Percakapan di Socialchat tidak ditemukan!",
                                    icon: "warning",
                                    showCancelButton: true,
                                    confirmButtonColor: "#25D366",
                                    confirmButtonText: "Buka WhatsApp",
                                    cancelButtonText: "Tutup",
                                }).then((result) => {
                                    if (result.isConfirmed) {
                                        url = "https://wa.me/" + phone;
                                        window.open(url, "_blank").focus();
                                    }
                                });
                            }
                            return true;
                        },
                        allowOutsideClick: () => !Swal.isLoading(),
                    });
                    // .then((result) => {
                    //     console.log("🚀 ~ result:", result);
                    //     if (result.isConfirmed) {
                    //         // Swal.fire({
                    //         //     title: `${result.value.login}'s avatar`,
                    //         //     imageUrl: result.value.avatar_url,
                    //         // });
                    //     }
                    // });
                    // Swal.fire({
                    //     title: "Ups...",
                    //     text: "Percakapan di Socialchat tidak ditemukan!",
                    //     icon: "warning",
                    //     showCancelButton: true,
                    //     confirmButtonColor: "#25D366",
                    //     confirmButtonText: "Buka WhatsApp",
                    //     cancelButtonText: "Tutup",
                    // }).then((result) => {
                    //     if (result.isConfirmed) {
                    //         url = "https://wa.me/" + phone;
                    //         window.open(url, "_blank").focus();
                    //     }
                    // });
                }
            } else {
                url = "https://wa.me/" + phone;
                window.open(url, "_blank").focus();
            }
        },
        async onClickCopyNotifText() {
            navigator.clipboard.writeText(
                this.order.notif_msg.replaceAll("<br>", "\n")
            );
            Swal.fire({
                title: "Text notif COPIED!",
                text: 'Silahkan "PASTE" text di Socialchat/WhatsApp.',
                icon: "success",
                confirmButtonColor: "#3085d6",
                // confirmButtonText: "Tutup",
            });
        },
        async onClickMarkAsSent(product, e) {
            if (confirm('Tandai juga "NOTIF TERKIRIM" ?')) {
                axios
                    .post(
                        `/orderproduct/${product.pivot.id}/mark-notif-as-sent`
                    )
                    .then((res) => {
                        console.log("res", res);
                        if (res?.data?.status === "success") {
                            // Vue.set(
                            //     product.pivot,
                            //     "notif_sent",
                            //     moment().format("YYYY-MM-DD HH:mm:ss")
                            // );
                            // Vue.set(
                            //     product.pivot,
                            //     "updated_at",
                            //     moment().format("YYYY-MM-DD HH:mm:ss")
                            // );
                            product.pivot.notif_sent = moment().format(
                                "YYYY-MM-DD HH:mm:ss"
                            );
                            product.pivot.updated_at = moment().format(
                                "YYYY-MM-DD HH:mm:ss"
                            );
                            mdtoast("NOTIF MARK AS SENT", {
                                duration: 1500,
                                type: mdtoast.SUCCESS,
                            });
                        } else {
                            mdtoast("FAILED", {
                                duration: 1500,
                                type: mdtoast.ERROR,
                            });
                        }
                    })
                    .catch((err) => {
                        const errors = err?.response;
                        console.log("errors", errors);
                        mdtoast("FAILED", {
                            duration: 1500,
                            type: mdtoast.ERROR,
                        });
                    });
            } else {
            }
        },
        async onClickCopyNotifPpob(product) {
            if (confirm('Tandai juga "NOTIF TERKIRIM" ?')) {
                axios
                    .post(
                        `/orderproduct/${product.pivot.id}/mark-notif-as-sent`
                    )
                    .then((res) => {
                        console.log("res", res);
                        if (res?.data?.status === "success") {
                            // Vue.set(
                            //     product.pivot,
                            //     "notif_sent",
                            //     moment().format("YYYY-MM-DD HH:mm:ss")
                            // );
                            // Vue.set(
                            //     product.pivot,
                            //     "updated_at",
                            //     moment().format("YYYY-MM-DD HH:mm:ss")
                            // );
                            product.pivot.notif_sent = moment().format(
                                "YYYY-MM-DD HH:mm:ss"
                            );
                            product.pivot.updated_at = moment().format(
                                "YYYY-MM-DD HH:mm:ss"
                            );
                            mdtoast("NOTIF MARK AS SENT", {
                                duration: 1500,
                                type: mdtoast.SUCCESS,
                            });
                        } else {
                            mdtoast("FAILED", {
                                duration: 1500,
                                type: mdtoast.ERROR,
                            });
                        }
                    })
                    .catch((err) => {
                        const errors = err?.response;
                        console.log("errors", errors);
                        mdtoast("FAILED", {
                            duration: 1500,
                            type: mdtoast.ERROR,
                        });
                    });
            }
            navigator.clipboard.writeText(
                product.msg_ppob.replaceAll("<br>", "\n")
            );
            Swal.fire({
                title: "Text notif COPIED!",
                text: 'Silahkan "PASTE" text di Socialchat/WhatsApp.',
                icon: "success",
                confirmButtonColor: "#3085d6",
                // confirmButtonText: "Tutup",
            });
        },
        async onClickCopyPpobToken(sn) {
            navigator.clipboard.writeText(`⚡️ TOKEN: ${sn.split("/")[0]}`);
            Swal.fire({
                title: "TOKEN COPIED!",
                text: 'Silahkan "PASTE" text di Socialchat/WhatsApp.',
                icon: "success",
                confirmButtonColor: "#3085d6",
                // confirmButtonText: "Tutup",
            });
        },
        async onClickPrint(e) {
            function writeStrToCharacteristic(characteristic, str) {
                let buffer = new ArrayBuffer(str.length);
                let dataView = new DataView(buffer);
                for (var i = 0; i < str.length; i++) {
                    dataView.setUint8(i, str.charAt(i).charCodeAt());
                }
                console.log("accessing the device");
                // return characteristic.writeValue(buffer);
            }

            const mainService = "000018f0-0000-1000-8000-00805f9b34fb";
            const characteristicUUID = "00002af1-0000-1000-8000-00805f9b34fb";

            this.deviceCache = await navigator.bluetooth.requestDevice({
                filters: [
                    {
                        services: ["000018f0-0000-1000-8000-00805f9b34fb"],
                    },
                ],
            });

            console.log("Connecting to GATT Server...");
            this.serverCache = await this.deviceCache.gatt.connect();

            console.log("Getting Services...");
            this.serviceCache = await this.serverCache.getPrimaryService(
                mainService
            );

            console.log("Getting Characteristics A...");
            this.characteristicCacheA =
                await this.serviceCache.getCharacteristic(characteristicUUID);

            const options = this.order.options;
            const isHideDeposit =
                typeof options === "object" &&
                !Array.isArray(options) &&
                options !== null &&
                "is_hide_deposit" in options
                    ? options.is_hide_deposit
                    : false;

            const encoder = new ThermalPrinterEncoder({
                language: "esc-pos",
                width: 32,
                wordWrap: true,
                imageMode: "raster",
            });

            // const logo = new Image();
            // // logo.src = "../../../images/logotype-mini.jpeg";
            // // logo.src = ImageLogoTypeGasplus;
            // logo.src =
            //     "https://www.bimmerworld.com/51147044207-Z4-Hood-Emblem-tn.jpg";
            // // console.log("🚀 ~ ImageLogoTypeGasplus:", ImageLogoTypeGasplus);
            // console.log("🚀 ~ logo:", logo);

            // const logoInfo = encoder
            //     .initialize()
            //     .newline()
            //     .newline()
            //     .align("center")
            //     .image(logo, 32, 16, "Gasplus")
            //     .newline()
            //     .newline()
            //     .newline()
            //     .encode();
            // await this.characteristicCacheA.writeValue(logoInfo);

            const headerInfo = encoder
                .initialize()
                .newline()
                .newline()
                .align("center")
                .bold(true)
                .height(2)
                .width(2)
                .line("GASPLUS")
                .bold(false)
                .height(1)
                .width(1)
                .size("small")
                .line(this.order.store.name)
                .line(
                    "WA: " + this.formatPhoneNumber(this.order.store.whatsapp_1)
                )
                .size("normal")
                .line("--------------------------------")
                .encode();

            await this.characteristicCacheA.writeValue(headerInfo);

            const customerInfo = encoder
                .initialize()
                // .size("small")
                .line(this.order.code + "-" + moment().format("DD/MM/YYYY"))
                .line(this.order.customer.name.substring(0, 100))
                // .line(this.order.address.address.substring(0, 100))
                // .size("normal")
                .line("--------------------------------")
                .encode();

            await this.characteristicCacheA.writeValue(customerInfo);

            if (this.order.products.length > 0) {
                const productHeaderInfo = encoder
                    .initialize()
                    .bold(true)
                    .table(
                        [
                            { width: 14, align: "left" },
                            { width: 16, marginLeft: 1, align: "right" },
                        ],
                        [["Nama Barang", "Harga"]]
                    )
                    .bold(false)
                    .line("--------------------------------")
                    .encode();

                await this.characteristicCacheA.writeValue(productHeaderInfo);
            }

            for (let index = 0; index < this.order.products.length; index++) {
                const product = this.order.products[index];

                const productInfo = encoder
                    .initialize()
                    // .line(`(${product.pivot.qty}x) ${product.name}`)
                    .line(product.name)
                    .table(
                        [
                            { width: 2, align: "left" },
                            { width: 14, marginLeft: 1, align: "left" },
                            { width: 14, marginLeft: 1, align: "right" },
                        ],
                        [
                            [
                                `${product.pivot.qty}`,
                                `x ${product.pivot.price.toLocaleString("id")}`,

                                (
                                    this.safeParseInt(product.pivot.qty) *
                                    this.safeParseInt(product.pivot.price)
                                ).toLocaleString("id"),
                            ],
                        ]
                    )
                    // .bold(false)
                    .encode();

                await this.characteristicCacheA.writeValue(productInfo);
            }

            for (
                let index = 0;
                index < this.order.additionalcosts.length;
                index++
            ) {
                const additionalcost = this.order.additionalcosts[index];
                const costInRupiah =
                    this.safeParseInt(additionalcost.total_cost) > 0
                        ? additionalcost.total_cost.toLocaleString("id")
                        : additionalcost.cost.toLocaleString("id");

                const additionalcostsInfo = encoder
                    .initialize()
                    // .size("small")
                    // .line(
                    //     `(+) ${additionalcost.name} @Rp${
                    //         this.safeParseInt(additionalcost.total_cost) > 0
                    //             ? additionalcost.total_cost.toLocaleString("id")
                    //             : additionalcost.cost.toLocaleString("id")
                    //     }`
                    // )
                    // .size("normal")
                    .line(additionalcost.name)
                    .table(
                        [
                            { width: 2, align: "left" },
                            { width: 14, marginLeft: 1, align: "left" },
                            { width: 14, marginLeft: 1, align: "right" },
                        ],
                        [["1", `x ${costInRupiah}`, `${costInRupiah}`]]
                    )
                    .encode();

                await this.characteristicCacheA.writeValue(additionalcostsInfo);
            }

            if (this.order.products.length > 0) {
                const productInfoClosing = encoder
                    .initialize()
                    .line("--------------------------------")
                    .encode();

                await this.characteristicCacheA.writeValue(productInfoClosing);
            }

            if (this.safeParseInt(this.order.deposit_job) > 0) {
                const topupDeposit = encoder
                    .initialize()
                    .bold(true)
                    .table(
                        [
                            { width: 14, align: "left" },
                            { width: 16, marginLeft: 1, align: "right" },
                        ],
                        [
                            [
                                "Topup Deposit",
                                `${this.order.deposit_job.toLocaleString(
                                    "id"
                                )}`,
                            ],
                        ]
                    )
                    .bold(false)
                    .line("--------------------------------")
                    .encode();

                await this.characteristicCacheA.writeValue(topupDeposit);

                // const saldoDeposit = encoder
                //     .initialize()
                //     .table(
                //         [
                //             { width: 14, align: "left" },
                //             { width: 16, marginLeft: 1, align: "right" },
                //         ],
                //         [
                //             [
                //                 "Saldo Deposit:",
                //                 `${this.order.customer.deposit_amount.toLocaleString(
                //                     "id"
                //                 )}`,
                //             ],
                //         ]
                //     )
                //     .bold(false)
                //     .encode();

                // await this.characteristicCacheA.writeValue(saldoDeposit);
            }

            if (
                !isHideDeposit &&
                this.safeParseInt(this.order.deposit_balance_before) > 0
            ) {
                const depositBalanceBeforeInfo = encoder
                    .initialize()
                    // .line(
                    // `Saldo Deposit: Rp${this.order.deposit_balance_before.toLocaleString(
                    //     "id"
                    // )}`
                    // )
                    .table(
                        [
                            { width: 18, align: "left" },
                            { width: 12, marginLeft: 1, align: "right" },
                        ],
                        [
                            [
                                "Saldo Deposit:",
                                `${this.order.deposit_balance_before.toLocaleString(
                                    "id"
                                )}`,
                            ],
                        ]
                    )
                    .encode();

                await this.characteristicCacheA.writeValue(
                    depositBalanceBeforeInfo
                );
            }

            const totalNotaInfo = encoder
                .initialize()
                // .line(`Total Nota: Rp${this.order.total.toLocaleString("id")}`)
                .table(
                    [
                        { width: 14, align: "left" },
                        { width: 16, marginLeft: 1, align: "right" },
                    ],
                    [
                        [
                            "Total Nota:",
                            `${this.order.total.toLocaleString("id")}`,
                        ],
                    ]
                )
                .encode();

            await this.characteristicCacheA.writeValue(totalNotaInfo);

            if (
                !isHideDeposit &&
                this.safeParseInt(this.order.amount_deposit_used) > 0
            ) {
                const depositUsedInfo = encoder
                    .initialize()
                    // .line(
                    //     `Deposit Terpakai: -Rp${this.order.amount_deposit_used.toLocaleString(
                    //         "id"
                    //     )}`
                    // )
                    .table(
                        [
                            { width: 18, align: "left" },
                            { width: 12, marginLeft: 1, align: "right" },
                        ],
                        [
                            [
                                "Deposit Terpakai:",
                                `-${this.order.amount_deposit_used.toLocaleString(
                                    "id"
                                )}`,
                            ],
                        ]
                    )
                    .encode();

                await this.characteristicCacheA.writeValue(depositUsedInfo);
            }

            if (
                !isHideDeposit &&
                this.safeParseInt(this.order.amount_deposit_used) < 0
            ) {
                const depositUsedInfo = encoder
                    .initialize()
                    // .line(
                    //     `Kekurangan Lalu: +Rp${this.order.amount_deposit_used.toLocaleString(
                    //         "id"
                    //     )}`
                    // )
                    .table(
                        [
                            { width: 18, align: "left" },
                            { width: 12, marginLeft: 1, align: "right" },
                        ],
                        [
                            [
                                "Kekurangan Lalu:",
                                `+${this.order.amount_deposit_used.toLocaleString(
                                    "id"
                                )}`,
                            ],
                        ]
                    )
                    .encode();

                await this.characteristicCacheA.writeValue(depositUsedInfo);
            }

            const totalDueInfo = encoder
                .initialize()
                .bold(true)
                // .line(
                //     `TOTAL TAGIHAN: Rp${this.order.total_after_deposit.toLocaleString(
                //         "id"
                //     )}`
                // )
                .table(
                    [
                        { width: 14, align: "left" },
                        { width: 16, marginLeft: 1, align: "right" },
                    ],
                    [
                        [
                            "TOTAL TAGIHAN:",
                            `${this.order.total_after_deposit.toLocaleString(
                                "id"
                            )}`,
                        ],
                    ]
                )
                .bold(false)
                .encode();

            await this.characteristicCacheA.writeValue(totalDueInfo);

            if (
                !isHideDeposit &&
                this.safeParseInt(this.order.deposit_balance_after) > 0
            ) {
                const depositBalanceAfterInfo = encoder
                    .initialize()
                    .line("--------------------------------")
                    // .line(
                    //     `Sisa Deposit: Rp${this.order.deposit_balance_after.toLocaleString(
                    //         "id"
                    //     )}`
                    // )
                    .table(
                        [
                            { width: 18, align: "left" },
                            { width: 12, marginLeft: 1, align: "right" },
                        ],
                        [
                            [
                                "Sisa Deposit:",
                                `${this.order.deposit_balance_after.toLocaleString(
                                    "id"
                                )}`,
                            ],
                        ]
                    )
                    .encode();

                await this.characteristicCacheA.writeValue(
                    depositBalanceAfterInfo
                );
            }

            if (
                !isHideDeposit &&
                this.safeParseInt(this.order.deposit_balance_after) < 0
            ) {
                const depositBalanceAfterInfo = encoder
                    .initialize()
                    .line("--------------------------------")
                    // .line(
                    //     `Kekurangan Bayar: Rp${this.order.deposit_balance_after.toLocaleString(
                    //         "id"
                    //     )}`
                    // )
                    .table(
                        [
                            { width: 18, align: "left" },
                            { width: 12, marginLeft: 1, align: "right" },
                        ],
                        [
                            [
                                "Kekurangan Bayar:",
                                `${this.order.deposit_balance_after.toLocaleString(
                                    "id"
                                )}`,
                            ],
                        ]
                    )
                    .encode();

                await this.characteristicCacheA.writeValue(
                    depositBalanceAfterInfo
                );
            }

            const footerInfo = encoder
                .initialize()
                .line("--------------------------------")
                .align("right")
                .size("small")
                // .italic(true)
                .line(
                    `${this.username}, ${moment().format("D MMM YYYY, HH:mm")}`
                )
                // .italic(false)
                .size("normal")
                .newline()
                .newline()
                .newline()
                .encode();

            await this.characteristicCacheA.writeValue(footerInfo);
        },
        toWa(number) {
            if (!number) return number;
            let num = number;
            num = num.replace(/[^0-9]/g, "");
            // num.replace(' ', '');
            // num.replace('-', '');
            // num.replace('+', '');
            // num.replace('(', '');
            // num.replace(')', '');
            if (num.substr(0, 1) == "0") {
                num = "62" + num.substr(1);
            } else if (num.substr(0, 1) == "8") {
                num = "62" + num;
            }
            return num;
        },
        secondsToHms: function (order) {
            if (!order.duration)
                return {
                    hours: 0,
                    minutes: 0,
                    seconds: 0,
                };
            let inp = Number(order.duration);
            const hh = Math.floor(inp / 60 / 60);
            inp -= hh * 60 * 60;
            const mm = Math.floor(inp / 60);
            inp -= mm * 60;
            const ss = Math.floor(inp);
            inp -= ss;
            const tm = {
                hours: hh,
                minutes: mm,
                seconds: ss,
            };
            // console.log("tm", tm);
            return tm;
        },
        timeDiff: function (order) {
            // const date1 = new Date(order.created_at); // 9:00 AM
            // const date2 = new Date(order.received_at); // 5:00 PM

            // // the following is to handle cases where the times are on the opposite side of
            // // midnight e.g. when you want to get the difference between 9:00 PM and 5:00 AM

            // if (date2 < date1) {
            //     date2.setDate(date2.getDate() + 1);
            // }

            // const diff = date2 - date1;
            // console.log("diff", diff);

            // let msec = diff;
            // const hh = Math.floor(msec / 1000 / 60 / 60);
            // msec -= hh * 1000 * 60 * 60;
            // const mm = Math.floor(msec / 1000 / 60);
            // msec -= mm * 1000 * 60;
            // const ss = Math.floor(msec / 1000);
            // msec -= ss * 1000;

            const start = moment(order.created_at);
            if (start.hours() < 7) {
                start.hours(7);
            }
            const end = moment(order.received_at);
            const diff = end.diff(start);

            // console.log("diff", diff);

            console.log("order.created_at", order.created_at);
            console.log("order.received_at", order.received_at);
            console.log("order.duration", order.duration);
            console.log("diff", diff);

            if (diff > 0) {
                let msec = diff;
                const hh = Math.floor(msec / 1000 / 60 / 60);
                msec -= hh * 1000 * 60 * 60;
                const mm = Math.floor(msec / 1000 / 60);
                msec -= mm * 1000 * 60;
                const ss = Math.floor(msec / 1000);
                msec -= ss * 1000;
                const tm = {
                    hours: hh,
                    minutes: mm,
                    seconds: ss,
                };
                console.log("tm", tm);
                return tm;
            } else {
                return {
                    hours: 0,
                    minutes: 0,
                    seconds: 0,
                };
            }
        },
    },
    // watch: {
    //   filter_payment_method: function (newValue, oldValue) {
    //     // console.log("watch");
    //     // console.log(oldValue);
    //     // console.log(newValue);
    //   },
    // },
};
</script>
