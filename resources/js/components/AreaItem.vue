<template>
    <div
        :class="[
            'text-black w-full whitespace-normal p-1 border-gray-300',
            { 'border-t': withBorder },
        ]"
    >
        <div class="mt-1 mx-0.5 mb-1">
            <span v-if="!option.isNew || isSelection">🏘 </span>
            <span v-if="option.isNew && !isSelection" class="font-bold"
                >+ Add New:
            </span>
            <span
                v-html="highlightKeyword(option.name, keyword, option.isNew)"
            />
        </div>
        <div v-if="option.latlng" class="mx-0.5 mb-1 text-xs text-gray-600">
            📍
            <span v-html="option.latlng" />
        </div>
    </div>
</template>

<script>
export default {
    props: {
        option: Object,
        keyword: String,
        withBorder: Boolean,
        isSelection: <PERSON><PERSON><PERSON>,
        // randomColorsWithContrast: Array,
    },
    // data: function () {
    //     return {
    //         randomColorsWithContrast: [
    //             "#000000",
    //             "#dc2626",
    //             "#d97706",
    //             "#65a30d",
    //             "#059669",
    //             "#0891b2",
    //             "#2563eb",
    //             "#7c3aed",
    //             "#be123c",
    //             "#450a0a",
    //             "#713f12",
    //             "#365314",
    //             "#164e63",
    //             "#4c1d95",
    //             "#701a75",
    //             "#881337",
    //             "#020617",
    //         ],
    //     };
    // },
    methods: {
        highlightKeyword(words, keyword, isNew = false) {
            if (!keyword || isNew) {
                return words;
            }

            const escapedKeyword = keyword.replace(
                /[-\/\\^$*+?.()|[\]{}]/g,
                "\\$&"
            );
            const regex = new RegExp(`(${escapedKeyword})`, "gi");

            return words.replace(
                regex,
                '<span class="bg-yellow-200">$1</span>'
            );
        },
    },
};
</script>
