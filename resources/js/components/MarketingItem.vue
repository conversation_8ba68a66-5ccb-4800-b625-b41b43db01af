<template>
    <div class="relative">
        <!-- <a
      target="_blank"
      :href="
        safeParseInt(roleId) <= 3
          ? baseUrl + '/cs/form/marketing/' + order.id
          : ''
      "
      :class="[
        'absolute inset-0',
        safeParseInt(roleId) <= 3 ? '' : 'pointer-events-none',
      ]"
    ></a> -->
        <a
            target="_blank"
            href="#"
            :class="[
                'absolute inset-0',
                'pointer-events-none',
                // safeParseInt(roleId) <= 3 ? '' : 'pointer-events-none',
            ]"
        ></a>
        <div class="relative pointer-events-none index-10">
            <h4 class="text-lg font-bold">
                {{ order.code
                }}<span class="ml-1 text-xs text-gray-400"
                    >🕐
                    {{
                        order.photos && order.photos.length > 0
                            ? moment(order.photos[0].created_at).format("HH:mm")
                            : moment(order.created_at).format("HH:mm")
                    }}</span
                >
                <template v-if="order.area && order.received_at">
                    <template
                        v-if="
                            Number(order.duration) > 5400 ||
                            Number(order.duration) < -5400
                        "
                    >
                        <span class="ml-1 text-xs text-red-500"
                            >🔴
                            {{
                                secondsToHms(order).hours > 0
                                    ? secondsToHms(order).hours + ":"
                                    : ""
                            }}{{ secondsToHms(order).minutes }}</span
                        >
                    </template>
                    <template v-else>
                        <span class="ml-1 text-xs text-green-500"
                            >🟢 {{ secondsToHms(order).minutes }}</span
                        >
                    </template>
                </template>
                <template v-if="safeParseInt(order.distance_store_area)">
                    <span class="ml-1 text-xs text-gray-500">{{
                        safeParseInt(order.distance_store_area)
                    }}</span>
                </template>
                <template v-if="safeParseInt(order.distance_area_received)">
                    <span class="text-xs text-gray-800"
                        >/
                        {{ safeParseInt(order.distance_area_received) }}</span
                    >
                </template>
            </h4>
            <p class="flex">
                <span
                    v-if="order.area"
                    class="text-xs text-gray-500 font-bold mb-1 mr-2.5"
                >
                    🔁 Checkin: {{ order.area.count }}
                </span>
                <template v-if="order.photos && order.photos.length > 0">
                    <span class="mb-1 text-xs font-bold text-gray-500">
                        📸 Foto: {{ order.photos.length }}
                    </span>
                </template>
            </p>
            <p
                :class="[
                    'px-2 py-0.5 mb-1 text-xs font-bold border-2 rounded inline-block bg-transparent uppercase',
                    '_js-status-item-' + order.id,
                    color[order.status.code],
                ]"
            >
                JOB
                <span :class="['_js-status-text-' + order.id]">{{
                    label[order.status.code]
                }}</span>
            </p>
            <template v-if="order.driver"
                ><p
                    :class="[
                        'px-2 py-0.5 mb-1 ml-0.5 text-xs border-2 border-blue-500 text-blue-500 bg-transparent font-bold rounded inline-block',
                        '_js-driver-item-' + order.id,
                    ]"
                >
                    🛵
                    {{ order.driver.name }}
                </p>
            </template>
            {{ safeParseInt(order.is_offline) ? "📵OFFLINE" : "" }}
            <template v-if="order.driver_note">
                <p
                    :class="[
                        'font-bold',
                        safeParseInt(order.status_id) == 5
                            ? 'text-red-600'
                            : '',
                        safeParseInt(order.status_id) != 5
                            ? 'text-blue-600'
                            : '',
                    ]"
                >
                    ⚠️ {{ order.driver_note }}
                    <template v-if="order.cancelby">
                        <span class="ml-1 text-sm text-gray-300"
                            >(by {{ order.cancelby.name }})</span
                        >
                    </template>
                </p>
            </template>
            <div v-if="order.area" class="my-1">
                <a
                    :href="
                        safeParseInt(roleId) <= 3
                            ? baseUrl + '/cs/form/area/' + order.area.id
                            : ''
                    "
                    target="_blank"
                    :class="[
                        safeParseInt(roleId) <= 3
                            ? 'pointer-events-auto rounded border-2 border-yellow-500 text-yellow-500 bg-transparent font-bold px-2 py-0.5'
                            : '',
                    ]"
                    >🏘 {{ order.area.name }}</a
                >
            </div>
            <template v-if="order.area && order.area.latlng">
                <a
                    :href="
                        order.area
                            ? 'http://www.google.com/maps/place/' +
                              order.area.latlng
                            : ''
                    "
                    target="_blank"
                    class="relative inline-block mt-1 text-yellow-600 underline pointer-events-auto"
                    >📍 {{ order.area ? order.area.latlng : "" }}</a
                >
            </template>
            <template v-if="order.received_latlng">
                <a
                    :href="
                        order.received_latlng
                            ? 'http://www.google.com/maps/place/' +
                              order.received_latlng
                            : ''
                    "
                    target="_blank"
                    class="relative inline-block mt-1 text-yellow-600 underline pointer-events-auto"
                    >📍
                    {{ order.received_latlng ? order.received_latlng : "" }}</a
                >
            </template>
            <!-- <template v-if="order.note">
        <p class="font-bold text-red-500">
          📝 {{ order.note }}
          <span class="text-sm opacity-60">(by Pelanggan)</span>
        </p>
      </template> -->
            <template v-if="order.note_for_driver">
                <p class="font-bold text-blue-500">
                    🛵 {{ order.note_for_driver }}
                    <span class="text-sm opacity-60">(for Delman)</span>
                </p>
            </template>
            <!-- <hr class="my-2" /> -->

            <template
                v-if="
                    dateNow === moment().format('YYYY-MM-DD') &&
                    (safeParseInt(roleId) <= 4 || safeParseInt(roleId) === 6) &&
                    safeParseInt(order.status_id) <= 3 &&
                    driversParsed
                "
            >
                <label class="block mt-3">
                    <select
                        :data-marketing_id="order.id"
                        class="relative block w-full mt-1 border-gray-300 rounded-md shadow-sm pointer-events-auto _js-input-driver-marketing focus:border-blue-300 focus:ring focus:ring-red-200 focus:ring-opacity-50"
                    >
                        <!-- <option value="">Pilih driver..</option> -->
                        <option
                            v-for="(driver, index) in driversParsed"
                            :key="driver.id"
                            :value="driver.id"
                            :selected="order.driver_id == driver.id"
                        >
                            {{ driver.name }}
                        </option>
                    </select>
                </label>
            </template>
            <div
                class="mt-3"
                v-for="(photo, index) in order.photos"
                :key="photo.id"
            >
                <div
                    class="flex items-center h-8 mt-1 font-bold text-gray-500 uppercase"
                >
                    <p class="whitespace-nowrap">📸</p>
                    <div
                        class="h-8 w-full ml-1.5 rounded overflow-hidden flex justify-center items-center relative shadowborder-gray-500 border"
                    >
                        <img
                            :src="photo.custom_properties.url"
                            data-zoomable
                            :id="'marketing_img_zoom_' + order.id"
                            class="object-cover w-full h-auto pointer-events-auto"
                        />
                        <span
                            class="absolute pointer-events-none flex items-center justify-center w-full h-full text-white text-shadow-xl"
                            >LIHAT FOTO</span
                        >
                    </div>
                </div>
            </div>
            <template
                v-if="
                    dateNow === moment().format('YYYY-MM-DD') &&
                    !(
                        safeParseInt(order.status_id) == 1 &&
                        [5, 6].includes(safeParseInt(roleId))
                    ) &&
                    (safeParseInt(roleId) <= 3 ||
                        [5, 6].includes(safeParseInt(roleId))) &&
                    safeParseInt(order.status_id) <= 3
                "
            >
                <hr class="mt-3 mb-2" />
                <div class="flex justify-around mt-3 _js-wrp-action">
                    <button
                        type="button"
                        :data-marketing_id="order.id"
                        :class="[
                            '_js-btn-checkin-marketing pointer-events-auto relative text-white bg-blue-700 py-2 shadow-lg px-2 rounded font-bold flex-1 mr-2',
                            // payment == '' ? 'opacity-50' : '',
                        ]"
                    >
                        CHECKIN
                    </button>
                    <button
                        type="button"
                        :data-marketing_id="order.id"
                        :class="[
                            '_js-btn-batal-marketing pointer-events-auto relative text-white bg-red-500 py-2 shadow-lg px-2 rounded font-bold flex-1 ml-2',
                        ]"
                    >
                        BATAL
                    </button>
                </div>
            </template>
        </div>
        <span
            class="absolute index-10 top-1 right-1.5 opacity-40 text-xs font-bold text-gray-400"
            >{{ order.author ? order.author.name : "PWA" }}</span
        >
    </div>
</template>

<script>
// import MarketingFinish from "./MarketingFinish";

// function parseJson(str) {
//   try {
//     const value = JSON.parse(str);
//     return value;
//   } catch (e) {
//     return str;
//   }
// }
export default {
    // components: {
    //   MarketingFinish,
    // },
    props: {
        roleId: String | Number,
        baseUrl: String,
        dateNow: String,
        order: Object,
        driversParsed: Array,
    },
    data: function () {
        return {
            popupBatal: null,
            offlineData: null,
            images: null,
            color: {
                order: "border-yellow-500 text-yellow-500",
                assigned: "border-blue-500 text-blue-500",
                progress: "border-blue-500 text-blue-500",
                completed: "border-blue-700 text-blue-700",
                canceled: "border-red-500 text-red-500",
                paid: "border-green-500 text-green-500",
            },
            label: {
                order: "bebas",
                assigned: "diambil",
                progress: "diambil",
                completed: "terkirim",
                canceled: "batal",
                paid: "selesai",
            },
        };
    },
    created() {
        // const gp_user_roleId = localStorage.getItem("gp_user_roleId");
        // const gp_base_url = localStorage.getItem("gp_base_url");
        // const gp_drivers = localStorage.getItem("gp_drivers");
        // const gp_current_orders = localStorage.getItem("gp_current_orders");
        // const gp_orders_finished = localStorage.getItem("gp_orders_finished");
        // const gp_orders_canceled = localStorage.getItem("gp_orders_canceled");
        // const gp_current_orders_parsed = gp_current_orders
        //   ? parseJson(parseJson(gp_current_orders))
        //   : [];
        // const gp_orders_finished_parsed = gp_orders_finished
        //   ? parseJson(parseJson(gp_orders_finished))
        //   : [];
        // const gp_orders_canceled_parsed = gp_orders_canceled
        //   ? parseJson(parseJson(gp_orders_canceled))
        //   : [];
        // this.offlineData = {
        //   roleId: gp_user_roleId ? this.safeParseInt(gp_user_roleId) : null,
        //   baseUrl: gp_base_url ? gp_base_url : null,
        //   drivers: gp_drivers ? parseJson(parseJson(gp_drivers)) : [],
        //   orders: gp_current_orders_parsed.filter((obj) => {
        //     const findFinished = gp_orders_finished_parsed.findIndex(
        //       (finished) =>
        //         this.safeParseInt(finished.order_id) === this.safeParseInt(obj.id)
        //     );
        //     const findCanceled = gp_orders_canceled_parsed.findIndex(
        //       (canceled) =>
        //         this.safeParseInt(canceled.order_id) === this.safeParseInt(obj.id)
        //     );
        //     return findFinished < 0 && findCanceled < 0;
        //   }),
        // };
    },
    mounted() {
        // console.log("Example component mounted");
        this.images = mediumZoom("#marketing_img_zoom_" + this.order.id);
        // this.popupBatal = tippy("._js-btn-popup-batal", {
        //   content(reference) {
        //     const id = reference.getAttribute("data-popup-batal");
        //     const template = document.getElementById(id);
        //     return template.innerHTML;
        //   },
        //   allowHTML: true,
        //   trigger: "click",
        // });
        // $("body").on("click", "._js-btn-close-popup-batal", () => {
        //   this.popupBatal.forEach((el) => {
        //     console.log("close tippy");
        //     el.hide();
        //   });
        // });
    },
    updated() {
        this.images.detach();
        this.images = mediumZoom("#marketing_img_zoom_" + this.order.id);
        // if (this.popupBatal) {
        //   this.popupBatal.forEach((el) => {
        //     el.destroy();
        //   });
        //   this.popupBatal = tippy("._js-btn-popup-batal", {
        //     content(reference) {
        //       const id = reference.getAttribute("data-popup-batal");
        //       const template = document.getElementById(id);
        //       return template.innerHTML;
        //     },
        //     allowHTML: true,
        //     trigger: "click",
        //   });
        // }
    },
    // computed: {
    //   roleId: function () {
    //     return this.roleId ? this.roleId : this.offlineData.roleId;
    //   },
    //   baseUrl: function () {
    //     return this.base_url ? this.base_url : this.offlineData.baseUrl;
    //   },
    //   dateNow: function () {
    //     return this.date_now ? this.date_now : moment().format("YYYY-MM-DD");
    //   },
    //   ordersFiltered: function () {
    //     const orders = this.orders
    //       ? parseJson(this.orders)
    //       : this.offlineData.orders;

    //     const data = orders.filter((order) => {
    //       // console.log("this.filter_payment_method", this.filter_payment_method);
    //       // console.log(
    //       //   "order.payment_method_confirmed",
    //       //   order.payment_method_confirmed
    //       // );
    //       const word = this.search ? this.search.toLowerCase() : null;
    //       const customerName =
    //         order && order.customer && order.customer.name
    //           ? order.customer.name.toLowerCase()
    //           : null;
    //       const driverName =
    //         order && order.driver && order.driver.name
    //           ? order.driver.name.toLowerCase()
    //           : null;
    //       const passSearchCustomer = customerName
    //         ? customerName.includes(word)
    //         : false;
    //       const passSearchDriver = driverName ? driverName.includes(word) : false;
    //       const passSearchOrderCode = order.code.includes(
    //         this.search.toUpperCase()
    //       );

    //       const filterSearchWord = word
    //         ? passSearchCustomer || passSearchDriver || passSearchOrderCode
    //         : true;

    //       const filterSearch =
    //         word === ">1jam"
    //           ? this.timeDiff(order).hours > 0 ||
    //             this.timeDiff(order).minutes > 60
    //           : filterSearchWord;

    //       let passFilterPaymentMethod = true;
    //       if (this.status === "selesai") {
    //         const paymentMethod =
    //           order && order.payment_method_confirmed
    //             ? order.payment_method_confirmed
    //             : null;
    //         passFilterPaymentMethod =
    //           paymentMethod === this.filter_payment_method ? true : false;
    //       } else if (
    //         this.status === "bebas" ||
    //         this.status === "batal" ||
    //         this.status === "diambil"
    //       ) {
    //         const paymentMethod = order && order.payment ? order.payment : "cash";
    //         passFilterPaymentMethod =
    //           paymentMethod === this.filter_payment_method ? true : false;
    //       } else if (this.status === "terkirim" || this.status === "total") {
    //         const paymentMethod =
    //           order && order.payment_method_ask ? order.payment_method_ask : null;
    //         const pM = paymentMethod !== "cash" ? "non-tunai" : "cash";
    //         passFilterPaymentMethod =
    //           pM === this.filter_payment_method ? true : false;
    //       }

    //       const filterPayment = this.filter_payment_method
    //         ? passFilterPaymentMethod
    //         : true;

    //       const filterStatus =
    //         this.status === "total"
    //           ? this.safeParseInt(order.status_id) !== 5
    //           : true;

    //       return filterSearch && filterPayment && filterStatus;
    //     });

    //     console.log("data orders", data);
    //     return data;
    //   },
    //   driversParsed: function () {
    //     return this.drivers ? parseJson(this.drivers) : this.offlineData.drivers;
    //   },
    // },
    methods: {
        moment: moment,
        toWa(number) {
            if (!number) return number;
            let num = number;
            num = num.replace(/[^0-9]/g, "");
            // num.replace(' ', '');
            // num.replace('-', '');
            // num.replace('+', '');
            // num.replace('(', '');
            // num.replace(')', '');
            if (num.substr(0, 1) == "0") {
                num = "62" + num.substr(1);
            } else if (num.substr(0, 1) == "8") {
                num = "62" + num;
            }
            return num;
        },
        secondsToHms: function (order) {
            if (!order.duration)
                return {
                    hours: 0,
                    minutes: 0,
                    seconds: 0,
                };
            let inp = Number(order.duration);
            const hh = Math.floor(inp / 60 / 60);
            inp -= hh * 60 * 60;
            const mm = Math.floor(inp / 60);
            inp -= mm * 60;
            const ss = Math.floor(inp);
            inp -= ss;
            const tm = {
                hours: hh,
                minutes: mm,
                seconds: ss,
            };
            // console.log("tm", tm);
            return tm;
        },
        timeDiff: function (order) {
            // const date1 = new Date(order.created_at); // 9:00 AM
            // const date2 = new Date(order.received_at); // 5:00 PM

            // // the following is to handle cases where the times are on the opposite side of
            // // midnight e.g. when you want to get the difference between 9:00 PM and 5:00 AM

            // if (date2 < date1) {
            //     date2.setDate(date2.getDate() + 1);
            // }

            // const diff = date2 - date1;
            // console.log("diff", diff);

            // let msec = diff;
            // const hh = Math.floor(msec / 1000 / 60 / 60);
            // msec -= hh * 1000 * 60 * 60;
            // const mm = Math.floor(msec / 1000 / 60);
            // msec -= mm * 1000 * 60;
            // const ss = Math.floor(msec / 1000);
            // msec -= ss * 1000;

            const start =
                order.photos && order.photos > 0
                    ? moment(order.photos[0].custom_properties.created)
                    : moment(order.created_at);
            if (start.hours() < 7) {
                start.hours(7);
            }
            const end = moment(order.received_at);
            const diff = Math.abs(end.diff(start));

            console.log("start", start.format("LLL"));
            console.log("end", end.format("LLL"));
            console.log("diff", diff);

            if (diff > 0) {
                let msec = diff;
                const hh = Math.floor(msec / 1000 / 60 / 60);
                msec -= hh * 1000 * 60 * 60;
                const mm = Math.floor(msec / 1000 / 60);
                msec -= mm * 1000 * 60;
                const ss = Math.floor(msec / 1000);
                msec -= ss * 1000;
                return {
                    hours: hh,
                    minutes: mm,
                    seconds: ss,
                };
            } else {
                return {
                    hours: 0,
                    minutes: 0,
                    seconds: 0,
                };
            }
        },
    },
    // watch: {
    //   filter_payment_method: function (newValue, oldValue) {
    //     // console.log("watch");
    //     // console.log(oldValue);
    //     // console.log(newValue);
    //   },
    // },
};
</script>
