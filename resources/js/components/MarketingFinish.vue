<template>
  <div>
    <div class="flex justify-around mt-3 _js-wrp-action">
      <button
        type="button"
        :data-order_id="orderId"
        :disabled="!payment"
        :class="[
          '_js-btn-selesai pointer-events-auto relative text-white bg-blue-700 py-2 shadow-lg px-2 rounded font-bold flex-1 mr-2',
          payment == '' ? 'opacity-50' : '',
        ]"
      >
        SELESAI
      </button>
      <button
        type="button"
        :data-popup-batal="'popup-batal-' + orderId"
        :class="[
          '_js-btn-popup-batal pointer-events-auto relative text-white bg-red-500 py-2 shadow-lg px-2 rounded font-bold flex-1 ml-2',
        ]"
      >
        BATAL
      </button>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    orderId: Number,
  },
  data: function () {
    return {
      payment: "",
    };
  },
  methods: {
    safeParseInt(val) {
      const parsed = parseInt(val);
      if (isNaN(parsed)) {
        return 0;
      }
      return parsed;
    },
  },
  watch: {
    payment: function (newValue, oldValue) {
      this.customCash = 0;
    },
  },
};
</script>
