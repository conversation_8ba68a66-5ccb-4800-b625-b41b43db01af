<template>
    <div class="px-3 pb-20 pt-32 flex flex-col gap-3">
        <template v-if="!stockopname">
            <p class="text-center text-blue-500 font-semibold my-6 text-base">
                <PERSON>um ada laporan...
            </p>
            <button
                type="button"
                :disabled="isLoadingCreate"
                @click="onClickCreateStockopname"
                :class="[
                    'px-4 py-3 rounded-md font-bold text-base shadow-xl text-white uppercase tracking-widest focus:outline-none disabled:opacity-25 transition ease-in-out duration-150 relative text-center justify-center flex gap-2 items-center w-full bg-blue-600',
                    {
                        'opacity-50 cursor-not-allowed': isLoadingCreate,
                    },
                ]"
            >
                <svg
                    v-if="isLoadingCreate"
                    class="animate-spin h-6 w-6 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                >
                    <circle
                        class="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        stroke-width="4"
                    ></circle>
                    <path
                        class="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                </svg>
                <svg
                    v-if="!isLoadingCreate"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="4"
                    stroke="currentColor"
                    class="inline-block w-6 h-6"
                >
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M12 4.5v15m7.5-7.5h-15"
                    /></svg
                >Buat Laporan
            </button>
        </template>
        <div v-if="stockopname" class="text-xs flex flex-col">
            <div class="fixed z-50 top-0 left-0 right-0">
                <div class="max-w-xl mx-auto relative">
                    <button
                        @click="onClickRefreshData"
                        type="button"
                        class="flex flex-col items-center font-bold text-xs py-0.5 px-1 uppercase rounded-md text-blue-500 bg-yellow-400 absolute z-40 top-1 right-1 gap-0.5"
                    >
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 16 16"
                            fill="currentColor"
                            class="w-6 h-6"
                        >
                            <path
                                fill-rule="evenodd"
                                d="M13.836 2.477a.75.75 0 0 1 .75.75v3.182a.75.75 0 0 1-.75.75h-3.182a.75.75 0 0 1 0-1.5h1.37l-.84-.841a4.5 4.5 0 0 0-7.08.932.75.75 0 0 1-1.3-.75 6 6 0 0 1 9.44-1.242l.842.84V3.227a.75.75 0 0 1 .75-.75Zm-.911 7.5A.75.75 0 0 1 13.199 11a6 6 0 0 1-9.44 1.241l-.84-.84v1.371a.75.75 0 0 1-1.5 0V9.591a.75.75 0 0 1 .75-.75H5.35a.75.75 0 0 1 0 1.5H3.98l.841.841a4.5 4.5 0 0 0 7.08-.932.75.75 0 0 1 1.025-.273Z"
                                clip-rule="evenodd"
                            />
                        </svg>

                        Refresh
                    </button>
                    <div
                        v-if="
                            loadingPercentage >= 0 && loadingPercentage <= 100
                        "
                        class="absolute top-0 left-0 right-0 z-50 flex items-center h-1"
                    >
                        <div
                            class="bg-blue-500 h-full relative transform-gpu transition-all"
                            :style="{ width: loadingPercentage + '%' }"
                        >
                            <span
                                class="absolute right-0 top-1 font-bold text-xs text-blue-600 z-50"
                                >{{ loadingPercentage }}%</span
                            >
                        </div>
                    </div>
                </div>
            </div>
            <div
                class="rounded-lg relative bg-blue-50 border border-blue-200 flex flex-col py-2 px-3 gap-1"
            >
                <div
                    v-if="isLoadingRefresh"
                    class="absolute bg-white bg-opacity-50 inset-0 z-10 flex items-center justify-center text-sm font-bold text-blue-500"
                >
                    <svg
                        class="animate-spin h-4 w-4 mr-1.5 inline-block"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                    >
                        <circle
                            class="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            stroke-width="4"
                        ></circle>
                        <path
                            class="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                    </svg>
                    Loading...
                </div>
                <!-- <div
                    class="font-bold text-sm flex border-b border-blue-500 text-blue-600"
                >
                    Total FISIK:
                    <span class="ml-auto">
                        <span class="text-red-600"
                            >🔻{{
                                stockopname.total_products_less
                                    ? stockopname.total_products_less
                                    : "0"
                            }}
                            | 🔺{{
                                stockopname.total_products_over
                                    ? stockopname.total_products_over
                                    : "0"
                            }}</span
                        >
                        | ✅{{
                            stockopname.total_products_equal
                                ? stockopname.total_products_equal
                                : "100"
                        }}%</span
                    >
                </div> -->
                <div
                    class="font-bold flex border-b border-blue-500 text-blue-900"
                >
                    SPD ACC:
                    <span class="ml-auto text-sm"
                        >Rp{{
                            stockopname.total_spd_acc
                                ? stockopname.total_spd_acc.toLocaleString("id")
                                : "0"
                        }}
                        ({{
                            stockopname.qty_spd_acc
                                ? stockopname.qty_spd_acc.toLocaleString("id")
                                : "0"
                        }})</span
                    >
                </div>
                <div
                    class="font-bold flex border-b border-blue-500 text-blue-900"
                >
                    SPD GPA:
                    <span class="ml-auto text-sm"
                        >Rp{{
                            stockopname.total_spd_gpa
                                ? stockopname.total_spd_gpa.toLocaleString("id")
                                : "0"
                        }}
                        ({{
                            stockopname.qty_spd_gpa
                                ? stockopname.qty_spd_gpa.toLocaleString("id")
                                : "0"
                        }})</span
                    >
                </div>
                <a
                    :href="`${base_url}/manager/job-list/${store_slug}?date=${datenow}&ltt=spd_deposit`"
                    target="_blank"
                    class="font-bold flex border-b border-blue-500 text-blue-900"
                >
                    <span class="underline">SPD DEPOSIT ↗</span>
                    <span class="ml-auto text-sm"
                        >Rp{{
                            stockopname.total_spd_deposit
                                ? stockopname.total_spd_deposit.toLocaleString(
                                      "id"
                                  )
                                : "0"
                        }}
                        ({{
                            stockopname.qty_spd_deposit
                                ? stockopname.qty_spd_deposit.toLocaleString(
                                      "id"
                                  )
                                : "0"
                        }})</span
                    >
                </a>
                <div
                    class="font-bold flex border-b border-blue-500 text-blue-900"
                >
                    STD:
                    <span class="ml-auto text-sm"
                        >{{
                            stockopname.total_std
                                ? stockopname.total_std.toLocaleString("id")
                                : "0"
                        }}
                        ({{
                            stockopname.avg_std
                                ? stockopname.avg_std.toLocaleString("id")
                                : "0"
                        }})</span
                    >
                </div>
                <a
                    :href="`${base_url}/manager/job-list/${store_slug}?date=${datenow}&ltt=std_cpr`"
                    target="_blank"
                    class="font-bold flex border-b border-blue-500 text-blue-900"
                >
                    <span class="underline">STD CPR ↗</span>
                    <span class="ml-auto text-sm"
                        >{{
                            stockopname.total_std_cpr
                                ? stockopname.total_std_cpr.toLocaleString("id")
                                : "0"
                        }}
                        ({{
                            stockopname.avg_std_cpr
                                ? stockopname.avg_std_cpr.toLocaleString("id")
                                : "0"
                        }})</span
                    >
                </a>
                <div
                    class="font-bold flex border-b border-blue-500 text-blue-900"
                >
                    APC:
                    <span class="ml-auto text-sm"
                        >Rp{{
                            stockopname.total_apc
                                ? stockopname.total_apc.toLocaleString("id")
                                : "0"
                        }}
                        (Rp{{
                            stockopname.avg_apc
                                ? stockopname.avg_apc.toLocaleString("id")
                                : "0"
                        }})</span
                    >
                </div>

                <!-- <div
                    class="font-bold flex border-b border-green-500 text-green-900"
                >
                    TOTAL INCOME: <span class="ml-auto text-sm">Rp123.000</span>
                </div> -->
                <!-- <div
                    class="font-bold flex border-b border-green-500 text-green-900"
                >
                    INVOICE: <span class="ml-auto text-sm">Rp123.000</span>
                </div> -->
                <!-- <div
                    class="font-bold flex border-b border-green-500 text-green-900"
                >
                    DEPOSIT: <span class="ml-auto text-sm">Rp123.000</span>
                </div>
                <div
                    class="font-bold flex border-b border-green-500 text-green-900"
                >
                    NON-CASH IN: <span class="ml-auto text-sm">Rp123.000</span>
                </div> -->
                <div
                    class="font-bold flex border-b border-green-500 text-green-900"
                >
                    NON TUNAI:
                    <span class="ml-auto text-sm"
                        >Rp{{
                            stockopname.total_noncash_in
                                ? stockopname.total_noncash_in.toLocaleString(
                                      "id"
                                  )
                                : "0"
                        }}</span
                    >
                </div>
                <div
                    class="font-bold flex border-b border-green-500 text-green-900"
                >
                    PENGELUARAN:
                    <span class="ml-auto text-sm"
                        >-Rp{{
                            stockopname.total_cost
                                ? stockopname.total_cost.toLocaleString("id")
                                : "0"
                        }}</span
                    >
                </div>
                <a
                    :href="`${base_url}/manager/job-list/${store_slug}?date=${datenow}&search=${splitCashOrders
                        .map((o) => o.code)
                        .join(',')}`"
                    v-if="splitCashOrders.length > 0"
                    class="flex flex-col font-bold gap-0.5 px-1 py-0.5 -mx-1 rounded bg-blue-200"
                >
                    <h5 class="underline text-blue-800 uppercase text-sm flex">
                        Split Cash ↗
                        <span class="ml-auto capitalize"
                            >(Rp{{
                                splitCashOrders
                                    .reduce(
                                        (accum, item) =>
                                            accum + item.amount_split_to_cash,
                                        0
                                    )
                                    .toLocaleString("id")
                            }})</span
                        >
                    </h5>
                    <p
                        v-for="(order, index) in splitCashOrders"
                        class="flex text-blue-700"
                    >
                        {{ order.code }} / {{ order.customer.name }}
                        <span class="ml-auto"
                            >Rp{{
                                order.amount_split_to_cash.toLocaleString("id")
                            }}</span
                        >
                    </p>
                </a>
                <div
                    class="font-bold flex text-sm text-white bg-green-600 -mx-1 px-1 rounded"
                >
                    SETOR:
                    <span class="ml-auto"
                        >Rp{{
                            stockopname.total_cash_to_deposit
                                ? stockopname.total_cash_to_deposit.toLocaleString(
                                      "id"
                                  )
                                : "0"
                        }}</span
                    >
                </div>
            </div>
            <div class="flex flex-col gap-1 mt-2 relative">
                <div
                    v-if="isLoadingRefresh"
                    class="absolute bg-white bg-opacity-50 inset-0 z-10 flex items-center justify-center text-sm font-bold text-blue-300"
                >
                    <svg
                        class="animate-spin h-4 w-4 mr-1.5 inline-block"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                    >
                        <circle
                            class="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            stroke-width="4"
                        ></circle>
                        <path
                            class="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                    </svg>
                    Loading...
                </div>
                <div
                    class="font-bold flex px-1 py-0.5 rounded text-white bg-gray-600"
                >
                    UPDATED:
                    <span class="ml-auto"
                        >{{
                            moment(stockopname.updated_at).format(
                                "D MMM YY - HH:mm"
                            )
                        }}
                        / by {{ stockopname.editor.name }}</span
                    >
                </div>
                <div
                    v-if="stockopname.submittedby"
                    class="font-bold flex px-1 py-0.5 rounded text-white bg-blue-600"
                >
                    SUBMITTED:
                    <span class="ml-auto"
                        >{{
                            moment(stockopname.submitted_at).format(
                                "D MMM YY - HH:mm"
                            )
                        }}
                        / by {{ stockopname.submittedby.name }}</span
                    >
                </div>
                <div
                    v-if="stockopname.confirmedby"
                    class="font-bold flex px-1 py-0.5 rounded text-white bg-green-600"
                >
                    CONFIRMED:
                    <span class="ml-auto"
                        >{{
                            moment(stockopname.confirmed_at).format(
                                "D MMM YY - HH:mm"
                            )
                        }}
                        / by {{ stockopname.confirmedby.name }}</span
                    >
                </div>
            </div>

            <!-- PENGELUARAN -->
            <h3 class="mt-6 text-lg font-bold">List Pengeluaran Toko</h3>
            <table class="-ml-1">
                <thead
                    class="sticky z-30 bg-red-200 text-red-900"
                    style="top: 114px"
                >
                    <tr>
                        <th class="text-center py-1.5 px-1">No</th>
                        <th class="text-left py-1.5 px-1 w-full">Catatan</th>
                        <th class="text-left py-1.5 px-1">Biaya</th>
                        <th class="text-right py-1.5 px-1"></th>
                    </tr>
                </thead>
                <tbody>
                    <tr
                        class="border-b border-gray-200"
                        v-for="(cost, index) in stockopname.costs.filter(
                            (c) => c.note !== '__delete_this_row__'
                        )"
                    >
                        <td class="py-1 px-1 text-center">{{ index + 1 }}</td>
                        <td class="py-1 px-1 w-full">
                            <input
                                type="text"
                                class="text-xs rounded px-1.5 py-1 w-full border-gray-300"
                                v-model="cost.note"
                            />
                        </td>
                        <td
                            class="py-1 px-1 flex gap-0.5 items-center font-bold"
                        >
                            Rp
                            <money
                                placeholder="0"
                                class="text-xs rounded text-right ml-0.5 font-bold px-1.5 py-1 w-28 border-gray-300"
                                v-bind="money"
                                v-model="cost.cost"
                            ></money>
                        </td>
                        <td class="py-0 px-0">
                            <button
                                @click="onClickRemoveCost(index)"
                                type="button"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    viewBox="0 0 24 24"
                                    fill="currentColor"
                                    class="w-5 h-5 text-gray-400 mt-1"
                                >
                                    <path
                                        fill-rule="evenodd"
                                        d="M16.5 4.478v.227a48.816 48.816 0 0 1 3.878.512.75.75 0 1 1-.256 1.478l-.209-.035-1.005 13.07a3 3 0 0 1-2.991 2.77H8.084a3 3 0 0 1-2.991-2.77L4.087 6.66l-.209.035a.75.75 0 0 1-.256-1.478A48.567 48.567 0 0 1 7.5 4.705v-.227c0-1.564 1.213-2.9 2.816-2.951a52.662 52.662 0 0 1 3.369 0c1.603.051 2.815 1.387 2.815 2.951Zm-6.136-1.452a51.196 51.196 0 0 1 3.273 0C14.39 3.05 15 3.684 15 4.478v.113a49.488 49.488 0 0 0-6 0v-.113c0-.794.609-1.428 1.364-1.452Zm-.355 5.945a.75.75 0 1 0-1.5.058l.347 9a.75.75 0 1 0 1.499-.058l-.346-9Zm5.48.058a.75.75 0 1 0-1.498-.058l-.347 9a.75.75 0 0 0 1.5.058l.345-9Z"
                                        clip-rule="evenodd"
                                    />
                                </svg>
                            </button>
                        </td>
                    </tr>

                    <tr class="">
                        <td colspan="4" class="py-1 px-1">
                            <button
                                @click="onClickAddCost"
                                type="button"
                                class="flex justify-center items-center rounded font-bold text-center w-full text-red-600 p-2"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="4"
                                    stroke="currentColor"
                                    class="inline-block w-3 h-3 mr-1"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="M12 4.5v15m7.5-7.5h-15"
                                    />
                                </svg>
                                TAMBAH PENGELUARAN
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>

            <!-- PRDOUCT -->
            <h3 class="mt-6 text-lg font-bold flex items-center">
                List Produk
                <span
                    v-if="loadingPercentage >= 0 && loadingPercentage <= 100"
                    class="ml-auto text-blue-500 text-xs flex items-center"
                >
                    <svg
                        class="animate-spin h-3 w-3 mr-1 inline-block"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                    >
                        <circle
                            class="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            stroke-width="4"
                        ></circle>
                        <path
                            class="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                    </svg>
                    SYNCING</span
                >
                <span
                    v-if="loadingPercentage === -1"
                    class="ml-auto text-green-600 text-xs"
                    >✅ SYNCED</span
                >
            </h3>
            <table class="-ml-1">
                <thead
                    class="sticky z-30 bg-yellow-200 text-yellow-900"
                    style="top: 114px"
                >
                    <tr>
                        <th class="text-left py-1.5 px-1" style="width: 100px">
                            Produk
                        </th>
                        <th class="text-right py-1.5 px-1">Awal</th>
                        <th class="text-right py-1.5 px-1">Masuk</th>
                        <th class="text-right py-1.5 px-1">Terjual</th>
                        <th class="text-right py-1.5 px-1">Akhir</th>
                        <th class="text-right py-1.5 px-1 w-11">FISIK</th>
                        <th class="text-right py-1.5 px-1"></th>
                    </tr>
                </thead>
                <tbody>
                    <template
                        v-for="(category, index) in Object.keys(
                            productsGrouped
                        )"
                    >
                        <tr
                            class="border-b border-gray-200 sticky z-30"
                            style="top: 142px"
                        >
                            <td
                                colspan="7"
                                class="py-0.5 uppercase px-1 font-bold bg-yellow-100 text-yellow-600"
                            >
                                {{ category }}
                            </td>
                        </tr>

                        <tr
                            v-for="(product, subIndex) in productsGrouped[
                                category
                            ]"
                            :class="[
                                'border-b border-gray-200',
                                {
                                    'font-bold text-blue-500 bg-blue-50':
                                        product.id === 'total',
                                },
                            ]"
                        >
                            <td class="py-1 px-1 break-words">
                                {{ product.product_code }}
                            </td>
                            <td
                                v-if="
                                    product.is_loading &&
                                    loadingPercentage >= 0 &&
                                    loadingPercentage <= 100
                                "
                                class="py-1.5 px-1 text-sm font-bold text-blue-500"
                                colspan="6"
                            >
                                <svg
                                    class="animate-spin h-4 w-4 mr-1.5 inline-block"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                >
                                    <circle
                                        class="opacity-25"
                                        cx="12"
                                        cy="12"
                                        r="10"
                                        stroke="currentColor"
                                        stroke-width="4"
                                    ></circle>
                                    <path
                                        class="opacity-75"
                                        fill="currentColor"
                                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                    ></path>
                                </svg>
                                Loading...
                            </td>
                            <td
                                v-if="!product.is_loading && product.error"
                                class="py-1 px-1 font-semibold text-xs text-red-600"
                                colspan="6"
                            >
                                {{ product.error }}
                            </td>
                            <template
                                v-if="
                                    !product.is_loading &&
                                    product.last_synced_at &&
                                    !product.error &&
                                    product.start_balance !== null
                                "
                            >
                                <td class="py-1 px-1 text-right">
                                    {{ product.start_balance }}
                                </td>
                                <td class="py-1 px-1 text-right">
                                    {{ product.quantity_in }}
                                </td>
                                <td class="py-1 px-1 text-right">
                                    {{ product.quantity_out }}
                                </td>
                                <td class="py-1 px-1 text-right">
                                    {{ product.last_balance }}
                                </td>
                                <td class="py-1 px-0.5 text-right w-12">
                                    <money
                                        :disabled="product.id === 'total'"
                                        :placeholder="product.last_balance"
                                        :class="[
                                            'text-xs rounded px-0.5 py-0.5 w-full text-right border-gray-300 font-bold',
                                            {
                                                'text-green-600 bg-green-50':
                                                    product.last_balance ===
                                                    product.quantity_check,
                                            },
                                            {
                                                'text-red-600 bg-red-50':
                                                    product.last_balance !==
                                                    product.quantity_check,
                                            },
                                        ]"
                                        v-bind="money"
                                        v-model="product.quantity_check"
                                    ></money>
                                </td>
                                <td
                                    class="py-1 pl-1 flex items-center justify-center"
                                >
                                    <input
                                        v-if="product.id !== 'total'"
                                        type="checkbox"
                                        class="mr-0.5"
                                        v-model="product.is_checked"
                                    />
                                    <!-- <button
                                        v-if="product.id !== 'total'"
                                        @click="onClickMinFisik(product.id)"
                                        type="button"
                                    >
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            viewBox="0 0 24 24"
                                            fill="currentColor"
                                            class="w-6 h-6 text-red-700"
                                        >
                                            <path
                                                fill-rule="evenodd"
                                                d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm3 10.5a.75.75 0 0 0 0-1.5H9a.75.75 0 0 0 0 1.5h6Z"
                                                clip-rule="evenodd"
                                            />
                                        </svg>
                                    </button>
                                    <button
                                        v-if="product.id !== 'total'"
                                        @click="onClickPlusFisik(product.id)"
                                        type="button"
                                    >
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            viewBox="0 0 24 24"
                                            fill="currentColor"
                                            class="w-6 h-6 text-blue-500"
                                        >
                                            <path
                                                fill-rule="evenodd"
                                                d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25ZM12.75 9a.75.75 0 0 0-1.5 0v2.25H9a.75.75 0 0 0 0 1.5h2.25V15a.75.75 0 0 0 1.5 0v-2.25H15a.75.75 0 0 0 0-1.5h-2.25V9Z"
                                                clip-rule="evenodd"
                                            />
                                        </svg>
                                    </button> -->
                                </td>
                            </template>
                        </tr>
                        <!-- <tr class="border-b border-gray-200">
                        <td colspan="7" class="py-1 px-0.5">
                            <input
                                type="text"
                                class="text-sm rounded text-right px-1.5 py-1 w-full border-yellow-600 text-yellow-700 font-semibold"
                                value=""
                                placeholder="...catatan"
                            />
                        </td>
                    </tr> -->
                    </template>
                </tbody>
            </table>

            <div class="flex flex-col gap-3 mt-10">
                <button
                    @click="onClickSaveDraft"
                    type="button"
                    :disabled="
                        isLoadingSubmit ||
                        stockopname.submitted_at ||
                        (loadingPercentage >= 0 && loadingPercentage <= 100)
                    "
                    :class="[
                        'px-4 py-3 rounded-md font-bold text-base shadow-xl text-white uppercase tracking-widest focus:outline-none disabled:opacity-25 transition ease-in-out duration-150 relative text-center justify-center flex flex-col items-center w-full bg-gray-700',
                        {
                            'opacity-50 cursor-not-allowed':
                                isLoadingSubmit ||
                                stockopname.submitted_at ||
                                (loadingPercentage >= 0 &&
                                    loadingPercentage <= 100),
                        },
                    ]"
                >
                    {{
                        isLoadingSubmit === "draft" ? "SAVING..." : "SAVE DRAFT"
                    }}
                    <span class="text-xs opacity-50"
                        >{{
                            moment(stockopname.updated_at).format(
                                "D MMM YY - HH:mm"
                            )
                        }}` by {{ stockopname.editor.name }}</span
                    >
                </button>
                <button
                    @click="onClickSaveSubmit"
                    type="button"
                    :disabled="
                        currentUser.id !== stockopname.assigned_to ||
                        isLoadingSubmit ||
                        (loadingPercentage >= 0 && loadingPercentage <= 100)
                    "
                    :class="[
                        'px-4 py-3 rounded-md font-bold text-base shadow-xl text-white uppercase tracking-widest focus:outline-none disabled:opacity-25 transition ease-in-out duration-150 relative text-center justify-center flex flex-col items-center w-full bg-blue-700',
                        {
                            'opacity-50 cursor-not-allowed':
                                currentUser.id !== stockopname.assigned_to ||
                                isLoadingSubmit ||
                                (loadingPercentage >= 0 &&
                                    loadingPercentage <= 100),
                        },
                    ]"
                >
                    {{
                        isLoadingSubmit === "submit"
                            ? "SUBMITTING..."
                            : "SUBMIT"
                    }}<span
                        v-if="stockopname.submitted_at"
                        class="text-xs opacity-50"
                        >{{
                            moment(stockopname.submitted_at).format(
                                "D MMM YY - HH:mm"
                            )
                        }}
                        by {{ stockopname.submittedby.name }}</span
                    >
                    <span v-else class="text-xs opacity-50"
                        >Only by {{ stockopname.assignedto.name }}</span
                    >
                </button>
                <button
                    @click="onClickSaveConfirm"
                    v-if="currentUser.role_id <= 2"
                    :disabled="
                        isLoadingSubmit ||
                        !stockopname.submitted_at ||
                        (loadingPercentage >= 0 && loadingPercentage <= 100)
                    "
                    type="button"
                    :class="[
                        'px-4 py-3 rounded-md font-bold text-base shadow-xl text-white uppercase tracking-widest focus:outline-none disabled:opacity-25 transition ease-in-out duration-150 relative text-center justify-center flex flex-col items-center w-full bg-green-600',
                        {
                            'opacity-50 cursor-not-allowed':
                                isLoadingSubmit ||
                                !stockopname.submitted_at ||
                                (loadingPercentage >= 0 &&
                                    loadingPercentage <= 100),
                        },
                    ]"
                >
                    {{
                        isLoadingSubmit === "confirm"
                            ? "CONFIRMING..."
                            : "CONFIRM"
                    }}<span
                        v-if="stockopname.confirmed_at"
                        class="text-xs opacity-50"
                        >{{
                            moment(stockopname.confirmed_at).format(
                                "D MMM YY - HH:mm"
                            )
                        }}
                        by {{ stockopname.confirmedby.name }}</span
                    >
                </button>
            </div>
        </div>
    </div>
</template>

<script>
// import OrderItem from "./OrderItem";

function parseJson(str) {
    try {
        const value = JSON.parse(str);
        return value;
    } catch (e) {
        return str;
    }
}
import { Money } from "v-money";
export default {
    components: { Money },
    props: {
        datenow: String,
        store_slug: String,
        currentstoreraw: String,
        currentuserraw: String,
        productsraw: String,
        splitcashordersraw: String,
        stockopnameraw: String,
        base_url: String,
    },
    data: function () {
        return {
            money: {
                decimal: ",",
                thousands: ".",
                // prefix: "Rp",
                prefix: "",
                suffix: "",
                precision: 0,
                masked: false /* doesn't work with directive */,
            },
            productsList: [],
            currentStore: null,
            currentUser: null,
            splitCashOrders: [],
            loadingPercentage: -1,
            isLoadingCreate: false,
            isLoadingSubmit: false,
            isLoadingRefresh: false,
            stockopname: null,
        };
    },
    created() {
        this.currentStore = parseJson(this.currentstoreraw);
        this.currentUser = parseJson(this.currentuserraw);
        // console.log("🚀 ~ currentuser", this.currentuserraw);
        this.stockopname = parseJson(this.stockopnameraw);
        // console.log(
        //     "🚀 ~ this.stockopnameraw:",
        //     parseJson(this.stockopnameraw)
        // );
        this.productsList = parseJson(this.productsraw);
        this.splitCashOrders = parseJson(this.splitcashordersraw);
        // console.log("🚀 ~ splitCashOrders:", this.splitCashOrders);
        // const productsGroupedLocal = Object.groupBy(
        //     products,
        //     ({ categories }) => {
        //         return categories &&
        //             Array.isArray(categories) &&
        //             categories.length > 0
        //             ? categories[0].name
        //             : "Lain-lain";
        //     }
        // );
        // Object.keys(productsGroupedLocal).forEach((category) => {
        //     productsGroupedLocal[category].forEach((product, index) => {
        //         productsGroupedLocal[category][index]["summary"] = {
        //             start_balance: null,
        //             last_balance: null,
        //             quantity_in: null,
        //             quantity_out: null,
        //             balance_diff: null,
        //         };
        //     });
        // });
        // this.productsGrouped = productsGroupedLocal;
        // const ini = this;
        // Object.keys(this.productsGrouped).forEach((category) => {
        //     this.productsGrouped[category].forEach((product, index) => {
        //         console.log("🚀 ~ product:", product);
        //         ini.productsGrouped[category][index]["summary"] = "test";
        //     });
        // });
    },
    mounted() {
        $("._js-loading").hide();
        this.upsertAccStockProducts();
    },
    updated() {
        // this.images.detach();
        // this.images = mediumZoom("[data-zoomable]");
        // if (this.popupBatal) {
        //   this.popupBatal.forEach((el) => {
        //     el.destroy();
        //   });
        //   this.popupBatal = tippy("._js-btn-popup-batal", {
        //     content(reference) {
        //       const id = reference.getAttribute("data-popup-batal");
        //       const template = document.getElementById(id);
        //       return template.innerHTML;
        //     },
        //     allowHTML: true,
        //     trigger: "click",
        //   });
        // }
    },
    computed: {
        productsGrouped: function () {
            const productListOfStore = this.productsList;
            let productList = this.stockopname.products;
            productList = productList
                .filter((p) => {
                    return productListOfStore
                        ? productListOfStore.findIndex((plos) => {
                              return plos.id === p.product_id;
                          }) > -1
                        : true;
                })
                .map((p) => {
                    p.quantity_check = p.quantity_check ?? 0;
                    return p;
                });
            productList = productList.reduce((acc, currentValue) => {
                let groupKey = "Lain-lain";
                if (
                    currentValue.product.categories &&
                    currentValue.product.categories instanceof Array &&
                    currentValue.product.categories.length > 0
                ) {
                    groupKey = currentValue.product.categories[0].name;
                }
                if (!acc[groupKey]) {
                    acc[groupKey] = [];
                }
                acc[groupKey].push(currentValue);
                return acc;
            }, {});

            let { Galon, Karton, LPG, ...others } = productList;

            const listOrderOfGalon = [
                "IA",
                "IV",
                "IPS",
                "IP",
                "IC",
                "LM15",
                "T16",
                "GA",
                "GV",
                "GPS",
                "GP",
                "GC",
                "TGP",
            ];
            const listOrderOfKarton = [
                "KA220",
                "KAC220",
                "KA330",
                "KA600",
                "KA1500",
                "KV200",
                "KV330",
                "KV550",
                "KV1500",
                "KP120_40",
                "KP120_48",
                "KP200",
                "KP330",
                "KP500",
                "KP1500",
                "KPS400",
                "KPS600",
                "KPS1500",
            ];
            const listOrderOfLPG = [
                "IGB12",
                "IBG12",
                "IBG5",
                "TGB12",
                "TBG12",
                "TBG5",
            ];

            const rowTotal = {
                id: "total",
                product_code: "TOTAL",
                start_balance: 0,
                quantity_in: 0,
                quantity_out: 0,
                last_balance: 0,
                quantity_check: 0,
                last_synced_at: "now",
            };
            let totals = {
                start_balance: 0,
                quantity_in: 0,
                quantity_out: 0,
                last_balance: 0,
                quantity_check: 0,
            };

            // ? Galon
            Galon = Galon.sort((a, b) => {
                const indexA = listOrderOfGalon.indexOf(a.product_code);
                const indexB = listOrderOfGalon.indexOf(b.product_code);
                if (indexA === -1) return 1;
                if (indexB === -1) return -1;
                return indexA - indexB;
            });
            const indexT16 = Galon.map((e) => e.product_code).indexOf("T16");
            if (indexT16 > -1) {
                Galon.splice(indexT16 + 1, 0, rowTotal);
            } else {
                const indexLM15 = Galon.map((e) => e.product_code).indexOf(
                    "LM15"
                );
                if (indexLM15 > -1) {
                    Galon.splice(indexLM15 + 1, 0, rowTotal);
                } else {
                    const indexIP = Galon.map((e) => e.product_code).indexOf(
                        "IP"
                    );
                    if (indexIP > -1) {
                        Galon.splice(indexIP + 1, 0, rowTotal);
                    }
                }
            }

            const indexGC = Galon.map((e) => e.product_code).indexOf("GC");
            if (indexGC > -1) {
                Galon.splice(indexGC + 1, 0, rowTotal);
            } else {
                const indexGP = Galon.map((e) => e.product_code).indexOf("GP");
                if (indexGP > -1) {
                    Galon.splice(indexGP + 1, 0, rowTotal);
                } else {
                    const indexGPS = Galon.map((e) => e.product_code).indexOf(
                        "GPS"
                    );
                    if (indexGPS > -1) {
                        Galon.splice(indexGPS + 1, 0, rowTotal);
                    }
                }
            }
            for (let index = 0; index < Galon.length; index++) {
                const item = Galon[index];
                if (item.id === "total") {
                    Galon[index] = {
                        ...Galon[index],
                        start_balance: totals.start_balance,
                        quantity_in: totals.quantity_in,
                        quantity_out: totals.quantity_out,
                        last_balance: totals.last_balance,
                        quantity_check: totals.quantity_check,
                    };
                    totals = {
                        start_balance: 0,
                        quantity_in: 0,
                        quantity_out: 0,
                        last_balance: 0,
                        quantity_check: 0,
                    };
                    continue;
                }
                totals.start_balance += item.start_balance;
                totals.quantity_in += item.quantity_in;
                totals.quantity_out += item.quantity_out;
                totals.last_balance += item.last_balance;
                totals.quantity_check += item.quantity_check;
            }

            // ? Karton
            Karton = Karton.sort((a, b) => {
                const indexA = listOrderOfKarton.indexOf(a.product_code);
                const indexB = listOrderOfKarton.indexOf(b.product_code);
                if (indexA === -1) return 1;
                if (indexB === -1) return -1;
                return indexA - indexB;
            });
            const indexKA1500 = Karton.map((e) => e.product_code).indexOf(
                "KA1500"
            );
            if (indexKA1500 !== -1) {
                Karton.splice(indexKA1500 + 1, 0, rowTotal);
            }
            const indexKV1500 = Karton.map((e) => e.product_code).indexOf(
                "KV1500"
            );
            if (indexKV1500 !== -1) {
                Karton.splice(indexKV1500 + 1, 0, rowTotal);
            }
            const indexKP1500 = Karton.map((e) => e.product_code).indexOf(
                "KP1500"
            );
            if (indexKP1500 !== -1) {
                Karton.splice(indexKP1500 + 1, 0, rowTotal);
            }
            Karton.push(rowTotal);
            for (let index = 0; index < Karton.length; index++) {
                const item = Karton[index];
                // console.log("🚀 ~ item:", item?.product?.code);
                if (index === 0) {
                    totals = {
                        start_balance: 0,
                        quantity_in: 0,
                        quantity_out: 0,
                        last_balance: 0,
                        quantity_check: 0,
                    };
                }
                if (item.id === "total") {
                    Karton[index] = {
                        ...Karton[index],
                        start_balance: totals.start_balance,
                        quantity_in: totals.quantity_in,
                        quantity_out: totals.quantity_out,
                        last_balance: totals.last_balance,
                        quantity_check: totals.quantity_check,
                    };
                    totals = {
                        start_balance: 0,
                        quantity_in: 0,
                        quantity_out: 0,
                        last_balance: 0,
                        quantity_check: 0,
                    };
                    continue;
                }
                totals.start_balance += item.start_balance;
                totals.quantity_in += item.quantity_in;
                totals.quantity_out += item.quantity_out;
                totals.last_balance += item.last_balance;
                totals.quantity_check += item.quantity_check;
            }

            // ? LPT
            LPG = LPG.sort((a, b) => {
                const indexA = listOrderOfLPG.indexOf(a.product_code);
                const indexB = listOrderOfLPG.indexOf(b.product_code);
                if (indexA === -1) return 1;
                if (indexB === -1) return -1;
                return indexA - indexB;
            });
            const indexIBG5 = LPG.map((e) => e.product_code).indexOf("IBG5");
            if (indexIBG5 !== -1) {
                LPG.splice(indexIBG5 + 1, 0, rowTotal);
            }
            const indexTBG5 = LPG.map((e) => e.product_code).indexOf("TBG5");
            if (indexTBG5 !== -1) {
                LPG.splice(indexTBG5 + 1, 0, rowTotal);
            }
            for (let index = 0; index < LPG.length; index++) {
                const item = LPG[index];
                if (item.id === "total") {
                    LPG[index] = {
                        ...LPG[index],
                        start_balance: totals.start_balance,
                        quantity_in: totals.quantity_in,
                        quantity_out: totals.quantity_out,
                        last_balance: totals.last_balance,
                        quantity_check: totals.quantity_check,
                    };
                    totals = {
                        start_balance: 0,
                        quantity_in: 0,
                        quantity_out: 0,
                        last_balance: 0,
                        quantity_check: 0,
                    };
                    continue;
                }
                totals.start_balance += item.start_balance;
                totals.quantity_in += item.quantity_in;
                totals.quantity_out += item.quantity_out;
                totals.last_balance += item.last_balance;
                totals.quantity_check += item.quantity_check;
            }

            return {
                Galon,
                Karton,
                LPG,
                ...others,
            };
        },
    },
    methods: {
        moment: moment,
        upsertAccSummaryProduct: async function () {
            const ini = this;
            if (!ini.stockopname) {
                return;
            }
            if (!ini.stockopname.store.accurate_warehouse_name) {
                alert(
                    "Nama gudang belum diset di toko " +
                        ini.stockopname.store.name
                );
                return;
            }
            ini.isLoadingRefresh = true;
            try {
                const res = await axios.put(
                    "/manager/stockopname/accsummary/product",
                    {
                        stockopname_id: ini.stockopname.id,
                        store_id: this.currentStore.id,
                        date: this.datenow,
                    }
                );
                const data = res.data;
                console.log("🚀 ~ summary data:", data);
                ini.stockopname.total_spd_acc = parseInt(data.total_spd_acc);
                ini.stockopname.total_spd_deposit = parseInt(
                    data.total_spd_deposit
                );
                ini.stockopname.qty_spd_acc = parseInt(data.qty_spd_acc);
                ini.stockopname.qty_spd_deposit = parseInt(
                    data.qty_spd_deposit
                );
                ini.stockopname.total_spd_gpa = parseInt(data.total_spd_gpa);
                ini.stockopname.qty_spd_gpa = parseInt(data.qty_spd_gpa);
                ini.stockopname.total_std = parseInt(data.total_std);
                ini.stockopname.total_std_cpr = parseInt(data.total_std_cpr);
                ini.stockopname.avg_std = parseInt(data.avg_std);
                ini.stockopname.avg_std_cpr = parseInt(data.avg_std_cpr);
                ini.stockopname.total_noncash_in = parseInt(
                    data.total_noncash_in
                );
                ini.stockopname.total_cost = parseInt(data.total_cost);
                ini.stockopname.total_cash_to_deposit = parseInt(
                    data.total_cash_to_deposit
                );
                ini.stockopname.updated_at = data.updated_at;
                ini.stockopname.editor.name = data.editor.name;
                ini.isLoadingRefresh = false;
            } catch (error) {
                console.log("🚀 ~ summary error", error.response.data);
                ini.isLoadingRefresh = false;
            }
        },
        upsertAccStockProducts: async function (isForce = false) {
            // console.log("init");
            const ini = this;
            if (!ini.stockopname) {
                return;
            }
            if (!ini.stockopname.store.accurate_warehouse_name) {
                alert(
                    "Nama gudang belum diset di toko " +
                        ini.stockopname.store.name
                );
                return;
            }

            if (new Date(ini.datenow) > new Date("2025-05-05")) {
                mdtoast("CHECK & RE-SYNC JOB ACC", {
                    duration: 1500,
                    type: mdtoast.INFO,
                });
                try {
                    const resJobsResyncAcc = await axios.post(
                        "/manager/jobs/resyncacc",
                        {
                            store_id: ini.stockopname.store.id,
                            date: ini.datenow,
                        }
                    );
                    console.log("🚀 ~ resJobsResyncAcc:", resJobsResyncAcc);
                    if (resJobsResyncAcc?.data) {
                        mdtoast("RE-SYNC JOB ACC SUCCESS", {
                            duration: 1500,
                            type: mdtoast.SUCCESS,
                        });
                    } else {
                        mdtoast("RE-SYNC JOB ACC FAILED", {
                            duration: 1500,
                            type: mdtoast.ERROR,
                        });
                    }
                } catch (error) {
                    console.error(
                        "Error during resync job:",
                        error.response?.data || error.message
                    );
                    mdtoast("RE-SYNC JOB ACC FAILED", {
                        duration: 1500,
                        type: mdtoast.ERROR,
                    });
                }
            }
            let countSync = 0;
            ini.loadingPercentage = 0;
            const percentageUnit = Math.floor(100 / ini.productsList.length);
            ini.stockopname.products.map((p) => {
                p.is_loading = true;
                return p;
            });
            if (
                isForce ||
                ini.stockopname.products.some((p) => !p.last_synced_at)
            ) {
                ini.upsertAccSummaryProduct();
            }
            for (let index = 0; index < ini.productsList.length; index++) {
                const productLocal = ini.productsList[index];
                let doSync = false;
                let productFound = null;
                const productIndex = ini.stockopname.products.findIndex(
                    (sp) => sp && sp.product_id === productLocal.id
                );
                if (productIndex > -1) {
                    productFound = ini.stockopname.products[productIndex];
                    if (!productFound.last_synced_at) {
                        doSync = true;
                        countSync++;
                    }
                } else {
                    doSync = true;
                    countSync++;
                }
                if (isForce) {
                    doSync = true;
                    countSync++;
                }
                if (doSync) {
                    try {
                        const res = await axios.post(
                            "/manager/stockopname/accstock/product",
                            {
                                stockopname_product_id: productFound
                                    ? productFound.id
                                    : "new",
                                stockopname_id: ini.stockopname.id,
                                product_id: productLocal.id,
                                product_code: productLocal.code,
                                warehouse_name:
                                    ini.stockopname.store
                                        .accurate_warehouse_name,
                                store_id: ini.stockopname.store.id,
                                date: ini.datenow,
                            }
                        );
                        const data = res.data;
                        console.log("🚀 ~ data:", data);
                        if (productIndex > -1) {
                            if (isForce) {
                                data.is_checked =
                                    ini.stockopname.products[
                                        productIndex
                                    ].is_checked;
                                data.quantity_check =
                                    ini.stockopname.products[
                                        productIndex
                                    ].quantity_check;
                            }
                            ini.stockopname.products.splice(
                                productIndex,
                                1,
                                data
                            );
                        } else {
                            ini.stockopname.products.push(data);
                        }
                    } catch (error) {
                        console.log("🚀 ~ error", error.response.data);
                        const data = error.response.data.stockopname_product;
                        if (data) {
                            console.log("🚀 ~ data", data);
                            if (isForce) {
                                data.is_checked =
                                    ini.stockopname.products[
                                        productIndex
                                    ].is_checked;
                            }
                            ini.stockopname.products.splice(
                                productIndex,
                                1,
                                data
                            );
                        }
                        if (
                            error.response.data.error ||
                            error.response.data.message
                        ) {
                            console.log("🚀 ~ masuk else");
                            ini.stockopname.products[productIndex].error =
                                error.response.data.error ??
                                error.response.data.message;
                        }
                    }
                }
                if (productIndex > -1) {
                    ini.stockopname.products[productIndex].is_loading = false;
                }
                ini.loadingPercentage += percentageUnit;
            }
            ini.loadingPercentage = 100;
            setTimeout(() => {
                if (countSync > 0) {
                    alert("Data stok produk berhasil tersinkron");
                }
                ini.loadingPercentage = -1;
            }, 500);
        },
        onClickRefreshData: async function () {
            this.upsertAccStockProducts(true);
        },
        onClickCreateStockopname: async function () {
            const ini = this;
            this.isLoadingCreate = true;
            try {
                const res = await axios.post("/manager/stockopname/create", {
                    store_id: this.currentStore.id,
                    date: this.datenow,
                    // assigned_to: 1,
                });
                console.log("🚀 ~ res:", res);
                const stockopname = res.data;
                if (
                    stockopname &&
                    typeof stockopname === "object" &&
                    "id" in stockopname
                ) {
                    ini.stockopname = stockopname;
                    ini.upsertAccStockProducts();
                }
                this.isLoadingCreate = false;
            } catch (error) {
                console.log("error", error.response.data);
                mdtoast(error.response.data.error, {
                    duration: 3000,
                    type: mdtoast.ERROR,
                });
                this.isLoadingCreate = false;
            }
        },
        onClickMinFisik: function (stockopnameProductId) {
            const productIndex = this.stockopname.products.findIndex(
                (sp) => sp && sp.id === stockopnameProductId
            );
            if (productIndex > -1) {
                this.stockopname.products[productIndex].quantity_check--;
            }
        },
        onClickPlusFisik: function (stockopnameProductId) {
            const productIndex = this.stockopname.products.findIndex(
                (sp) => sp && sp.id === stockopnameProductId
            );
            if (productIndex > -1) {
                this.stockopname.products[productIndex].quantity_check++;
            }
        },
        onClickAddCost: function () {
            this.stockopname.costs.push({
                id: `new_${Math.floor(Math.random() * 5)}`,
                note: "",
                cost: 0,
            });
        },
        onClickRemoveCost: function (index) {
            if (confirm("Hapus item ini?")) {
                this.stockopname.costs[index].note = "__delete_this_row__";
            }
        },
        doSave: async function (data) {
            const ini = this;
            ini.isLoadingSubmit = "draft";
            try {
                const res = await axios.put(
                    "/manager/stockopname/update/" + ini.stockopname.id,
                    data
                );
                console.log("🚀 ~ res:", res);
                const stockopname = res.data;
                if (
                    stockopname &&
                    typeof stockopname === "object" &&
                    "id" in stockopname
                ) {
                    ini.stockopname = stockopname;
                }
                let msg = "";
                if ("is_submit" in data && data.is_submit) {
                    msg = "Submit data berhasil";
                } else if ("is_confirm" in data && data.is_confirm) {
                    msg = "Confirm data berhasil";
                } else {
                    msg = "Draft data berhasil disimpan";
                }
                mdtoast(msg, {
                    duration: 3000,
                    type: mdtoast.SUCCESS,
                });
                ini.isLoadingSubmit = false;
            } catch (error) {
                console.log("error", error.response.data);
                mdtoast(error.response.data.error, {
                    duration: 3000,
                    type: mdtoast.ERROR,
                });
                ini.isLoadingSubmit = false;
            }
        },
        onClickSaveDraft: async function () {
            console.log("🚀 ~ onClickSaveDraft:");
            let errorMessage = "";
            if (this.stockopname.costs.some((c) => !c.note)) {
                errorMessage = "Catatan pengeluaran wajib diisi!";
            }
            if (errorMessage) {
                mdtoast(errorMessage, {
                    duration: 1500,
                    type: mdtoast.ERROR,
                });
                return;
            }
            await this.doSave(this.stockopname);
        },
        onClickSaveSubmit: async function () {
            console.log("🚀 ~ onClickSaveSubmit:");
            let errorMessage = "";
            if (this.stockopname.costs.some((c) => !c.note)) {
                errorMessage = "Catatan pengeluaran wajib diisi!";
            }
            if (
                this.stockopname.products.filter((p) => p.is_checked).length !==
                this.productsList.length
            ) {
                errorMessage = "Produk belum ter-centang semua!";
            }
            if (errorMessage) {
                mdtoast(errorMessage, {
                    duration: 1500,
                    type: mdtoast.ERROR,
                });
                return;
            }
            if (confirm("Submit tis report?")) {
                await this.doSave({ ...this.stockopname, is_submit: true });
            }
        },
        onClickSaveConfirm: async function () {
            console.log("🚀 ~ onClickSaveConfirm:");
            let errorMessage = "";
            if (this.stockopname.costs.some((c) => !c.note)) {
                errorMessage = "Catatan pengeluaran wajib diisi!";
            }
            if (
                this.stockopname.products.filter((p) => p.is_checked).length !==
                this.productsList.length
            ) {
                errorMessage = "Produk belum ter-centang semua!";
            }
            if (errorMessage) {
                mdtoast(errorMessage, {
                    duration: 1500,
                    type: mdtoast.ERROR,
                });
                return;
            }
            if (confirm("Confirm this report?")) {
                await this.doSave({ ...this.stockopname, is_confirm: true });
            }
        },
    },
    // watch: {
    //   filter_payment_method: function (newValue, oldValue) {
    //     // console.log("watch");
    //     // console.log(oldValue);
    //     // console.log(newValue);
    //   },
    // },
};
</script>
