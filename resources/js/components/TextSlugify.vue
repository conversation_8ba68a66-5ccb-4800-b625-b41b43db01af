<template>
    <div class="d-flex align-items-center">
        <input
            class="SharpText SharpTextSlugify"
            :value="value"
            @input="handleChanged"
        />
    </div>
</template>

<script>
export default {
    props: {
        value: String // field value
    },
    methods: {
        handleChanged(e) {
            const toKebabCase = str =>
                str &&
                str
                    .match(
                        /[A-Z]{2,}(?=[A-Z][a-z]+[0-9]*|\b)|[A-Z]?[a-z]+[0-9]*|[A-Z]|[0-9]+/g
                    )
                    .map(x => x.toLowerCase())
                    .join("-");
            const valKebabCased = toKebabCase(e.target.value);
            this.$emit("input", valKebabCased); // emit input when the value change, form data is updated
        }
    }
};
</script>

<style type="text/css" scoped>
.SharpTextPrepend {
    flex-grow: 1;
}
</style>
