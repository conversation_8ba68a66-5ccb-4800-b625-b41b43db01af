<script setup lang="ts">
import MobileAppLayout from '@/layouts/MobileAppLayout.vue';
import { Head, Link, router } from '@inertiajs/vue3';
import { computed } from 'vue';

interface Store {
    id: number;
    slug: string;
    name: string;
}

interface Employee {
    id: number;
    full_name: string;
}

interface SDM {
    id: number;
    name: string;
    store_id: number;
    role: { title: string };
    employee: Employee[];
}

interface Armada {
    id: number;
    licence_number: string;
    store_id: number;
    employee_id: number;
}

interface User {
    id: number;
    role_id: number;
    email: string;
    name: string;
}

interface Props {
    store_slug: string;
    store_id: number;
    stores: Store[];
    sdms: SDM[];
    armadas: Armada[];
    user: User;
}

const props = defineProps<Props>();

// Computed properties for role-based visibility
const isSuperAdminOwnerCS = computed(() => props.user.role_id <= 3);
const isSuperAdminOwner = computed(() => props.user.role_id <= 1);
const isManager = computed(() => props.user.role_id === 3);
const canManageSDM = computed(() => props.user.role_id <= 4);
const canManageArmada = computed(() => props.user.role_id <= 3);
const canSyncStock = computed(() => 
    props.user.role_id <= 2 || 
    props.user.email === '<EMAIL>' || 
    props.user.email === '<EMAIL>'
);

// Navigation handlers
const handleStoreChange = (event: Event) => {
    const target = event.target as HTMLSelectElement;
    router.visit(target.value);
};

const handleSDMStoreChange = (event: Event, sdmId: number, currentStoreId: number) => {
    const target = event.target as HTMLSelectElement;
    const selectedStoreId = parseInt(target.value) || 0;
    
    const selectedStore = props.stores.find(store => store.id === selectedStoreId);
    const currentStore = props.stores.find(store => store.id === currentStoreId);
    const sdm = props.sdms.find(s => s.id === sdmId);
    
    const selectedStoreName = selectedStore ? selectedStore.name : 'Kosong';
    const currentStoreName = currentStore ? currentStore.name : 'Kosong';
    
    if (confirm(`Pindah 🛵 ${sdm?.name}: 🛍 ${currentStoreName} ➡️ ke toko 🛍 ${selectedStoreName}?`)) {
        // In a real app, this would submit the form
        console.log('Moving SDM', sdmId, 'from store', currentStoreId, 'to store', selectedStoreId);
        // For demo purposes, just show an alert
        alert('SDM berhasil dipindahkan! (Demo mode)');
    } else {
        target.value = currentStoreId.toString();
    }
};

const handleArmadaEmployeeChange = (event: Event, armadaId: number, currentEmployeeId: number) => {
    const target = event.target as HTMLSelectElement;
    const selectedEmployeeId = parseInt(target.value) || 0;
    
    if (selectedEmployeeId !== currentEmployeeId) {
        if (confirm('Pindah armada ke SDM yang dipilih?')) {
            // In a real app, this would submit the form
            console.log('Moving Armada', armadaId, 'from employee', currentEmployeeId, 'to employee', selectedEmployeeId);
            // For demo purposes, just show an alert
            alert('Armada berhasil dipindahkan! (Demo mode)');
        } else {
            target.value = currentEmployeeId.toString();
        }
    }
};

const handleSyncStock = () => {
    if (confirm('Update stock dari Accurate?')) {
        alert('Stock berhasil diupdate! (Demo mode)');
    }
};

const handleLogout = () => {
    if (confirm('Yakin ingin logout?')) {
        router.post('/logout');
    }
};

// Get current store name
const currentStoreName = computed(() => {
    const store = props.stores.find(s => s.slug === props.store_slug);
    return store ? store.name : 'Unknown Store';
});
</script>

<template>
    <Head title="Account" />

    <MobileAppLayout :store-slug="store_slug">
        <!-- Header Section -->
        <template #header>
            <h2 class="text-lg font-bold">Account</h2>
        </template>

        <!-- Main Content -->
        <div class="px-5 pb-20 pt-20">
            <div class="grid grid-cols-1 gap-6">
                <h2 class="text-2xl font-bold text-gray-700">{{ user.name }}</h2>
                
                <!-- Store Selection for Super Admin, Owner and CS -->
                <label v-if="isSuperAdminOwnerCS" class="block">
                    <span class="font-bold text-gray-700">Toko</span>
                    <select
                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-red-200 focus:ring-opacity-50"
                        @change="handleStoreChange"
                    >
                        <option
                            v-for="store in stores"
                            :key="store.id"
                            :value="route('account', { store: store.slug })"
                            :selected="store.slug === store_slug"
                        >
                            {{ store.name }}
                        </option>
                    </select>
                </label>
            </div>

            <!-- Manager Performance Link -->
            <template v-if="isManager">
                <hr class="my-10" />
                <!-- Performance link would go here if needed -->
            </template>

            <hr class="my-10" />

            <!-- Sync Stock Button -->
            <button
                v-if="canSyncStock"
                type="button"
                class="mb-5 inline-block w-full rounded-md bg-blue-500 px-4 py-3 text-center text-base font-bold uppercase tracking-widest text-white shadow-xl transition duration-150 ease-in-out hover:opacity-90 focus:outline-none active:opacity-90 disabled:opacity-25"
                @click="handleSyncStock"
            >
                Update Stock
            </button>

            <!-- Accurate API Links for Super Admin -->
            <template v-if="isSuperAdminOwner">
                <Link
                    href="/accurate-auth"
                    target="_blank"
                    class="relative mb-5 w-full bg-blue-400 px-4 py-3 rounded-md font-bold text-base shadow-xl text-white uppercase tracking-widest hover:opacity-90 active:opacity-90 focus:outline-none disabled:opacity-25 transition ease-in-out duration-150"
                >
                    1. Get Accurate API Access
                </Link>
                <Link
                    href="/accurate-sync-init"
                    target="_blank"
                    class="relative mb-5 w-full bg-blue-500 px-4 py-3 rounded-md font-bold text-base shadow-xl text-white uppercase tracking-widest hover:opacity-90 active:opacity-90 focus:outline-none disabled:opacity-25 transition ease-in-out duration-150"
                >
                    2. Sync Accurate Initial DB
                </Link>
                <Link
                    href="/accurate-sync"
                    target="_blank"
                    class="relative mb-5 w-full bg-blue-600 px-4 py-3 rounded-md font-bold text-base shadow-xl text-white uppercase tracking-widest hover:opacity-90 active:opacity-90 focus:outline-none disabled:opacity-25 transition ease-in-out duration-150"
                >
                    3. Sync Accurate Customer
                </Link>
            </template>

            <!-- Inactive Customers Link -->
            <template v-if="isSuperAdminOwnerCS">
                <Link
                    :href="`/cs/list/customer?filter_stores%5B0%5D=${store_id}&page=1&filter_last_order=%3E3`"
                    class="relative w-full bg-green-600 px-4 py-3 rounded-md font-bold text-base shadow-xl text-white uppercase tracking-widest hover:opacity-90 active:opacity-90 focus:outline-none disabled:opacity-25 transition ease-in-out duration-150"
                >
                    List Pelanggan Tidak Aktif
                </Link>
                <hr class="my-10" />
            </template>

            <!-- SDM Management -->
            <div v-if="canManageSDM && sdms.length > 0" class="flex flex-col gap-2">
                <h2 class="text-lg font-bold text-gray-700">SDM 👉 Toko</h2>
                <div v-for="sdm in sdms" :key="sdm.id" class="flex items-center">
                    <!-- SDM Info -->
                    <div class="mr-1 flex flex-col text-sm font-semibold">
                        <Link
                            v-if="sdm.employee.length > 0"
                            :href="user.role_id <= 3 ? `/manager/account/${store_slug}/sdm/${sdm.employee[0].id}` : '#'"
                            class="text-red-700 underline"
                        >
                            {{ sdm.name }} ↗︎
                        </Link>
                        <Link
                            v-else
                            :href="user.role_id <= 3 ? `/cs/form/employee?user_id=${sdm.id}` : '#'"
                            class="flex flex-col"
                        >
                            <p class="flex flex-col">
                                {{ sdm.name }}
                                <span class="text-yellow-500 underline">➕ Data SDM</span>
                            </p>
                        </Link>
                        <span class="text-xs text-gray-400">{{ sdm.role.title }}</span>
                    </div>
                    
                    <span class="ml-auto mr-2 text-sm">👉</span>
                    
                    <!-- Store Selection -->
                    <select
                        class="mt-1 block rounded-md border-gray-300 text-sm shadow-sm focus:border-blue-300 focus:ring focus:ring-red-200 focus:ring-opacity-50"
                        :value="sdm.store_id"
                        @change="handleSDMStoreChange($event, sdm.id, sdm.store_id)"
                    >
                        <option v-if="user.role_id <= 3" value="0">Kosong</option>
                        <option
                            v-for="store in stores"
                            :key="store.id"
                            :value="store.id"
                            :selected="store.id === sdm.store_id"
                        >
                            {{ store.name }}
                        </option>
                    </select>
                </div>
                <hr class="my-10" />
            </div>

            <!-- Armada Management -->
            <div v-if="canManageArmada && armadas.length > 0" class="flex flex-col gap-2">
                <h2 class="text-lg font-bold text-gray-700">Armada 👉 SDM</h2>
                <div v-for="armada in armadas" :key="armada.id" class="flex items-center">
                    <!-- Armada Info -->
                    <Link
                        :href="`/manager/account/${store_slug}/armada/${armada.id}`"
                        class="mr-1 text-sm font-semibold text-red-700 underline"
                    >
                        {{ armada.licence_number }} ↗︎
                    </Link>
                    
                    <span class="ml-auto mr-2 text-sm">👉</span>
                    
                    <!-- Employee Selection -->
                    <select
                        class="mt-1 block rounded-md border-gray-300 text-sm shadow-sm focus:border-blue-300 focus:ring focus:ring-red-200 focus:ring-opacity-50"
                        :value="armada.employee_id"
                        @change="handleArmadaEmployeeChange($event, armada.id, armada.employee_id)"
                    >
                        <option value="0">Cadangan</option>
                        <template v-for="sdm in sdms" :key="sdm.id">
                            <option
                                v-if="sdm.employee.length > 0"
                                :value="sdm.employee[0].id"
                                :selected="sdm.employee[0].id === armada.employee_id"
                            >
                                {{ sdm.employee[0].full_name }}
                            </option>
                        </template>
                    </select>
                </div>
                <hr class="my-10" />
            </div>

            <!-- Logout Button -->
            <button
                type="button"
                class="relative w-full bg-red-600 px-4 py-3 rounded-md font-bold text-base shadow-xl text-white uppercase tracking-widest hover:opacity-90 active:opacity-90 focus:outline-none disabled:opacity-25 transition ease-in-out duration-150"
                @click="handleLogout"
            >
                Log Out
                <svg
                    class="absolute right-2 top-0 bottom-0 ml-auto h-full w-6"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                >
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                    />
                </svg>
            </button>
            
            <!-- App Version -->
            <p class="mt-3 w-full text-center text-xs italic text-gray-400">
                Gasplus Manager v1.0
            </p>
        </div>
    </MobileAppLayout>
</template>
