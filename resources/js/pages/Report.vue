<script setup lang="ts">
import MobileAppLayout from '@/layouts/MobileAppLayout.vue';
import { Head, Link, router } from '@inertiajs/vue3';
import { computed } from 'vue';

interface Store {
    id: number;
    slug: string;
    name: string;
}

interface User {
    id: number;
    role_id: number;
    email: string;
    name: string;
    options?: {
        is_show_ltt?: boolean;
    };
}

interface Props {
    store_slug: string;
    stores: Store[];
    user: User;
}

const props = defineProps<Props>();

// Computed properties for role-based visibility
const isSuperAdminOwner = computed(() => [1, 2].includes(props.user.role_id));
const canSeeDeposit = computed(() => [1, 2, 3].includes(props.user.role_id));
const canSeeLTT = computed(() => {
    return [1, 2].includes(props.user.role_id) || 
           (props.user.options?.is_show_ltt === true);
});

// Navigation handlers
const handleStoreChange = (event: Event) => {
    const target = event.target as HTMLSelectElement;
    router.visit(target.value);
};
</script>

<template>
    <Head title="Report" />

    <MobileAppLayout :store-slug="store_slug">
        <!-- Header Section with Yellow Background -->
        <template #header>
            <div class="absolute inset-0 flex items-center bg-yellow-500 px-4">
                <h2 class="text-lg font-bold">
                    Report
                    <select 
                        class="ml-1 inline border-none bg-transparent p-0 text-lg font-bold text-black"
                        @change="handleStoreChange"
                    >
                        <option
                            v-for="store in stores"
                            :key="store.id"
                            :value="route('report', { store: store.slug })"
                            :selected="store.slug === store_slug"
                        >
                            {{ store.name }}
                        </option>
                    </select>
                </h2>
            </div>
        </template>

        <!-- Main Content -->
        <div class="flex flex-col gap-8 px-8 pb-20 pt-24">
            <!-- Driver Performance Report -->
            <Link
                v-if="isSuperAdminOwner"
                href="#"
                class="w-full bg-blue-600 px-4 py-3 rounded-md font-bold text-base shadow-xl text-white uppercase tracking-widest hover:opacity-90 active:opacity-90 focus:outline-none disabled:opacity-25 transition ease-in-out duration-150"
            >
                In-Job 🛵
            </Link>

            <!-- Lantai 2 Performance Report -->
            <Link
                v-if="isSuperAdminOwner"
                href="#"
                class="w-full bg-blue-500 px-4 py-3 rounded-md font-bold text-base shadow-xl text-white uppercase tracking-widest hover:opacity-90 active:opacity-90 focus:outline-none disabled:opacity-25 transition ease-in-out duration-150"
            >
                Lantai 2 🎢
            </Link>

            <!-- B-Bro Performance Report -->
            <Link
                v-if="isSuperAdminOwner"
                href="#"
                class="w-full bg-pink-600 px-4 py-3 rounded-md font-bold text-base shadow-xl text-white uppercase tracking-widest hover:opacity-90 active:opacity-90 focus:outline-none disabled:opacity-25 transition ease-in-out duration-150"
            >
                Performa B-Bro 🏍
            </Link>

            <!-- Product Performance Report -->
            <Link
                v-if="isSuperAdminOwner"
                href="#"
                class="w-full bg-green-600 px-4 py-3 rounded-md font-bold text-base shadow-xl text-white uppercase tracking-widest hover:opacity-90 active:opacity-90 focus:outline-none disabled:opacity-25 transition ease-in-out duration-150"
            >
                Performa Produk 📦
            </Link>

            <!-- LTT (Lap. Tutup Toko) Report -->
            <Link
                v-if="canSeeLTT"
                href="#"
                class="w-full bg-yellow-600 px-4 py-3 rounded-md font-bold text-base shadow-xl text-white uppercase tracking-widest hover:opacity-90 active:opacity-90 focus:outline-none disabled:opacity-25 transition ease-in-out duration-150"
            >
                (LTT) Lap. Tutup Toko 🏬
            </Link>

            <!-- Deposit Report -->
            <Link
                v-if="canSeeDeposit"
                href="#"
                class="w-full bg-indigo-600 px-4 py-3 rounded-md font-bold text-base shadow-xl text-white uppercase tracking-widest hover:opacity-90 active:opacity-90 focus:outline-none disabled:opacity-25 transition ease-in-out duration-150"
            >
                Report Deposit 🌿
            </Link>
        </div>
    </MobileAppLayout>
</template>
