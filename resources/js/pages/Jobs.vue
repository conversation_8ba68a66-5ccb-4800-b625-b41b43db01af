<script setup lang="ts">
import MobileAppLayout from '@/layouts/MobileAppLayout.vue';
import { Head, Link, router } from '@inertiajs/vue3';
import { computed } from 'vue';

interface Store {
    id: number;
    slug: string;
    name: string;
}

interface Driver {
    id: number;
    name: string;
    orders_diambil: number;
    orders_terkirim: number;
    orders_selesai: number;
    orders_batal: number;
    marketings_diambil: number;
    marketings_selesai: number;
    marketings_batal: number;
}

interface Bank {
    bank_name: string;
    orders_count: number;
    order_total: number;
}

interface StoreData {
    id: number;
    marketing_each_day: number;
    hide_freejob_fordelman: boolean;
}

interface User {
    id: number;
    role_id: number;
    email: string;
    name: string;
}

interface OrderUnfinish {
    date: string;
    count: number;
}

interface Props {
    store_slug: string;
    stores: Store[];
    drivers: Driver[];
    banks: Bank[];
    date_now: string;
    date_prev: string;
    date_next: string;
    day: string;
    store: StoreData;
    current_marketing: { id: number; photos: any[] };
    order_total_count: number;
    order_bebas_count: number;
    order_diambil_count: number;
    order_terkirim_count: number;
    order_selesai_count: number;
    order_batal_count: number;
    marketing_diambil_count: number;
    marketing_selesai_count: number;
    marketing_batal_count: number;
    marketing_plus_selesai_count: number;
    order_terkirim_cash_count: number;
    order_terkirim_cash_total: number;
    order_terkirim_non_cash_count: number;
    order_terkirim_non_cash_total: number;
    order_selesai_cash_count: number;
    order_selesai_cash_total: number;
    order_selesai_split_cash_total: number;
    order_selesai_non_cash_count: number;
    order_selesai_non_cash_total: number;
    order_selesai_invoice_count: number;
    order_selesai_invoice_total: number;
    invoice_confirmed_non_cash_count: number;
    invoice_confirmed_non_cash_total: number;
    invoice_total_count: number;
    order_total: number;
    total_operatingcost_today: number;
    total_job_this_month: number;
    total_job_this_month_overtime: number;
    total_job_today: number;
    total_job_today_overtime: number;
    orders_unfinish: OrderUnfinish[];
    user: User;
}

const props = defineProps<Props>();

// Computed properties for role-based visibility
const isAdminRole = computed(() => [1, 2, 3, 4, 6].includes(props.user.role_id));
const isDriverRole = computed(() => props.user.role_id === 5);
const canManagePurchases = computed(() => [1, 2, 3, 4, 6].includes(props.user.role_id));
const canManageInvoices = computed(() => props.user.role_id <= 3);
const canAddOrders = computed(() => [1, 2, 3, 4, 6].includes(props.user.role_id));
const canManageOperatingCosts = computed(() => [1, 2, 3, 4, 5, 6].includes(props.user.role_id));
const canSeeFinancials = computed(() => props.user.role_id <= 4 || props.user.role_id === 6);

// Navigation handlers
const handleStoreChange = (event: Event) => {
    const target = event.target as HTMLSelectElement;
    router.visit(target.value);
};

const handleDateChange = (event: Event) => {
    const target = event.target as HTMLInputElement;
    const url = new URL(window.location.href);
    url.searchParams.set('date', target.value);
    router.visit(url.toString());
};

// Calculate percentages for overtime
const percentageMonth = computed(() => {
    return props.total_job_this_month > 0 && props.total_job_this_month_overtime > 0
        ? Math.round((props.total_job_this_month_overtime / props.total_job_this_month) * 100)
        : 0;
});

const percentageToday = computed(() => {
    return props.total_job_today > 0 && props.total_job_today_overtime > 0
        ? Math.round((props.total_job_today_overtime / props.total_job_today) * 100)
        : 0;
});

// Calculate total marketing selesai by driver
const totalMarketingSelesaiByDriver = computed(() => {
    return props.drivers.reduce((total, driver) => total + driver.marketings_selesai, 0);
});

// Check if B-Bro is done
const bbroIsDone = computed(() => {
    if (isDriverRole.value) {
        return props.store.marketing_each_day <= props.marketing_selesai_count;
    } else {
        return props.store.marketing_each_day * props.drivers.length <= totalMarketingSelesaiByDriver.value;
    }
});

// Check if current date
const isToday = computed(() => props.date_now === new Date().toISOString().slice(0, 10));

// Format currency
const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID').format(amount);
};
</script>

<template>
    <Head title="Jobs" />

    <MobileAppLayout :store-slug="store_slug">
        <!-- Header Section -->
        <template #header>
            <h2 class="text-sm font-bold">
                Jobs
                <select class="ml-0.5 inline border-none bg-transparent p-0 text-sm font-bold text-white" @change="handleStoreChange">
                    <option
                        v-for="store in stores"
                        :key="store.id"
                        :value="route('jobs', { store: store.slug, date: date_now })"
                        :selected="store.slug === store_slug"
                    >
                        {{ store.name }}
                    </option>
                </select>
            </h2>
            <div class="ml-auto flex">
                <!-- Purchase Button -->
                <Link v-if="canManagePurchases" href="#" class="ml-2.5 w-9 text-center">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            d="M13.5 16.875h3.375m0 0h3.375m-3.375 0V13.5m0 3.375v3.375M6 10.5h2.25a2.25 2.25 0 0 0 2.25-2.25V6a2.25 2.25 0 0 0-2.25-2.25H6A2.25 2.25 0 0 0 3.75 6v2.25A2.25 2.25 0 0 0 6 10.5Zm0 9.75h2.25A2.25 2.25 0 0 0 10.5 18v-2.25a2.25 2.25 0 0 0-2.25-2.25H6a2.25 2.25 0 0 0-2.25 2.25V18A2.25 2.25 0 0 0 6 20.25Zm9.75-9.75H18a2.25 2.25 0 0 0 2.25-2.25V6A2.25 2.25 0 0 0 18 3.75h-2.25A2.25 2.25 0 0 0 13.5 6v2.25a2.25 2.25 0 0 0 2.25 2.25Z"
                        />
                    </svg>
                    <span class="-mt-1 block text-xs">Prchs</span>
                </Link>

                <!-- Invoice Button -->
                <Link v-if="canManageInvoices" href="#" class="ml-2.5 w-9 text-center">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                    </svg>
                    <span class="-mt-1 block text-xs">Invc</span>
                </Link>

                <!-- Add Order Button -->
                <Link v-if="canAddOrders" href="#" class="ml-2.5 w-9 text-center">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z"
                            clip-rule="evenodd"
                        />
                    </svg>
                    <span class="-mt-1 block text-xs">Add</span>
                </Link>
            </div>
        </template>

        <!-- Navigation Section -->
        <template #subheader>
            <div class="mt-2 flex h-10 items-center justify-center text-red-500">
                <Link :href="route('jobs', { store: store_slug, date: date_prev })" class="h-6 w-6">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm.707-10.293a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L9.414 11H13a1 1 0 100-2H9.414l1.293-1.293z"
                            clip-rule="evenodd"
                        />
                    </svg>
                </Link>
                <input
                    type="date"
                    class="mx-3 w-56 border-none p-0 text-center text-lg font-bold focus:ring-0"
                    :max="new Date().toISOString().slice(0, 10)"
                    :value="date_now"
                    @change="handleDateChange"
                />
                <Link v-if="date_next" :href="route('jobs', { store: store_slug, date: date_next })" class="h-6 w-6">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 1.414L10.586 9H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z"
                            clip-rule="evenodd"
                        />
                    </svg>
                </Link>
            </div>
            <p class="w-full pb-3 text-center text-sm text-gray-400">{{ day }}</p>
        </template>

        <!-- Main Content -->
        <div class="px-8 pt-36 pb-20">
            <!-- Overtime Statistics -->
            <Link
                v-if="canSeeFinancials"
                href="#"
                class="mt-3 h-8 w-full bg-gray-200 pt-1 text-gray-500 capitalize shadow-none"
                style="font-size: 13px"
            >
                {{ total_job_this_month }}/<span class="text-red-600">{{ total_job_this_month_overtime }}</span> -
                <span :class="percentageMonth > 10 ? 'text-red-600' : 'text-green-500'">{{ percentageMonth }}%</span>&nbsp;&nbsp;|&nbsp;&nbsp;{{
                    total_job_today
                }}/<span class="text-red-600">{{ total_job_today_overtime }}</span> -
                <span :class="percentageToday > 10 ? 'text-red-600' : 'text-green-500'">{{ percentageToday }}%</span>
            </Link>

            <!-- B-Bro Button -->
            <button
                type="button"
                :class="[
                    'mt-6 w-full gap-3 border-4 text-white',
                    bbroIsDone ? 'border-green-900 bg-green-600' : 'border-pink-900 bg-pink-600',
                    !isToday ? 'pointer-events-none opacity-50' : '',
                    'rounded-md px-4 py-3 text-base font-bold tracking-widest uppercase shadow-xl transition duration-150 ease-in-out hover:opacity-90 focus:outline-none active:opacity-90 disabled:opacity-25',
                ]"
            >
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="4"
                    stroke="currentColor"
                    :class="['inline-block h-6 w-6', bbroIsDone ? 'text-green-900' : 'text-pink-900']"
                >
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                </svg>
                <span v-if="isDriverRole"> B-Bro ({{ store.marketing_each_day }}-{{ marketing_selesai_count }}) </span>
                <span v-else> B-Bro ({{ store.marketing_each_day * drivers.length }}-{{ totalMarketingSelesaiByDriver }}) </span>
            </button>

            <!-- Operating Costs Button -->
            <Link
                v-if="canManageOperatingCosts"
                href="#"
                class="mt-6 w-full gap-3 rounded-md border-4 border-green-900 bg-green-700 px-4 py-3 text-base font-bold tracking-widest text-white uppercase shadow-xl transition duration-150 ease-in-out hover:opacity-90 focus:outline-none active:opacity-90 disabled:opacity-25"
            >
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="4"
                    stroke="currentColor"
                    class="inline-block h-6 w-6 text-green-900"
                >
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                </svg>
                Pengeluaran Toko
            </Link>

            <!-- Invoice Link -->
            <Link
                v-if="invoice_total_count > 0"
                href="#"
                class="mt-8 w-full rounded-md bg-blue-200 px-4 py-3 text-base font-bold tracking-widest text-blue-900 uppercase shadow-xl transition duration-150 ease-in-out hover:opacity-90 focus:outline-none active:opacity-90 disabled:opacity-25"
            >
                🧾 Invoice ({{ invoice_total_count }})
            </Link>

            <!-- Total Jobs -->
            <Link
                href="#"
                :class="[
                    'mt-8 w-full rounded-md bg-black px-4 py-3 text-base font-bold tracking-widest text-white uppercase shadow-xl transition duration-150 ease-in-out hover:opacity-90 focus:outline-none active:opacity-90 disabled:opacity-25',
                    order_total_count === 0 ? 'pointer-events-none opacity-25' : '',
                ]"
            >
                Total Job ({{ order_total_count }})
            </Link>

            <!-- Free Jobs -->
            <Link
                v-if="!isDriverRole || (isDriverRole && !store.hide_freejob_fordelman)"
                href="#"
                :class="[
                    'mt-8 w-full rounded-md bg-yellow-500 px-4 py-3 text-base font-bold tracking-widest text-white uppercase shadow-xl transition duration-150 ease-in-out hover:opacity-90 focus:outline-none active:opacity-90 disabled:opacity-25',
                    order_bebas_count === 0 ? 'pointer-events-none opacity-25' : '',
                ]"
            >
                Job Bebas ({{ order_bebas_count }})
            </Link>

            <!-- Picked Up Jobs -->
            <Link
                href="#"
                :class="[
                    'mt-8 w-full rounded-md bg-blue-500 px-4 py-3 text-base font-bold tracking-widest text-white uppercase shadow-xl transition duration-150 ease-in-out hover:opacity-90 focus:outline-none active:opacity-90 disabled:opacity-25',
                    order_diambil_count === 0 ? 'pointer-events-none opacity-25' : '',
                ]"
            >
                Job Diambil ({{ order_diambil_count }})
                <template v-for="driver in drivers" :key="driver.id">
                    <br /><span class="text-xs leading-none">{{ driver.name }} ({{ driver.orders_diambil }})</span>
                </template>
            </Link>

            <!-- Delivered Jobs -->
            <Link
                href="#"
                :class="[
                    'mt-8 w-full rounded-md bg-blue-700 px-4 py-3 text-base font-bold tracking-widest text-white uppercase shadow-xl transition duration-150 ease-in-out hover:opacity-90 focus:outline-none active:opacity-90 disabled:opacity-25',
                    order_terkirim_count === 0 ? 'pointer-events-none opacity-25' : '',
                ]"
            >
                Job Terkirim ({{ order_terkirim_count }})
                <template v-for="driver in drivers" :key="driver.id">
                    <br /><span class="text-xs leading-none">{{ driver.name }} ({{ driver.orders_terkirim }})</span>
                </template>
                <hr class="my-2" />
                <span class="text-xs leading-none">🪙 CASH ({{ order_terkirim_cash_count }}) </span>
                <span class="text-xs leading-none capitalize">Rp{{ formatCurrency(order_terkirim_cash_total) }}</span>
                <br /><span class="text-xs leading-none">🏧 NON-CASH ({{ order_terkirim_non_cash_count }}) </span>
                <span class="text-xs leading-none capitalize">Rp{{ formatCurrency(order_terkirim_non_cash_total) }}</span>
            </Link>

            <!-- Completed Jobs -->
            <Link
                href="#"
                :class="[
                    'mt-8 w-full rounded-md bg-green-500 px-4 py-3 text-base font-bold tracking-widest text-white uppercase shadow-xl transition duration-150 ease-in-out hover:opacity-90 focus:outline-none active:opacity-90 disabled:opacity-25',
                    order_selesai_count === 0 ? 'pointer-events-none opacity-25' : '',
                ]"
            >
                Job Selesai ({{ order_selesai_count }})
                <template v-for="driver in drivers" :key="driver.id">
                    <br /><span class="text-xs leading-none">{{ driver.name }} ({{ driver.orders_selesai }})</span>
                </template>
                <div class="capitalize">
                    <hr class="mt-1 mb-2.5" />
                    <p class="mt-1.5 rounded bg-blue-200 px-0.5 text-sm text-blue-900">
                        🧾 Invoice ({{ invoice_confirmed_non_cash_count }})
                        <span v-if="canSeeFinancials" class="font-bold"> Rp{{ formatCurrency(invoice_confirmed_non_cash_total) }} </span>
                    </p>
                    <hr class="mt-2.5 mb-2.5" />
                    <p class="mt-1.5 rounded bg-green-700 px-0.5 text-sm">
                        🪙 CASH ({{ order_selesai_cash_count }})
                        <span v-if="canSeeFinancials" class="font-bold">
                            Rp{{ formatCurrency(order_selesai_cash_total + order_selesai_split_cash_total) }}
                        </span>
                    </p>
                    <p class="mt-1.5 rounded bg-blue-800 px-0.5 text-sm">
                        🏧 NON-CASH ({{ order_selesai_non_cash_count + order_selesai_invoice_count }})
                        <span v-if="canSeeFinancials" class="font-bold">
                            Rp{{ formatCurrency(order_selesai_non_cash_total + order_selesai_invoice_total) }}
                        </span>
                    </p>
                    <template v-for="bank in banks" :key="bank.bank_name">
                        <p
                            v-if="bank.bank_name.toLowerCase().trim() !== 'deposit'"
                            :class="['mt-1.5 rounded px-0.5 text-sm', bank.bank_name.toLowerCase() === 'qris' ? 'bg-gray-700' : 'bg-blue-700']"
                        >
                            {{ bank.bank_name }} ({{ bank.orders_count }})
                            <span v-if="canSeeFinancials" class="font-bold"> Rp{{ formatCurrency(bank.order_total) }} </span>
                        </p>
                    </template>
                    <p class="mt-1.5 rounded bg-blue-600 px-0.5 text-sm">
                        AR ({{ order_selesai_invoice_count }})
                        <span class="font-bold"> Rp{{ formatCurrency(order_selesai_invoice_total) }} </span>
                    </p>
                    <p v-if="canSeeFinancials" class="mt-1.5 px-0.5 text-sm">
                        TOTAL
                        <span class="font-bold"> Rp{{ formatCurrency(order_total) }} </span>
                    </p>
                </div>
            </Link>

            <!-- Operating Costs Summary -->
            <Link
                v-if="total_operatingcost_today > 0"
                href="#"
                :class="[
                    'mt-8 w-full rounded-md border-4 border-green-700 bg-transparent px-4 py-3 text-base font-bold tracking-widest text-green-700 uppercase shadow-xl transition duration-150 ease-in-out hover:opacity-90 focus:outline-none active:opacity-90 disabled:opacity-25',
                    total_operatingcost_today === 0 ? 'pointer-events-none opacity-25' : '',
                ]"
            >
                Total Pengeluaran
                <p class="mt-1.5 rounded bg-yellow-200 px-0.5 text-sm text-yellow-900 capitalize">
                    🥊 Total All:
                    <span class="font-bold"> Rp{{ formatCurrency(total_operatingcost_today) }} </span>
                </p>
            </Link>

            <!-- Marketing Diambil -->
            <Link
                v-if="marketing_diambil_count > 0"
                href="#"
                :class="[
                    'mt-8 w-full rounded-md border-4 border-pink-500 bg-transparent px-4 py-3 text-base font-bold tracking-widest text-pink-500 uppercase shadow-xl transition duration-150 ease-in-out hover:opacity-90 focus:outline-none active:opacity-90 disabled:opacity-25',
                    marketing_diambil_count === 0 ? 'pointer-events-none opacity-25' : '',
                ]"
            >
                Bagi Brosur ({{ marketing_diambil_count }})
                <template v-for="driver in drivers" :key="driver.id">
                    <br /><span class="text-xs leading-none">{{ driver.name }} ({{ driver.marketings_diambil }})</span>
                </template>
            </Link>

            <!-- Marketing Selesai -->
            <Link
                href="#"
                :class="[
                    'mt-8 w-full rounded-md border-4 border-pink-600 bg-transparent px-4 py-3 text-base font-bold tracking-widest text-pink-600 uppercase shadow-xl transition duration-150 ease-in-out hover:opacity-90 focus:outline-none active:opacity-90 disabled:opacity-25',
                    marketing_selesai_count === 0 ? 'pointer-events-none opacity-25' : '',
                ]"
            >
                <span v-if="isDriverRole">
                    B-BRO Selesai ({{ store.marketing_each_day }}-{{ marketing_selesai_count }})
                    <span v-if="marketing_plus_selesai_count > 0">+{{ marketing_plus_selesai_count }}</span>
                </span>
                <span v-else>
                    B-BRO Selesai ({{ store.marketing_each_day * drivers.length }}-{{ totalMarketingSelesaiByDriver }})
                    <span v-if="marketing_plus_selesai_count > 0">+{{ marketing_plus_selesai_count }}</span>
                    <template v-for="driver in drivers" :key="driver.id">
                        <br /><span class="text-xs leading-none"
                            >{{ driver.name }} ({{ store.marketing_each_day }}-{{ driver.marketings_selesai }})</span
                        >
                    </template>
                </span>
            </Link>

            <!-- Marketing Batal -->
            <Link
                v-if="marketing_batal_count > 0"
                href="#"
                :class="[
                    'mt-8 w-full rounded-md border-4 border-red-600 bg-transparent px-4 py-3 text-base font-bold tracking-widest text-red-600 uppercase shadow-xl transition duration-150 ease-in-out hover:opacity-90 focus:outline-none active:opacity-90 disabled:opacity-25',
                    marketing_batal_count === 0 ? 'pointer-events-none opacity-25' : '',
                ]"
            >
                Batal Bagi Brosur ({{ marketing_batal_count }})
                <template v-for="driver in drivers" :key="driver.id">
                    <br /><span class="text-xs leading-none">{{ driver.name }} ({{ driver.marketings_batal }})</span>
                </template>
            </Link>

            <!-- Cancelled Jobs -->
            <Link
                href="#"
                :class="[
                    'mt-8 w-full rounded-md bg-red-500 px-4 py-3 text-base font-bold tracking-widest text-white uppercase shadow-xl transition duration-150 ease-in-out hover:opacity-90 focus:outline-none active:opacity-90 disabled:opacity-25',
                    order_batal_count === 0 ? 'pointer-events-none opacity-25' : '',
                ]"
            >
                Job Batal ({{ order_batal_count }})
                <template v-for="driver in drivers" :key="driver.id">
                    <br /><span class="text-xs leading-none">{{ driver.name }} ({{ driver.orders_batal }})</span>
                </template>
            </Link>

            <!-- Unfinished Orders -->
            <Link
                v-for="orderUnfinish in orders_unfinish"
                :key="orderUnfinish.date"
                :href="route('jobs', { store: store_slug, date: orderUnfinish.date })"
                class="mt-8 w-full rounded-md bg-indigo-600 px-4 py-3 text-base font-bold tracking-widest text-white uppercase shadow-xl transition duration-150 ease-in-out hover:opacity-90 focus:outline-none active:opacity-90 disabled:opacity-25"
            >
                Unconfirmed Job ({{ orderUnfinish.count }}) <br /><span class="text-xs opacity-50">{{ orderUnfinish.date }}</span>
            </Link>
        </div>
    </MobileAppLayout>
</template>
