<script setup lang="ts">
import { Link, usePage } from '@inertiajs/vue3';
import { computed } from 'vue';
import { route } from 'ziggy-js';

interface Props {
    storeSlug?: string;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const props = withDefaults(defineProps<Props>(), {
    storeSlug: 'all',
});

const page = usePage();

// Mock user data for now - in real app this would come from shared props
const user = computed(() => ({
    id: 1,
    role_id: 1,
    name: 'Admin User',
    email: '<EMAIL>',
}));

// Mock IAK balance for admin users
const iakBalance = computed(() => 1500000);
const iakBalanceUpdated = computed(() => '2024-01-15 10:30:00');

// Check if user is admin (roles 1,2)
const isAdmin = computed(() => [1, 2].includes(user.value.role_id));

// Check if user can see reports (roles 1,2,3 or has special option)
const canSeeReports = computed(() => [1, 2, 3].includes(user.value.role_id));

// Get current route name
const currentRoute = computed(() => {
    const url = page.url;
    if (url.includes('/calendar')) return 'calendar';
    if (url.includes('/jobs')) return 'jobs';
    if (url.includes('/report')) return 'report';
    if (url.includes('/account')) return 'account';
    return '';
});

// Navigation link classes
const getLinkClass = (routeName: string) => {
    const isActive = currentRoute.value === routeName;
    return `${isActive ? 'text-red-600' : 'text-gray-600'} flex flex-col items-center justify-center`;
};

const getReportLinkClass = () => {
    const isActive = currentRoute.value === 'report';
    return `${isActive ? 'text-yellow-600' : 'text-gray-600'} flex flex-col items-center justify-center`;
};
</script>

<template>
    <div class="bg-gray-300 font-sans antialiased">
        <div id="app">
            <!-- IAK Balance (Saldo) for Admin Users -->
            <div v-if="isAdmin" class="fixed top-0 right-0 left-0 z-50">
                <div class="relative mx-auto max-w-xl">
                    <button
                        type="button"
                        style="top: 53px; right: 3px"
                        class="absolute z-50 flex cursor-pointer flex-row-reverse items-center justify-center gap-1 rounded bg-red-900 px-1 py-0.5 text-white shadow"
                    >
                        <div class="flex items-center justify-center text-xs font-black">
                            Rp<span>{{ iakBalance.toLocaleString('id-ID') }}</span>
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="ml-0.5 h-3 w-3 stroke-2">
                                <path
                                    fill-rule="evenodd"
                                    d="M15.312 11.424a5.5 5.5 0 0 1-9.201 2.466l-.312-.311h2.433a.75.75 0 0 0 0-1.5H3.989a.75.75 0 0 0-.75.75v4.242a.75.75 0 0 0 1.5 0v-2.43l.31.31a7 7 0 0 0 11.712-*********** 0 0 0-1.449-.39Zm1.23-3.723a.75.75 0 0 0 .219-.53V2.929a.75.75 0 0 0-1.5 0V5.36l-.31-.31A7 7 0 0 0 3.239 8.188a.75.75 0 1 0 1.448.389A5.5 5.5 0 0 1 13.89 6.11l.311.31h-2.432a.75.75 0 0 0 0 1.5h4.243a.75.75 0 0 0 .53-.219Z"
                                    clip-rule="evenodd"
                                />
                            </svg>
                        </div>
                        <span class="text-white opacity-75" style="font-size: 9px">
                            {{ iakBalanceUpdated }}
                        </span>
                    </button>
                </div>
            </div>

            <!-- Page Heading -->
            <header class="fixed top-0 right-0 left-0 z-30">
                <div class="mx-auto max-w-xl bg-white shadow">
                    <div class="relative flex h-14 items-center bg-red-700 px-4 text-white">
                        <slot name="header" />
                    </div>
                    <slot name="subheader" />
                </div>
            </header>

            <!-- Page Content -->
            <main class="relative mx-auto min-h-screen max-w-xl bg-white">
                <!-- Loading overlay for offline jobs -->
                <div style="display: none" class="fixed top-14 right-0 bottom-16 left-0 z-50 flex items-center justify-center">
                    <div class="flex h-full w-full max-w-xl items-center justify-center bg-white text-xl font-bold text-gray-300">
                        Uploading Offline Jobs...
                    </div>
                </div>

                <slot />
            </main>

            <!-- Bottom Navigation -->
            <nav class="fixed right-0 bottom-0 left-0 z-30 h-16">
                <div class="mx-auto flex min-h-full max-w-xl items-center justify-around border-t-2 border-solid border-gray-300 bg-white shadow">
                    <!-- Calendar Link -->
                    <Link :href="route('calendar')" :class="getLinkClass('calendar')">
                        <svg
                            v-if="currentRoute === 'calendar'"
                            class="h-8 w-8"
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                        >
                            <path
                                fill-rule="evenodd"
                                d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                                clip-rule="evenodd"
                            />
                        </svg>
                        <svg v-else class="h-8 w-8" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                            />
                        </svg>
                        <span class="mt-1 text-xs font-bold">Calendar</span>
                    </Link>

                    <!-- Jobs Link -->
                    <Link :href="route('jobs')" :class="getLinkClass('jobs')">
                        <svg
                            v-if="currentRoute === 'jobs'"
                            class="h-8 w-8"
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                        >
                            <path
                                fill-rule="evenodd"
                                d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z"
                                clip-rule="evenodd"
                            />
                            <path d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z" />
                        </svg>
                        <svg v-else class="h-8 w-8" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                            />
                        </svg>
                        <span class="mt-1 text-xs font-bold">Jobs</span>
                    </Link>

                    <!-- Reports Link (conditional) -->
                    <Link v-if="canSeeReports" :href="route('report')" :class="getReportLinkClass()">
                        <svg
                            v-if="currentRoute === 'report'"
                            xmlns="http://www.w3.org/2000/svg"
                            class="h-8 w-8"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                        >
                            <path
                                fill-rule="evenodd"
                                d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z"
                                clip-rule="evenodd"
                            />
                        </svg>
                        <svg
                            v-else
                            xmlns="http://www.w3.org/2000/svg"
                            class="h-8 w-8"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                            stroke-width="2"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"
                            />
                        </svg>
                        <span class="mt-1 text-xs font-bold">Performa</span>
                    </Link>

                    <!-- Account Link -->
                    <Link :href="route('account')" :class="getLinkClass('account')">
                        <svg
                            v-if="currentRoute === 'account'"
                            class="h-8 w-8"
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                        >
                            <path
                                fill-rule="evenodd"
                                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z"
                                clip-rule="evenodd"
                            />
                        </svg>
                        <svg v-else class="h-8 w-8" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                        </svg>
                        <span class="mt-1 text-xs font-bold">{{ user.name }}</span>
                    </Link>
                </div>
            </nav>
        </div>
    </div>
</template>
