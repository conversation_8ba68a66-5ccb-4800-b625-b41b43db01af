<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMarketingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('areas', function (Blueprint $table) {
            $table->id();
            $table->text('name');
            $table->string('latlng');
            $table->timestamps();
            $table->softDeletes();
            $table->userstamps();
            $table->softUserstamps();
        });

        Schema::create('marketings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('status_id');
            $table->foreign('status_id')->references('id')->on('statuses');
            $table->unsignedBigInteger('area_id')->nullable();
            $table->foreign('area_id')->references('id')->on('areas');
            $table->string('code')->index()->nullable();
            $table->unsignedSmallInteger('sequence_per_day')->nullable();
            $table->string('driver_note')->nullable();
            // $table->string('confirm_note')->nullable();
            // $table->text('note')->nullable();
            $table->string('note_for_driver')->nullable();
            $table->unsignedBigInteger('store_id');
            $table->foreign('store_id')->references('id')->on('stores');
            $table->unsignedBigInteger('driver_id')->nullable();
            $table->foreign('driver_id')->references('id')->on('users');
            $table->unsignedBigInteger('cancel_by')->nullable();
            $table->foreign('cancel_by')->references('id')->on('users');
            // $table->unsignedBigInteger('confirmed_by')->nullable();
            // $table->foreign('confirmed_by')->references('id')->on('users');
            $table->integer('duration')->nullable(); // in second
            $table->integer('distance_store_area')->nullable(); // in meter
            $table->integer('distance_area_received')->nullable(); // in meter
            // $table->enum('payment', ['cash', 'transfer', 'qris'])->nullable();
            // $table->enum('payment_method_ask', ['cash', 'non-cash'])->nullable();
            // $table->enum('payment_method_confirmed', ['cash', 'transfer', 'qris'])->nullable();
            // $table->unsignedBigInteger('bank_id')->nullable();
            // $table->foreign('bank_id')->references('id')->on('banks');
            // $table->integer('amount_will_pay')->nullable();
            // $table->integer('amount_return')->nullable();
            // $table->integer('total_deposit')->nullable();
            // $table->string('receiver_phone')->nullable();
            $table->timestamp('received_at')->nullable();
            $table->string('received_latlng')->nullable();
            $table->string('received_latlng_accuracy')->nullable();
            $table->boolean('is_offline')->default(0);
            $table->timestamps();
            $table->softDeletes();
            $table->userstamps();
            $table->softUserstamps();
        });

        if (Schema::hasTable('addresses')) {
            if (!Schema::hasColumn('addresses', 'area_id')) {
                Schema::table('addresses', function (Blueprint $table) {
                    $table->unsignedBigInteger('area_id')->nullable()->after('customer_id');
                    $table->foreign('area_id')->references('id')->on('areas');
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('addresses', 'area_id')) {
            Schema::table('addresses', function (Blueprint $table) {
                $table->dropForeign(['area_id']);
                $table->dropColumn('area_id');
            });
        }

        Schema::dropIfExists('marketings');
        Schema::dropIfExists('areas');
    }
}