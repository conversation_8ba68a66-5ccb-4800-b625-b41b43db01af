<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMergersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('mergers', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('main_customer_id')->unique();
            $table->foreign('main_customer_id')->references('id')->on('customers');
            $table->text('note')->nullable();
            $table->timestamps();
            $table->userstamps();
        });

        if (Schema::hasTable('customers')) {
            if (!Schema::hasColumn('customers', 'merger_id')) {
                Schema::table('customers', function (Blueprint $table) {
                    $table->boolean('is_main')->default(1)->after('id');
                    $table->unsignedBigInteger('merger_id')->nullable()->after('id');
                    $table->foreign('merger_id')->references('id')->on('mergers');
                });
            }
        }

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('customers', 'merger_id')) {
            Schema::table('customers', function (Blueprint $table) {
                $table->dropForeign(['merger_id']);
                $table->dropColumn('merger_id');
                $table->dropColumn('is_main');
            });
        }

        Schema::dropIfExists('mergers');
    }
}