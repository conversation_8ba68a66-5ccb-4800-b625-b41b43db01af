<?php

use App\Models\Store;
use App\Models\Category;
use App\Models\CategoryStore;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateInsentiveTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('categories')) {
            if (!Schema::hasColumn('categories', 'insentive')) {
                Schema::table('categories', function (Blueprint $table) {
                    $table->integer('insentive')->nullable()->after('name');
                });
            }
        }

        Schema::create('category_store', function ($table) {
            $table->id();
            $table->unsignedBigInteger('category_id');
            $table->foreign('category_id')->references('id')->on('categories')->onDelete('cascade');
            $table->unsignedBigInteger('store_id');
            $table->foreign('store_id')->references('id')->on('stores')->onDelete('cascade');
            // $table->primary(['category_id', 'store_id']);
            $table->integer('insentive')->nullable();
            $table->timestamps();
        });

        $categories = Category::all();
        $stores = Store::all();
        foreach ($stores as $store) {
            foreach ($categories as $category) {
                CategoryStore::create([
                    'store_id' => $store->id,
                    'category_id' => $category->id,
                ]);
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('category_store');

        if (Schema::hasColumn('categories', 'insentive')) {
            Schema::table('categories', function (Blueprint $table) {
                $table->dropColumn('insentive');
            });
        }
    }
}
