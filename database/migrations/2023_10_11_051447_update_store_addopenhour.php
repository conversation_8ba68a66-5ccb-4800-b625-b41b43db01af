<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateStoreAddopenhour extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('stores')) {
            if (!Schema::hasColumn('stores', 'holiday_start')) {
                Schema::table('stores', function (Blueprint $table) {
                    $table->dateTimeTz('holiday_end', $precision = 0)->nullable()->after('close_hour');
                    $table->dateTimeTz('holiday_start', $precision = 0)->nullable()->after('close_hour');
                    $table->text('holiday_note')->nullable()->after('close_hour');
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('stores', 'holiday_start')) {
            Schema::table('stores', function (Blueprint $table) {
                $table->dropColumn('holiday_note');
                $table->dropColumn('holiday_start');
                $table->dropColumn('holiday_end');
            });
        }
    }
}