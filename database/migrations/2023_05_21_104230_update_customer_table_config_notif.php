<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateCustomerTableConfigNotif extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('customers')) {
            if (!Schema::hasColumn('customers', 'notif_status')) {
                Schema::table('customers', function (Blueprint $table) {
                    $table->enum('notif_status', ['unset', 'on', 'off'])->default('unset')->after('deposit_amount');
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('customers', 'notif_status')) {
            Schema::table('customers', function (Blueprint $table) {
                $table->dropColumn('notif_status');
            });
        }
    }
}