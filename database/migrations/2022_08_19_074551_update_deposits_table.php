<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateDepositsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('invoices')) {
            if (!Schema::hasColumn('invoices', 'confirmed_at')) {
                Schema::table('invoices', function (Blueprint $table) {
                    $table->timestamp('confirmed_at')->nullable()->after('confirmed_by');
                });
            }
        }
        if (Schema::hasTable('deposit_flows')) {
            if (!Schema::hasColumn('deposit_flows', 'invoice_id')) {
                Schema::table('deposit_flows', function (Blueprint $table) {
                    $table->unsignedBigInteger('invoice_id')->nullable()->after('id');
                    $table->foreign('invoice_id')->references('id')->on('invoices');
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('deposit_flows', 'invoice_id')) {
            Schema::table('deposit_flows', function (Blueprint $table) {
                $table->dropForeign(['invoice_id']);
                $table->dropColumn('invoice_id');
            });
        }
        if (!Schema::hasColumn('invoices', 'confirmed_at')) {
            Schema::table('invoices', function (Blueprint $table) {
                $table->timestamp('confirmed_at')->nullable()->after('confirmed_by');
            });
        }
    }
}