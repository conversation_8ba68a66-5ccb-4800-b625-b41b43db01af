<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateStoreTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('stores')) {
            if (!Schema::hasColumn('stores', 'msg_order_canceled')) {
                Schema::table('stores', function (Blueprint $table) {
                    $table->text('msg_order_canceled')->nullable()->after('msg_order_delivered');
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('stores', 'msg_order_canceled')) {
            Schema::table('stores', function (Blueprint $table) {
                $table->dropColumn('msg_order_canceled');
            });
        }
    }
}