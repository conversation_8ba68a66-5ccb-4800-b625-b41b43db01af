<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateTableOrderAddarmadaid extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('orders')) {
            if (!Schema::hasColumn('orders', 'armada_id')) {
                Schema::table('orders', function (Blueprint $table) {
                    $table->unsignedBigInteger('armada_id')->nullable()->after('id');
                    $table->foreign('armada_id')->references('id')->on('armadas');
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('orders', 'armada_id')) {
            Schema::table('orders', function (Blueprint $table) {
                $table->dropForeign(['armada_id']);
                $table->dropColumn('armada_id');
            });
        }
    }
}