<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateTableNotificationSchedule extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('notification_schedules')) {
            if (!Schema::hasColumn('notification_schedules', 'others')) {
                Schema::table('notification_schedules', function (Blueprint $table) {
                    $table->json('others')->nullable();
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('notification_schedules', 'others')) {
            Schema::table('notification_schedules', function (Blueprint $table) {
                $table->dropColumn('others');
            });
        }
    }
}
