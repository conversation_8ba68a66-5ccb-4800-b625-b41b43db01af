<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateCustomerTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('stores', 'socialchat_channel_id')) {
            Schema::table('stores', function (Blueprint $table) {
                $table->string('socialchat_channel_id')->nullable();
            });
        }
        if (!Schema::hasColumn('addresses', 'socialchat_conversation_id')) {
            Schema::table('addresses', function (Blueprint $table) {
                $table->string('socialchat_conversation_id')->nullable();
            });
        }
        if (!Schema::hasColumn('customers', 'socialchat_conversation_id')) {
            Schema::table('customers', function (Blueprint $table) {
                $table->string('socialchat_conversation_id')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('customers', 'socialchat_conversation_id')) {
            Schema::table('customers', function (Blueprint $table) {
                $table->dropColumn('socialchat_conversation_id');
            });
        }
        if (Schema::hasColumn('addresses', 'socialchat_conversation_id')) {
            Schema::table('addresses', function (Blueprint $table) {
                $table->dropColumn('socialchat_conversation_id');
            });
        }
        if (Schema::hasColumn('stores', 'socialchat_channel_id')) {
            Schema::table('stores', function (Blueprint $table) {
                $table->dropColumn('socialchat_channel_id');
            });
        }
    }
}