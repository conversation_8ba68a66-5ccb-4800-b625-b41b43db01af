<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateAddressTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('addresses')) {
            if (!Schema::hasColumn('addresses', 'accurate_id')) {
                Schema::table('addresses', function (Blueprint $table) {
                    $table->string('accurate_id')->unique()->nullable();
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('addresses', 'accurate_id')) {
            Schema::table('addresses', function (Blueprint $table) {
                $table->dropColumn('accurate_id');
            });
        }
    }
}