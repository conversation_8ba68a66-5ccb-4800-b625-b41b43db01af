<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateStockopnameproductTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('stockopname_products', 'last_synced_at')) {
            Schema::table('stockopname_products', function (Blueprint $table) {
                $table->timestamp('last_synced_at')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('stockopname_products', 'last_synced_at')) {
            Schema::table('stockopname_products', function (Blueprint $table) {
                $table->dropColumn('last_synced_at');
            });
        }
    }
}