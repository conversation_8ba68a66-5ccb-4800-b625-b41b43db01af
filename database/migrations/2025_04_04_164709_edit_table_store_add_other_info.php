<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class EditTableStoreAddOtherInfo extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('stores')) {
            if (!Schema::hasColumn('stores', 'meta')) {
                Schema::table('stores', function (Blueprint $table) {
                    $table->json('meta')->nullable();
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('stores', 'meta')) {
            Schema::table('stores', function (Blueprint $table) {
                $table->dropColumn('meta');
            });
        }
    }
}
