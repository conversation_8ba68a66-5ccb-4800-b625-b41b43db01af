<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateTablePpobCustomer extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('ppob_customer_datas')) {
            if (!Schema::hasColumn('ppob_customer_datas', 'ppob_product_code')) {
                Schema::table('ppob_customer_datas', function (Blueprint $table) {
                    $table->string('ppob_product_code')->nullable()->index();
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('ppob_customer_datas', 'ppob_product_code')) {
            Schema::table('ppob_customer_datas', function (Blueprint $table) {
                $table->dropColumn('ppob_product_code');
            });
        }
    }
}
