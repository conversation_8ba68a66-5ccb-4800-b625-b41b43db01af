<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAccurateInvoiceReceiptTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasColumn('orders', 'accurate_invoice_number')) {
            Schema::table('orders', function (Blueprint $table) {
                $table->dropColumn('accurate_invoice_number');
                $table->dropColumn('accurate_invoice_id');
                $table->dropColumn('accurate_receipt_number');
                $table->dropColumn('accurate_receipt_id');
                $table->dropColumn('accurate_invoiced_at');
                $table->dropColumn('accurate_receipted_at');
            });
        }

        if (!Schema::hasColumn('order_product', 'accurate_invoice_item_id')) {
            Schema::table('order_product', function (Blueprint $table) {
                $table->string('accurate_invoice_item_id')->nullable();
                $table->softDeletes();
            });
        }

        if (!Schema::hasColumn('additional_costs', 'accurate_invoice_item_id')) {
            Schema::table('additional_costs', function (Blueprint $table) {
                $table->string('accurate_invoice_item_id')->nullable();
                $table->enum('code', ['JLA'])->default('JLA');
                $table->softDeletes();
            });
        }

        Schema::create('accurate_datas', function (Blueprint $table) {
            $table->id();
            $table->morphs('accuratable');
            $table->string('accuratable_key');
            $table->string('accurate_id')->nullable();
            $table->string('accurate_no')->nullable();
            $table->string('accurate_name')->nullable();
            $table->text('error_message')->nullable();
            $table->timestamp('synced_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('accurate_datas');

        if (Schema::hasColumn('additional_costs', 'accurate_invoice_item_id')) {
            Schema::table('additional_costs', function (Blueprint $table) {
                $table->dropColumn('accurate_invoice_item_id');
                $table->dropColumn('code');
                $table->dropColumn('deleted_at');
            });
        }

        if (Schema::hasColumn('order_product', 'accurate_invoice_item_id')) {
            Schema::table('order_product', function (Blueprint $table) {
                $table->dropColumn('accurate_invoice_item_id');
                $table->dropColumn('deleted_at');
            });
        }

        if (!Schema::hasColumn('orders', 'accurate_invoice_number')) {
            Schema::table('orders', function (Blueprint $table) {
                $table->string('accurate_invoice_number')->nullable();
                $table->string('accurate_invoice_id')->nullable();
                $table->string('accurate_receipt_number')->nullable();
                $table->string('accurate_receipt_id')->nullable();
                $table->timestamp('accurate_invoiced_at')->nullable();
                $table->timestamp('accurate_receipted_at')->nullable();
            });
        }
    }
}
