<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AccurateUpdateProductTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('banks', 'accurate_glaccount_id')) {
            Schema::table('banks', function (Blueprint $table) {
                $table->string('accurate_glaccount_id')->nullable();
            });
        }

        if (!Schema::hasColumn('stores', 'accurate_cash_glaccount_id')) {
            Schema::table('stores', function (Blueprint $table) {
                $table->string('accurate_cash_glaccount_id')->nullable();
            });
        }

        if (!Schema::hasColumn('stores', 'accurate_warehouse_name')) {
            Schema::table('stores', function (Blueprint $table) {
                // $table->string('accurate_warehouse_id')->nullable();
                $table->string('accurate_warehouse_name')->nullable();
            });
        }

        if (!Schema::hasColumn('products', 'accurate_item_id')) {
            Schema::table('products', function (Blueprint $table) {
                $table->string('accurate_item_id')->nullable();
                $table->string('accurate_item_no')->nullable();
                // $table->string('accurate_unit_id')->nullable();
                // $table->string('accurate_unit_name')->nullable();
            });
        }

        if (!Schema::hasColumn('orders', 'accurate_invoice_number')) {
            Schema::table('orders', function (Blueprint $table) {
                $table->string('accurate_invoice_number')->nullable();
                $table->string('accurate_invoice_id')->nullable();
                $table->string('accurate_receipt_number')->nullable();
                $table->string('accurate_receipt_id')->nullable();
                $table->timestamp('accurate_invoiced_at')->nullable();
                $table->timestamp('accurate_receipted_at')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('orders', 'accurate_invoice_number')) {
            Schema::table('orders', function (Blueprint $table) {
                $table->dropColumn('accurate_invoice_number');
                $table->dropColumn('accurate_invoice_id');
                $table->dropColumn('accurate_receipt_number');
                $table->dropColumn('accurate_receipt_id');
                $table->dropColumn('accurate_invoiced_at');
                $table->dropColumn('accurate_receipted_at');
            });
        }

        if (Schema::hasColumn('products', 'accurate_item_id')) {
            Schema::table('products', function (Blueprint $table) {
                $table->dropColumn('accurate_item_id');
                $table->dropColumn('accurate_item_no');
                // $table->dropColumn('accurate_unit_id');
                // $table->dropColumn('accurate_unit_name');
            });
        }

        if (Schema::hasColumn('stores', 'accurate_warehouse_name')) {
            Schema::table('stores', function (Blueprint $table) {
                // $table->dropColumn('accurate_warehouse_id');
                $table->dropColumn('accurate_warehouse_name');
            });
        }

        if (Schema::hasColumn('stores', 'accurate_cash_glaccount_id')) {
            Schema::table('stores', function (Blueprint $table) {
                $table->dropColumn('accurate_cash_glaccount_id');
            });
        }

        if (Schema::hasColumn('banks', 'accurate_glaccount_id')) {
            Schema::table('banks', function (Blueprint $table) {
                $table->dropColumn('accurate_glaccount_id');
            });
        }
    }
}
