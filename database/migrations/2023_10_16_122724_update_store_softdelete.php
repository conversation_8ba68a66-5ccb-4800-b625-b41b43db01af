<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateStoreSoftdelete extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('stores')) {
            if (!Schema::hasColumn('stores', 'deleted_at')) {
                Schema::table('stores', function (Blueprint $table) {
                    $table->softDeletes();
                    $table->softUserstamps();
                    // $table->foreign('armada_id')->references('id')->on('armadas');
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}