<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateStockopnameSpdTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('stockopnames', 'total_spd_acc')) {
            Schema::table('stockopnames', function (Blueprint $table) {
                $table->dropColumn('date_submitted');
                $table->dropColumn('date_confirmed');
                $table->integer('total_spd_acc')->nullable();
                $table->integer('qty_spd_acc')->nullable();
                $table->integer('total_spd_gpa')->nullable();
                $table->integer('qty_spd_gpa')->nullable();
                $table->integer('total_std')->nullable();
                $table->integer('avg_std')->nullable();
                $table->integer('total_apc')->nullable();
                $table->integer('avg_apc')->nullable();
                $table->integer('total_noncash_in')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('stockopnames', 'total_spd_acc')) {
            Schema::table('stockopnames', function (Blueprint $table) {
                $table->dropColumn('total_spd_acc');
                $table->dropColumn('avg_spd_acc');
                $table->dropColumn('total_spd_gpa');
                $table->dropColumn('avg_spd_gpa');
                $table->dropColumn('total_std');
                $table->dropColumn('avg_std');
                $table->dropColumn('total_apc');
                $table->dropColumn('avg_apc');
                $table->dropColumn('total_noncash_in');
            });
        }
    }
}
