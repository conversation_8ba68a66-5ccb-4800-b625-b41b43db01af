<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Faker\Factory as Faker;
use Intervention\Image\ImageManagerStatic as Image;
use Illuminate\Support\Facades\File;
use Illuminate\Filesystem\Filesystem;
use App\Models\Category;
use App\Models\Product;
use App\Models\City;
use App\Models\Store;
use App\Models\Bank;
use App\Models\Status;
use App\Models\Order;

class GasplusTablesSeeder extends Seeder
{
    public function run()
    {
        function slugify($text)
        {
            // replace non letter or digits by -
            $text = preg_replace('~[^\pL\d]+~u', '-', $text);
            // transliterate
            $text = iconv('utf-8', 'us-ascii//TRANSLIT', $text);
            // remove unwanted characters
            $text = preg_replace('~[^-\w]+~', '', $text);
            // trim
            $text = trim($text, '-');
            // remove duplicate -
            $text = preg_replace('~-+~', '-', $text);
            // lowercase
            $text = strtolower($text);
            if (empty($text)) {
                return 'n-a';
            }
            return $text;
        }

        
        $categories = [
        'Air Minum',
        'LPG',
        'Frozen',
        'Aksesori',
        'PluSmart',
      ];
        foreach ($categories as $item) {
            Category::create([
          'name' => $item,
          'slug' => slugify($item),
        ]);
        }

        $jsonUrl = 'database/data/products.json';
        $jsonString = file_get_contents(base_path($jsonUrl));
        $prodcuts = json_decode($jsonString, true);

        // $path = 'public/img/products';
        // if (!File::isDirectory($path)) {
        //     File::makeDirectory($path, 0777, true, true);
        // } else {
        //     $file = new Filesystem;
        //     $file->cleanDirectory($path);
        // }

        foreach ($prodcuts as $item) {
            $data = [];
            $photos = [];
            $category_id = 0;
            foreach ($item as $key => $val) {
                if ($key == 'images') {
                    $photos = $val;
                } elseif ($key == 'name') {
                    $data[$key] = $val;
                    $data['slug'] = slugify($val);
                } elseif ($key == 'category_id') {
                    $category_id = $val;
                } else {
                    $data[$key] = $val;
                }
            }
            $product = Product::create($data);

            foreach ($photos as $ikey => $ival) {
                // $ext = pathinfo('database/data/img/products/'.$ival, PATHINFO_EXTENSION);
                // $imgName = $data['slug'].'-'.time().$ikey.'.'.$ext;
                // $img = Image::make('database/data/img/products/'.$ival);
                // $img->resize(null, 500, function ($constraint) {
                //     $constraint->aspectRatio();
                //     $constraint->upsize();
                // })->save('public/img/products'.DIRECTORY_SEPARATOR.$imgName, 90);
                // if ($ikey == 0) {
                //     $product->featurephoto()->create(['file' => $imgName]);
                // } else {
                //     $product->photos()->create(['file' => $imgName]);
                // }
            }

            $category = Category::find($category_id);
            $product->categories()->attach($category);
        }



        $cities = [
          'Yogyakarta',
          'Semarang',
          'Purwokerto',
        ];
        foreach ($cities as $item) {
            $city = City::create([
            'name' => $item,
            'slug' => slugify($item),
          ]);
        }
  
        $stores = [
          [
            'id' => 1,
            'city_id' => 1,
            'area' => 'Palagan',
            'name' => 'Gasplus Palagan',
            'photo' => 'database/data/img/stores/<EMAIL>',
            'address' => 'Jl. Palagan Tentara Pelajar No.6 Ngaglik, Sleman',
            'latlng' => '-7.8254453,110.3445913',
            'description' => '<p>Gasplus melayani delivery kebutuhan sehari hari</p><p>(Aqua galon, LPG, Frozen Food, dll.)</p><p>Order tanpa RIBET<br/>MUDAH ~ CEPAT ~ TERPERCAYA</p>',
            'whatsapp_1' => '***********',
            'email' => '<EMAIL>',
            'bank' => [
              [
                'bank_name' => 'BCA',
                'account_number' => '**********',
                'holder_name' => 'Nitisari Setiadi',
              ],
              [
                'bank_name' => 'Mandiri',
                'account_number' => '*************',
                'holder_name' => 'Nitisari Setiadi',
              ]
            ]
          ],
          [
            'id' => 2,
            'city_id' => 1,
            'area' => 'Kemetiran',
            'name' => 'Gasplus Kemetiran',
            'photo' => 'database/data/img/stores/gasplus-kemetiran.jpg',
            'address' => 'Jl. Kemetiran Lor No.20',
            'latlng' => '-7.7913901,110.3581959',
            'description' => '<p>Gasplus melayani delivery kebutuhan sehari hari</p><p>(Aqua galon, LPG, Frozen Food, dll.)</p><p>Order tanpa RIBET<br/>MUDAH ~ CEPAT ~ TERPERCAYA</p>',
            'whatsapp_1' => '***********',
            'email' => '<EMAIL>',
            'bank' => [
              [
                'bank_name' => 'BCA',
                'account_number' => '**********',
                'holder_name' => 'Andi Madewa',
              ]
            ]
          ],
          [
            'id' => 3,
            'city_id' => 2,
            'area' => 'Kedungmundu',
            'name' => 'Gasplus Kedungmundu',
            'photo' => 'database/data/img/stores/<EMAIL>',
            'address' => 'Jl. Kedungmundu No.18 Tembalang',
            'latlng' => '-7.0241536,110.4578767',
            'description' => '<p>Gasplus melayani delivery kebutuhan sehari hari</p><p>(Aqua galon, LPG, Frozen Food, dll.)</p><p>Order tanpa RIBET<br/>MUDAH ~ CEPAT ~ TERPERCAYA</p>',
            'whatsapp_1' => '***********',
            'email' => '<EMAIL>',
            'bank' => [
              [
                'bank_name' => 'Mandiri',
                'account_number' => '*************',
                'holder_name' => 'Ida Sulistyowati',
              ]
            ]
          ],
          [
            'id' => 4,
            'city_id' => 2,
            'area' => 'Tembalang',
            'name' => 'Gasplus Tembalang',
            'photo' => 'database/data/img/stores/gasplus-tembalang.jpg',
            'address' => 'Jl. Banjarsari Selatan No.53',
            'latlng' => '-7.0627173,110.4338965',
            'description' => '<p>Gasplus melayani delivery kebutuhan sehari hari</p><p>(Aqua galon, LPG, Frozen Food, dll.)</p><p>Order tanpa RIBET<br/>MUDAH ~ CEPAT ~ TERPERCAYA</p>',
            'whatsapp_1' => '***********',
            'email' => '<EMAIL>',
          ],
          [
            'id' => 5,
            'city_id' => 3,
            'area' => 'S. Parman',
            'name' => 'Ellora S. Parman',
            'photo' => 'database/data/img/stores/<EMAIL>',
            'address' => 'Jl. S. Parman No.1 Kongsen',
            'latlng' => '-7.4283122,109.1036941',
            'description' => '<p>Gasplus melayani delivery kebutuhan sehari hari</p><p>(Aqua galon, LPG, Frozen Food, dll.)</p><p>Order tanpa RIBET<br/>MUDAH ~ CEPAT ~ TERPERCAYA</p>',
            'whatsapp_1' => '************',
            'email' => '<EMAIL>',
            'bank' => [
              [
                'bank_name' => 'BCA',
                'account_number' => '**********',
                'holder_name' => 'Antonius Aditya A.',
              ]
            ]
          ],
        ];
        // $path = 'public/img/stores';
        // if (!File::isDirectory($path)) {
        //     File::makeDirectory($path, 0777, true, true);
        // } else {
        //     $file = new Filesystem;
        //     $file->cleanDirectory($path);
        // }
        foreach ($stores as $item) {
            $data = [];
            $photo = '';
            $banks = [];
            foreach ($item as $key => $val) {
                if ($key == 'photo') {
                    $photo = $val;
                } elseif ($key == 'area') {
                    $data[$key] = $val;
                    $data['slug'] = slugify($val);
                } elseif ($key == 'bank') {
                    foreach ($val as $ival) {
                        array_push($banks, $ival);
                    }
                } else {
                    $data[$key] = $val;
                }
            }
            $store = Store::create($data);

            // $ext = pathinfo($photo, PATHINFO_EXTENSION);
            // $imgName = 'gasplus-'.$data['slug'].'-'.time().'.'.$ext;
            // $img = Image::make($photo);
            // $img->resize(null, 300, function ($constraint) {
            //     $constraint->aspectRatio();
            //     $constraint->upsize();
            //     // })->save('public/img/stores'.DIRECTORY_SEPARATOR.$imgName, 90);
            // })->save('public/img/stores'.DIRECTORY_SEPARATOR.$imgName, 90);
            // $store->featurephoto()->create(['file' => $imgName]);
  
            foreach ($banks as $iival) {
                $bank = Bank::create($iival);
                $store->banks()->attach($bank);
            }
  
            $products = Product::all();
            foreach ($products as $product) {
                $store->products()->attach($product);
            }
        }
    }
}
