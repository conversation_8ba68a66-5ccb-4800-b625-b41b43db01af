const defaultTheme = require("tailwindcss/defaultTheme");
const colors = require("tailwindcss/colors");

module.exports = {
    purge: [
        "./storage/framework/views/*.php",
        "./resources/views/**/*.blade.php",
        "./resources/js/**/*.vue",
    ],

    theme: {
        extend: {
            fontFamily: {
                sans: ["Nunito", ...defaultTheme.fontFamily.sans],
            },
        },
        placeholderColor: {
            transparent: "transparent",
            current: "currentColor",
            black: colors.black,
            white: colors.white,
            gray: colors.blueGray,
            red: colors.red,
            blue: colors.cyan,
            yellow: colors.amber,
            pink: colors.pink,
            indigo: colors.indigo,
            green: colors.lime,
            truegreen: colors.green,
            trueblue: colors.blue,
        },
        colors: {
            // Build your palette here
            transparent: "transparent",
            current: "currentColor",
            black: colors.black,
            white: colors.white,
            gray: colors.blueGray,
            red: colors.red,
            blue: colors.cyan,
            yellow: colors.amber,
            pink: colors.pink,
            indigo: colors.indigo,
            green: colors.lime,
            truegreen: colors.green,
            trueblue: colors.blue,
        },
    },

    // variants: {
    //     extend: {
    //         opacity: ["disabled"]
    //     }
    // },

    plugins: [
        require("@tailwindcss/typography"),
        require("@tailwindcss/forms"),
        require("tailwindcss-textshadow"),
        require("@tailwindcss/line-clamp"),
        require("tailwind-scrollbar-hide"),
        // require("daisyui"),
    ],
};
