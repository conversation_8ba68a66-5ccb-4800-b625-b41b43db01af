<?php

namespace App\Sharp\Filters;

use Code16\Sharp\EntityList\EntityListSelectFilter;

class FeedbackServiceRatingFilter implements EntityListSelectFilter
{
    public function values()
    {
        return [
            1 => 'Bintang 1',
            2 => 'Bintang 2',
            3 => 'Bintang 3',
            4 => 'Bintang 4',
            5 => 'Bintang 5',
        ];
    }

    public function label()
    {
        return "Rating Servis";
    }

    // public function isSearchable(): bool
    // {
    //     return true;
    // }

    // public function retainValueInSession()
    // {
    //     return true;
    // }
}
