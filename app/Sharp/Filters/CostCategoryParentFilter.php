<?php

namespace App\Sharp\Filters;

use App\Models\CostCategory;
use Code16\Sharp\EntityList\EntityListSelectFilter;

class CostCategoryParentFilter implements EntityListSelectFilter
{
    public function values()
    {
        return CostCategory::where('is_root', 1)->pluck("title", "id")->all();
    }

    public function label()
    {
        return "Category";
    }

    // public function isSearchable(): bool
    // {
    //     return true;
    // }

    // public function retainValueInSession()
    // {
    //     return true;
    // }
}