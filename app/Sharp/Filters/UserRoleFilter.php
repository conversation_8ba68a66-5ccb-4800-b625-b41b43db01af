<?php

namespace App\Sharp\Filters;

use App\Models\Role;
use Code16\Sharp\EntityList\EntityListSelectFilter;

class UserRoleFilter implements EntityListSelectFilter
{
    public function values()
    {
        return Role::orderBy("title")
            ->pluck("title", "id")
            ->all();
    }

    public function label()
    {
        return "Role";
    }

    // public function retainValueInSession()
    // {
    //     return true;
    // }

    public function isSearchable(): bool
    {
        return true;
    }

    // public function searchKeys(): array
    // {
    //     return ["title"];
    // }

    // public function template(): string
    // {
    //     return "{{title}}";
    // }
}
