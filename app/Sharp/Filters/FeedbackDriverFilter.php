<?php

namespace App\Sharp\Filters;

use App\Models\User;
use Code16\Sharp\EntityList\EntityListSelectMultipleFilter;

class FeedbackDriverFilter implements EntityListSelectMultipleFilter
{
    public function values()
    {
        return User::where('role_id', 5)
            ->pluck("name", "id")
            ->all();
    }

    public function label()
    {
        return "Driver";
    }

    public function isSearchable(): bool
    {
        return true;
    }

    // public function retainValueInSession()
    // {
    //     return true;
    // }
}
