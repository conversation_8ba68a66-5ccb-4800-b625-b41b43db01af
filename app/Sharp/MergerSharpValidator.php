<?php

namespace App\Sharp;

use Code16\Sharp\Form\Validator\SharpFormRequest;
use Code16\Sharp\Http\WithSharpContext;

class MergerSharpValidator extends SharpFormRequest
{
    use WithSharpContext;

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'main_customer_id' => 'required',
            // 'phone' => [
            //     'required',
            //     $this->context()->instanceId() ? 'unique:customers,phone,'.$this->context()->instanceId() : 'unique:customers',
            // ],
            'members' => 'required|array',
            'members.*.customer_id' => 'required',
        ];
    }
}