<?php

namespace App\Sharp;

use App\Helper\Helper;
use App\Models\Category;
use Code16\Sharp\Form\Eloquent\WithSharpFormEloquentUpdater;
use Code16\Sharp\Form\Fields\SharpFormTextField;
use Code16\Sharp\Form\Layout\FormLayoutColumn;
use App\Sharp\CustomFormFields\SharpFormTextCustomField;
use Code16\Sharp\Form\SharpForm;

class CategorySharpForm extends SharpForm
{
    use WithSharpFormEloquentUpdater;

    /**
     * Retrieve a Model for the form and pack all its data as JSON.
     *
     * @param $id
     * @return array
     */
    public function find($id): array
    {
        return $this->transform(
            Category::findOrFail($id)
        );
    }

    /**
     * @param $id
     * @param array $data
     * @return mixed the instance id
     */
    public function update($id, array $data)
    {
        $category = $id ? Category::findOrFail($id) : new Category;

        function slugify($text)
        {
            // replace non letter or digits by -
            $text = preg_replace('~[^\pL\d]+~u', '-', $text);
            // transliterate
            $text = iconv('utf-8', 'us-ascii//TRANSLIT', $text);
            // remove unwanted characters
            $text = preg_replace('~[^-\w]+~', '', $text);
            // trim
            $text = trim($text, '-');
            // remove duplicate -
            $text = preg_replace('~-+~', '-', $text);
            // lowercase
            $text = strtolower($text);
            if (empty($text)) {
                return 'n-a';
            }
            return $text;
        }
        $data['slug'] = slugify($data['name']);
        $data['insentive'] = (int)$data['insentive'] > 0 ? $data['insentive'] : null;
        
        $this->save($category, $data);
        $detail = Helper::revalidateBelanjaKantor();
        $detail = Helper::revalidateFrontEnd();
        $this->notify('Data berhasil disimpan!')
            ->setDetail($detail)
            ->setLevelSuccess();
    }

    /**
     * @param $id
     */
    public function delete($id)
    {
        Category::findOrFail($id)->find($id)->delete();
    }

    /**
     * Build form fields using ->addField()
     *
     * @return void
     */
    public function buildFormFields()
    {
        $this
        ->addField(
            SharpFormTextField::make('name')
                ->setLabel('Name *')
        )
        ->addField(
            SharpFormTextCustomField::make("insentive")
                ->setLabel("Insentive")
                // ->setPrepend("Rp")
                ->setInputType('money')
        )
        ;
        // )->addField(
        //     SharpFormTextCustomField::make("slug")
        //         ->setLabel("Slug *")
        //         ->setPrepend("https://gasplus.online/toko/palagan/produk#")
        //         ->setSlugify()
        // );
    }

    /**
     * Build form layout using ->addTab() or ->addColumn()
     *
     * @return void
     */
    public function buildFormLayout()
    {
        $this->addColumn(6, function (FormLayoutColumn $column) {
            $column
                ->withSingleField('name')
                ->withSingleField('insentive')
            ;
            // ->withSingleField("slug");
        });
    }
}