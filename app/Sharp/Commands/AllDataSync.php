<?php

namespace App\Sharp\Commands;

use Code16\Sharp\EntityList\Commands\EntityCommand;
use Code16\Sharp\EntityList\EntityListQueryParams;

class AllDataSync extends EntityCommand
{
    public function label(): string
    {
        return "Synchronize All Data";
    }

    public function description(): string
    {
        return "Overide all data from remote.";
    }

    public function execute(EntityListQueryParams $params, array $data=[]): array
    {
        $end_point = env('SYNC_APP_URL', 'http://localhost/gasplus-admin/api/v1') . "/citiesstores";
        $http_header = array(
            "Content-Type: Application/Json",
        );
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_HTTPHEADER, $http_header);
        curl_setopt($ch, CURLOPT_URL, $end_point);
        // curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_HTTPGET, 1);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        // Send Notification to Driver
        // $req_data = array(
        //     'title' 		=> "Order Baru 👤 ".$order->customer->name, //required
        //     'message' 		=> "Antar sekarang!", //required
        //     'target_url'	=> env('DELIVERY_APP_URL', 'http://delivery.gasplus.online').'/order/'.$order->id, //required
        //     'attribute'		=> array(
        //         'user_id' => $data["driver_id"], // Driver
        //         // 'role_id' => 4, // Admin Toko
        //         // 'store_id' => $store->id
        //     ),
        //     //following parameters are optional
        //     //'name'		=> 'Test campaign',
        //     //'icon'		=> 'https://cdn.webpushr.com/siteassets/wSxoND3TTb.png',
        //     //'image'		=> 'https://cdn.webpushr.com/siteassets/aRB18p3VAZ.jpeg',
        //     //'auto_hide'	=> 1,
        //     //'expire_push'	=> '5m',
        //     //'send_at'		=> '2020-12-28 06:17 +5:30',
        //     //'action_buttons'=> array(
        //         //array('title'=> 'Demo', 'url' => 'https://www.webpushr.com/demo'),
        //         //array('title'=> 'Rates', 'url' => 'https://www.webpushr.com/pricing')
        //     //)
        // );
        // curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($req_data));
        $response = curl_exec($ch);
        return $this->info($response);
        // return $this->info("All data synchronized!");
    }

    public function confirmationText(): string
    {
        return "Sure, really?";
    }

    // public function authorize():bool
    // {
    //     return sharp_user()->hasGroup("boss");
    // }
}
