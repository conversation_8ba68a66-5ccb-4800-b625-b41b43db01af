<?php

namespace App\Sharp\Commands;

use App\Models\Store;
use App\Models\Address;
// use App\Models\Customer;
use App\Helper\Helper;
use Code16\Sharp\EntityList\Commands\EntityCommand;
use Code16\Sharp\EntityList\EntityListQueryParams;
use Code16\Sharp\Form\Fields\SharpFormSelectField;
use Illuminate\Database\Eloquent\Builder;

class CustomerCountStoreDistance extends EntityCommand
{

    /**
     * @return string
     */
    public function label(): string
    {
        return "Rapikan Jarak";
    }

    public function description(): string
    {
        return "Hitung jarak pelanggan dari Toko";
    }

    public function buildFormFields(): void
    {
        $this->addField(
            SharpFormSelectField::make(
                'store_id',
                Store::pluck("name", "id")->all(),
            )
                ->setLabel('Rapikan Jarak Pelanggan Toko *')
                ->setDisplayAsDropdown()
            // $this->addField(
            //     SharpFormTextareaField::make("message")
            //         ->setLabel("Message")
        );
    }

    public function execute(EntityListQueryParams $params, array $data = []): array
    {

        $helper = new Helper;
        $store_id = $data['store_id'];
        $store_destination = Store::find($store_id);
        $addresses = Address::distinct()
            ->where('store_id', $store_id)
            ->where(function (Builder $subquery) {
                $subquery
                    ->doesntHave('storedistances')
                    ->orWhereHas('storedistances', function (Builder $query) {
                        $query
                            ->where('distance_meter', '<=', 7000)
                            ->whereNull('distance_meter_by_route')
                            //
                        ;
                    })
                    //
                ;
            })
            ->with(['storedistances', 'store'])
            ->get();
        $stores = Store::where('city_id', $store_destination->city_id)->get();
        foreach ($addresses as $address) {
            if (!$address->latlng) continue;
            $new_distance_sync = [];
            foreach ($stores as $store) {
                if (!$store->latlng) continue;
                if ($address->store->city_id !== $store->city_id) continue;
                $storedistance = $address->storedistances->where('pivot.store_id', $store->id)->first();
                $pivot_data = [
                    'distance_meter' => null,
                    'distance_meter_by_route' => null,
                ];
                if ($storedistance) {
                    $pivot_data = [
                        'distance_meter' => $storedistance->pivot->distance_meter,
                        'distance_meter_by_route' => $storedistance->pivot->distance_meter_by_route,
                    ];
                }
                if (!$pivot_data['distance_meter']) {
                    $distance = $helper->getDistance($store->latlng, $address->latlng);
                    $pivot_data['distance_meter'] = round($distance);
                }
                if (!$pivot_data['distance_meter_by_route'] && $pivot_data['distance_meter'] <= 7000) {
                    $distance_by_route = $helper->getDirectionMapbox($store->latlng, $address->latlng);
                    $pivot_data['distance_meter_by_route'] = $distance_by_route;
                }
                if ($storedistance) {
                    if ($storedistance->pivot->distance_meter !== $pivot_data['distance_meter'] || $storedistance->pivot->distance_meter_by_route !== $pivot_data['distance_meter_by_route']) {
                        $address->storedistances()->updateExistingPivot($store->id, $pivot_data);
                    }
                } else {
                    $new_distance_sync[$store->id] = $pivot_data;
                }
            }
            // return $distance_sync;
            // $address->storedistances()->updateExistingPivot($store->id, $pivot_data);
            if (count($new_distance_sync) > 0) {
                $address->storedistances()->syncWithoutDetaching($new_distance_sync);
            }
        }

        return $this->info('Hitung jarak toko berhasil. Silahkan REFRESH halaman.');
    }

    // public function confirmationText()
    // {
    //     return "Tunggu beberapa menit untuk membuat ulang dan merapikan deposit history";
    // }

    public function authorize(): bool
    {
        return sharp_user()->role_id <= 2;
    }
}
