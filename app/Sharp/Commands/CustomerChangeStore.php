<?php

namespace App\Sharp\Commands;

use App\Models\Store;
// use App\Models\Address;
use App\Models\Customer;
// use App\Helper\Helper;
use Code16\Sharp\EntityList\Commands\EntityCommand;
use Code16\Sharp\EntityList\EntityListQueryParams;
use Code16\Sharp\Form\Fields\SharpFormSelectField;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class CustomerChangeStore extends EntityCommand
{

    /**
     * @return string
     */
    public function label(): string
    {
        return "Pindah Toko";
    }

    public function description(): string
    {
        return "Pindah toko 1 alamat";
    }

    public function buildFormFields(): void
    {
        $this->addField(
            SharpFormSelectField::make(
                'store_id',
                Store::pluck("name", "id")->all(),
            )
                ->setLabel('Pindah ke Toko *')
                ->setDisplayAsDropdown()
                ->setClearable()
            // $this->addField(
            //     SharpFormTextareaField::make("message")
            //         ->setLabel("Message")
        );
    }

    // protected function initialData(): array
    // {
    //     return [
    //         "store_id" => 5
    //     ];
    // }

    public function execute(EntityListQueryParams $params, array $data = []): array
    {
        $this->validate($data, [
            "store_id" => "required"
        ]);
        $store_ids = $params->filterFor("stores");
        $stores = Store::whereIn('id', (array)$store_ids)->get();
        $store_destination = Store::find($data['store_id']);
        $distance_store_id = $params->filterFor("distance_store");
        $distance_store = Store::find($distance_store_id);
        $max_distance_store = $params->filterFor("max_distance_store") ? $params->filterFor("max_distance_store") : 3000;

        $customers = Customer::distinct();
        $customers->orderBy($params->sortedBy() ? $params->sortedBy() : 'id', $params->sortedDir() ? $params->sortedDir() : 'desc');
        if ($params->hasSearch()) {
            $words = '';
            foreach ($params->searchWords(false) as $key => $word) {
                if ($key != 0) {
                    $words = $words . ' ';
                }
                $words = $words . $word;
            }
            $words = '%' . $words . '%';
            $customers->where(function ($query) use ($words) {
                $query->whereHas('addresses', function (Builder $subquery) use ($words) {
                    $subquery->where('address', 'like', $words)
                        ->orWhere('latlng', 'like', $words)
                        ->orWhere('label', 'like', $words);
                })
                    ->orWhere('name', 'like', $words)
                    ->orWhere('email', 'like', $words)
                    ->orWhere('phone', 'like', $words);
            });
        }
        if ($params->filterFor("stores")) {
            $ids = $params->filterFor("stores");
            $distance_store_id = $params->filterFor("distance_store");
            $max_distance_store = $params->filterFor("max_distance_store");
            $customers->whereHas('addresses', function (Builder $query) use ($ids, $distance_store_id, $max_distance_store) {
                $query->whereIn('store_id', (array)$ids);
                if ($distance_store_id) {
                    $query->whereHas('storedistances', function (Builder $subquery) use ($distance_store_id, $max_distance_store) {
                        $subquery->where('store_id', $distance_store_id);
                        if ((int)$max_distance_store > 0) {
                            $subquery->where('distance_meter_by_route', "<=", (int)$max_distance_store);
                            // $subquery->where(function ($subsubquery) use ($max_distance_store) {
                            //     $subsubquery
                            //         ->where('distance_meter', "<=", (int)$max_distance_store)
                            //         ->orWhere('distance_meter_by_route', "<=", (int)$max_distance_store);
                            // });
                        }
                    });
                }
            });
        }
        if ($date_created = $params->filterFor("date_created")) {
            // $date_type = $params->filterFor("date_type");
            // if ($date_type == 'custom') {
            $customers->whereBetween("created_at", [
                $date_created['start'],
                $date_created['end'],
            ]);
            // }
        }
        $last_order = $params->filterFor("last_order");

        if ($last_order) {
            $month01 = Carbon::now();
            $month02 = Carbon::now();
            $month03 = Carbon::now();
            $month06 = Carbon::now();
            $month01->subDays(30);
            $month02->subDays(60);
            $month03->subDays(90);
            $month06->subDays(180);
            $latest_order = DB::table('orders')
                ->whereNull('deleted_at')
                ->where(function ($q) {
                    $q->where('status_id', 4)
                        ->orWhere('status_id', 6)
                        ->orWhere('status_id', 7);
                })
                ->select('customer_id', DB::raw('MAX(created_at) as last_order_created_at'))
                ->groupBy('customer_id');
            // $date_type = $params->filterFor("date_type");
            // if ($date_type == 'custom') {
            //     $customers->whereBetween("created_at", [
            //         $date_created['start'],
            //         $date_created['end'],
            //     ]);
            // }
            $customers->joinSub($latest_order, 'latest_order', function ($join) {
                $join->on('customers.id', '=', 'latest_order.customer_id');
            });
            if ($last_order === 'blash_response') {
                $customers
                    ->whereDate('last_order_created_at', '>=', $month01->toDateString())
                    ->whereNotNull('blash_updated_at');
            } else if ($last_order === '<1') {
                $customers->whereDate('last_order_created_at', '>=', $month01->toDateString());
            } else if ($last_order === '>1') {
                $customers->whereDate('last_order_created_at', '<=', $month01->toDateString())
                    ->whereDate('last_order_created_at', '>', $month02->toDateString());
            } else if ($last_order === '>2') {
                $customers->whereDate('last_order_created_at', '<=', $month02->toDateString())
                    ->whereDate('last_order_created_at', '>', $month03->toDateString());
            } else if ($last_order === '>3') {
                $customers->whereDate('last_order_created_at', '<=', $month03->toDateString())
                    ->whereDate('last_order_created_at', '>', $month06->toDateString());
            } else if ($last_order === '>6') {
                $customers->whereDate('last_order_created_at', '<=', $month06->toDateString());
            }
        }
        if ($params->filterFor("custom_filters")) {
            $custom_filters = $params->filterFor("custom_filters");
            if (in_array('empty_coordinate', (array)$custom_filters)) {
                $customers->whereHas('addresses', function (Builder $query) {
                    $query->whereNull('latlng');
                });
            }
            if (in_array('special_price', (array)$custom_filters)) {
                $customers->whereHas('products');
            }
            if (in_array('notif_wa_unset', (array)$custom_filters)) {
                $customers->where('notif_status', 'unset');
            }
            if (in_array('notif_wa_on', (array)$custom_filters)) {
                $customers->where('notif_status', 'on');
            }
            if (in_array('notif_wa_off', (array)$custom_filters)) {
                $customers->where('notif_status', 'off');
            }
            if (in_array('is_company', (array)$custom_filters)) {
                $customers->where('is_company', 1);
            }
            if (in_array('lt2', (array)$custom_filters)) {
                $customers->whereHas('addresses', function (Builder $subquery) {
                    $subquery->where('is_secondfloor', 1);
                });
            }
            if (in_array('duplicated_phone', (array)$custom_filters)) {
                $customers
                    // ->havingRaw('COUNT(phone) > 1')
                    // ->having(DB::raw('COUNT(phone)'), '>', 1)
                    ->whereIn('id', function ($subquery) {
                        $subquery->select('id')->from('customers')->groupBy('phone')->havingRaw('count(*) > 1');
                    });
            }
            if (in_array('blashed', (array)$custom_filters)) {
                $customers->whereNotNull('blash_updated_at');
            }
            if (in_array('unblash', (array)$custom_filters)) {
                $customers->whereNull('blash_updated_at');
            }
        }
        $customers = $customers->get();
        // $customers = $customers->filter(function ($customer) {
        //     return $customer->addresses->count() === 1;
        // });
        return $this->view("sharp.change-store", compact(
            'stores',
            'store_destination',
            'distance_store',
            'max_distance_store',
            'customers',
            'data'
        ));
        // $helper = new Helper;
        // $store_id = $data['store_id'];
        // $addresses = Address::where('store_id', $store_id)->get();
        // $stores = Store::all();

        // foreach ($addresses as $address) {
        //     if (!$address->latlng) continue;
        //     $distance_sync = [];
        //     foreach ($stores as $store) {
        //         if (!$store->latlng) continue;
        //         $distance = $helper->getDistance($store->latlng, $address->latlng);
        //         $pivot_data = [
        //             'distance_meter' => $distance,
        //         ];
        //         $storedistance = $address->storedistances()->where('store_id', $store->id)->first();
        //         if (($storedistance && !$storedistance->pivot->distance_meter_by_route) && $distance <= 7000) {
        //             $distance_meter_by_route = $helper->getDirectionMapbox($store->latlng, $address->latlng);
        //             if ($distance_meter_by_route > 0) {
        //                 $pivot_data['distance_meter_by_route'] = $distance_meter_by_route;
        //             }
        //         }
        //         $distance_sync[$store->id] = $pivot_data;
        //     }
        //     $address->storedistances()->sync($distance_sync);
        // }

        // if ($date_created = $params->filterFor("date_created")) {
        //     $date_type = $params->filterFor("date_type");
        //     if ($date_type == 'custom') {
        //         $customers->whereBetween("created_at", [
        //             $date_created['start'],
        //             $date_created['end'],
        //         ]);
        //     }
        // }

        // if ($params->filterFor("coordinate") == 'empty') {
        //     $customers->whereHas('addresses', function (Builder $query) {
        //         $query->whereNull('latlng');
        //     });
        // }

        // set_time_limit(300); // Extends to 5 minutes.
        // foreach ($customers->get() as $customer) {
        //     $helper->generateDepositHistory($customer->id);
        // }
        // $store = Store::find($store_id);
        // return $this->info('Hitung jarak toko berhasil. Silahkan REFRESH halaman.');
        // return $this->reload();
    }

    // public function confirmationText()
    // {
    //     return "Tunggu beberapa menit untuk membuat ulang dan merapikan deposit history";
    // }

    public function authorize(): bool
    {
        return sharp_user()->role_id <= 2;
    }
}
