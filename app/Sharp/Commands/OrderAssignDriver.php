<?php

namespace App\Sharp\Commands;

use App\Models\Order;
use App\Models\User;
use Code16\Sharp\EntityList\Commands\InstanceCommand;
use Code16\Sharp\Exceptions\Form\SharpApplicativeException;
use Code16\Sharp\Form\Fields\SharpFormSelectField;
use Illuminate\Database\Eloquent\Builder;

// use Code16\Sharp\Form\Fields\SharpFormTextareaField;

// use Illuminate\Support\Arr;

class OrderAssignDriver extends InstanceCommand
{
    public function label(): string
    {
        return "Pilih driver";
    }

    public function description(): string
    {
        return "Kirim notifikasi ke Delivery Man.";
    }

    public function execute($instanceId, array $data = []): array
    {
        $this->validate($data, [
            "driver_id" => "required"
        ]);

        if ($data["driver_id"] == "error") {
            throw new SharpApplicativeException("Driver can't be «error»");
        }

        $order = Order::findOrFail($instanceId);

        $order->update([
                'status_id' => 2, // <PERSON> (assigned)
                'driver_id' => $data["driver_id"]
            ]);

        // Send Notification by Email
        $details = Order::where('id', $instanceId)->with(['products', 'customer', 'address', 'store', 'store.banks'])->first();
        $driver = User::find($data["driver_id"]);
        \Mail::to($driver->email)->send(new \App\Mail\MailAssigned($details));

        // // Send Webpushr
        // $end_point = 'https://api.webpushr.com/v1/notification/send/attribute';
        // $http_header = array(
        //     "Content-Type: Application/Json",
        //     "webpushrKey: " . env('WEBPUSHR_KEY', 'd52766018d2e49e724b9b7da26dab392'),
        //     "webpushrAuthToken: " . env('WEBPUSHR_AUTH_TOKEN', '20775')
        // );
        // $curent_year = date('Y');
        // $curent_month = date('m');
        // $curent_date = date('d');
        // $current_date = $curent_year.$curent_month.$curent_date;
        // $ch = curl_init();
        // curl_setopt($ch, CURLOPT_HTTPHEADER, $http_header);
        // curl_setopt($ch, CURLOPT_URL, $end_point);
        // curl_setopt($ch, CURLOPT_POST, 1);
        // curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        // // Send Notification to Driver
        // $req_data = array(
        //     'title' 		=> "Order Baru 👤 ".$order->customer->name, //required
        //     'message' 		=> "Antar sekarang!", //required
        //     // 'target_url'	=> env('DELIVERY_APP_URL', 'http://delivery.gasplus.online').'/order/'.$order->id, //required
        //     'target_url'	=> env('DELIVERY_APP_URL', 'http://cs2.gasplus.online').'/cs/list/order?filter_status%5B0%5D=1&filter_date='.$current_date.'..'.$current_date.'&page=1', //required
        //     'attribute'		=> array(
        //         'user_id' => $data["driver_id"], // Driver
        //         // 'role_id' => 4, // Admin Toko
        //         // 'store_id' => $store->id
        //     ),
        //     //following parameters are optional
        //     //'name'		=> 'Test campaign',
        //     //'icon'		=> 'https://cdn.webpushr.com/siteassets/wSxoND3TTb.png',
        //     //'image'		=> 'https://cdn.webpushr.com/siteassets/aRB18p3VAZ.jpeg',
        //     //'auto_hide'	=> 1,
        //     //'expire_push'	=> '5m',
        //     //'send_at'		=> '2020-12-28 06:17 +5:30',
        //     //'action_buttons'=> array(
        //         //array('title'=> 'Demo', 'url' => 'https://www.webpushr.com/demo'),
        //         //array('title'=> 'Rates', 'url' => 'https://www.webpushr.com/pricing')
        //     //)
        // );
        // curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($req_data));
        // $response = curl_exec($ch);

        // // Send via WhatsApp
        // if (json_decode($response)->status == 'failure') {
        // }


        return $this->reload();
    }

    public function buildFormFields(): void
    {
        $this->addField(
            SharpFormSelectField::make(
                'driver_id',
                User::whereHas('role', function (Builder $query) {
                    $query->where('title', 'Driver');
                })->pluck("name", "id")->all()
            )
                ->setLabel('Delivery Man *')
                ->setClearable()
                ->setDisplayAsDropdown()
        // $this->addField(
        //     SharpFormTextareaField::make("message")
        //         ->setLabel("Message")
        );
    }

    /**
     * @param $instanceId
     * @return array
     */
    protected function initialData($instanceId): array
    {
        return $this
            // ->setCustomTransformer("message", function ($value, Spaceship $instance) {
            //     return sprintf("%s, message #%s", $instance->name, $instance->messages_sent_count);
            // })
            ->setCustomTransformer("driver_id", function ($value, Order $order) {
                return $order->driver_id;
            })
            ->transform(
                Order::findOrFail($instanceId)
            );
    }

    public function authorizeFor($instanceId): bool
    {
        return auth()->user()->role_id != 5; // except Driver
    }
}
