<?php

namespace App\Sharp\Commands;

// use App\Models\Order;
use App\Helper\Helper;
use App\Models\Socialchat;
use App\Models\NotificationLog;
// use Code16\Sharp\Form\Fields\SharpFormCheckField;
use App\Helper\HelperSocialchat;
use App\Models\NotificationSchedule;
use Code16\Sharp\EntityList\Commands\InstanceCommand;
// use Code16\Sharp\Exceptions\Form\SharpApplicativeException;

// use Code16\Sharp\Form\Fields\SharpFormTextareaField;

// use Illuminate\Support\Arr;

class WbclTestNotif extends InstanceCommand
{
    public function label(): string
    {
        return "Test Notif";
    }

    public function description(): string
    {
        return "Kirim test notifikasi ke Super Admin & Owner.";
    }

    // public function buildFormFields(): void
    // {
    //     $this->addField(
    //         SharpFormCheckField::make("is_start_now", "Ya")
    //                 ->setLabel("Kirim sekarang?")
    //     );
    // }

    public function execute($instanceId, array $data = []): array
    {
        $helper = new Helper();
        // $this->validate($data, [
        //     "is_start_now" => "required"
        // ]);

        // if ($data["is_start_now"] == "error") {
        //     throw new SharpApplicativeException("Driver can't be «error»");
        // }

        $notif_schedule = NotificationSchedule::findOrFail($instanceId);

        // $media = [];
        $media = $notif_schedule->notifreminderimage ? [
            'type' => 'image',
            // 'name' => 'Gasplus.' . explode('.', $notif_schedule->notifreminderimage->file_name)[1],
            // 'mimetype' => $setting->notifreminderimage01->mime_type,
            'url' => explode('?', $notif_schedule->notifreminderimage->thumbnail(500))[0] . '?ik=' . env('CLOUDFLARE_IGNORE_KEY'),
        ] : [];
        $text = $notif_schedule->message_template;
        $text = str_replace('{nama_toko}', 'Gasplus', $text);
        // $text = str_replace('* ', '- ', $text);
        // $text = preg_replace('/(?<!\*)\*(?!\*)/', '_', $text);
        // $text = str_replace('**', '*', $text);
        $helperSocialchat = new HelperSocialchat();
        $conv_id_niko = '66358a5aaf9ef08bd30a96fe';
        $conv_id_pak_yatno = '664b1f6f6a7efbc6f0d0930d';

        $niko = Socialchat::where('conversation_id', $conv_id_niko)->first()->customer;
        $text_niko = str_replace('{nama_pelanggan}', $niko->name, $text);
        $notification_log_niko = NotificationLog::create([
            'notification_schedule_id' => $notif_schedule->id,
            'customer_id' => $niko->id,
            'response' => '{}',
            'message' => '',
        ]);
        $hashed_notification_log_id = $helper->miniEncrypt($notification_log_niko->id);
        $text_niko = preg_replace_callback('/\{link_feedback:([a-z_0-9]+)\}/', function ($matches) use ($hashed_notification_log_id) {
            return url('/fb/' . $hashed_notification_log_id . '/' . $matches[1]);
        }, $text_niko);
        $text_niko = preg_replace_callback('/\{link_feedback_with_input:([a-z_0-9]+)\}/', function ($matches) use ($hashed_notification_log_id) {
            return url('/fb/' . $hashed_notification_log_id . '/' . $matches[1] . '?in=1');
        }, $text_niko);
        $link_unsubscribe = url('/us/' . $hashed_notification_log_id);
        $text_niko = str_replace('{link_unsubscribe}', $link_unsubscribe, $text_niko);

        $pak_yatno = Socialchat::where('conversation_id', $conv_id_pak_yatno)->first()->customer;
        $text_pak_yatno = str_replace('{nama_pelanggan}', $pak_yatno->name, $text);
        $notification_log_pak_yatno = NotificationLog::create([
            'notification_schedule_id' => $notif_schedule->id,
            'customer_id' => $pak_yatno->id,
            'response' => '{}',
            'message' => '',
        ]);
        $hashed_notification_log_id = $helper->miniEncrypt($notification_log_pak_yatno->id);
        $text_pak_yatno = preg_replace_callback('/\{link_feedback:([a-z_0-9]+)\}/', function ($matches) use ($hashed_notification_log_id) {
            return url('/fb/' . $hashed_notification_log_id . '/' . $matches[1]);
        }, $text_pak_yatno);
        $text_pak_yatno = preg_replace_callback('/\{link_feedback_with_input:([a-z_0-9]+)\}/', function ($matches) use ($hashed_notification_log_id) {
            return url('/fb/' . $hashed_notification_log_id . '/' . $matches[1] . '?in=1');
        }, $text_pak_yatno);
        $link_unsubscribe = url('/us/' . $hashed_notification_log_id);
        $text_pak_yatno = str_replace('{link_unsubscribe}', $link_unsubscribe, $text_pak_yatno);

        $is_success_notif_niko = $helperSocialchat->sendMessage($conv_id_niko, $text_niko, $media);
        $message = 'SUCCESS';
        if (empty($is_success_notif_niko->messageId) || empty($is_success_notif_niko->sendAt)) {
            $message = 'FAILED';
        }
        $notification_log_niko->response = $is_success_notif_niko;
        $notification_log_niko->message = $message;
        $notification_log_niko->save();
        $is_success_notif_pak_yatno = $helperSocialchat->sendMessage($conv_id_pak_yatno, $text_pak_yatno, $media);
        $message = 'TEST SUCCESS';
        if (empty($is_success_notif_pak_yatno->messageId) || empty($is_success_notif_pak_yatno->sendAt)) {
            $message = 'TEST FAILED';
        }
        $notification_log_pak_yatno->response = $is_success_notif_niko;
        $notification_log_pak_yatno->message = $message;
        $notification_log_pak_yatno->save();

        // return $this->info("Test Notif berhasil dikirim.");
        return $this->reload();
    }

    public function confirmationText(): string
    {
        return "Kirim test notifikasi ke Owner?";
    }

    public function authorizeFor($instanceId): bool
    {
        return sharp_user()->role_id <= 2;
    }
}