<?php

namespace App\Sharp\Commands;

use App\Models\Order;
use Code16\Sharp\EntityList\Commands\InstanceCommand;
use Code16\Sharp\Exceptions\Form\SharpApplicativeException;
use Code16\Sharp\Form\Fields\SharpFormCheckField;
use Illuminate\Database\Eloquent\Builder;

// use Code16\Sharp\Form\Fields\SharpFormTextareaField;

// use Illuminate\Support\Arr;

class OrderStartJob extends InstanceCommand
{
    public function label(): string
    {
        return "Mulai Job";
    }

    public function description(): string
    {
        return "Kirim order sekarang.";
    }

    // public function buildFormFields(): void
    // {
    //     $this->addField(
    //         SharpFormCheckField::make("is_start_now", "Ya")
    //                 ->setLabel("Kirim sekarang?")
    //     );
    // }

    public function execute($instanceId, array $data = []): array
    {
        // $this->validate($data, [
        //     "is_start_now" => "required"
        // ]);

        // if ($data["is_start_now"] == "error") {
        //     throw new SharpApplicativeException("Driver can't be «error»");
        // }

        $order = Order::findOrFail($instanceId);

        $order->update([
                'status_id' => 3, // set to Job Berjalan
                'driver_id' => auth()->user()->id,
            ]);


        return $this->reload();
    }

    public function confirmationText(): string
    {
        return "Antar order sekarang?";
    }

    public function authorizeFor($instanceId): bool
    {
        $order = Order::find($instanceId);
        return in_array(auth()->user()->role_id, [5, 6]) && $order->status_id == 2; // only Driver && status Job Diambil
    }
}
