<?php

namespace App\Sharp;

use Code16\Sharp\Form\Validator\SharpFormRequest;
use Code16\Sharp\Http\WithSharpContext;

class OperatingCostSharpValidator extends SharpFormRequest
{
    use WithSharpContext;

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'submit_at' => 'required',
            'store_id' => 'required',
            'cost_category_id' => 'required',
            'armada_id' => 'required_if:cost_category_id,5',
            'employee_id' => 'required_if:cost_category_id,1',
            'banks.*.child_cost_category_id' => 'required',
            'banks.*.price' => 'required',
        ];
    }
}
