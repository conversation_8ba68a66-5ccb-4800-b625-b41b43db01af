<?php

namespace App\Sharp;

use App\Models\NotificationLog;
use Code16\Sharp\Utils\LinkToEntity;
use Illuminate\Database\Eloquent\Builder;
use Code16\Sharp\EntityList\SharpEntityList;
use Code16\Sharp\EntityList\EntityListQueryParams;
use App\Sharp\Filters\NotificationLogDateCreatedFilter;
use Code16\Sharp\EntityList\Containers\EntityListDataContainer;

class NotificationLogSharpList extends SharpEntityList
{
    /**
     * Build list containers using ->addDataContainer()
     *
     * @return void
     */
    public function buildListDataContainers()
    {
        $this->addDataContainer(
            EntityListDataContainer::make('created_at')
                ->setLabel('Terkirim')
                ->setSortable()
        )->addDataContainer(
            EntityListDataContainer::make("customer_id")
                ->setLabel("Pelanggan")
                ->setSortable()
        )->addDataContainer(
            EntityListDataContainer::make("message")
                ->setLabel("Status")
                ->setSortable()
        )->addDataContainer(
            EntityListDataContainer::make("feedback_title")
                ->setLabel("Feedback")
                ->setSortable()
        )->addDataContainer(
            EntityListDataContainer::make("feedback_message")
                ->setLabel("Message")
                ->setSortable()
        )->addDataContainer(
            EntityListDataContainer::make("response")
                ->setLabel("Info")
        );
    }

    /**
     * Build list layout using ->addColumn()
     *
     * @return void
     */

    public function buildListLayout()
    {
        $this->addColumn("created_at", 1, 12)
            ->addColumn("customer_id", 2, 12)
            ->addColumn("message", 2, 12)
            ->addColumn("feedback_title", 3, 12)
            ->addColumn("feedback_message", 2, 12)
            ->addColumn("response", 2, 12)
        ;
    }

    /**
     * Build list config
     *
     * @return void
     */
    public function buildListConfig()
    {
        $this->setInstanceIdAttribute('id')
            // ->setSearchable()
            ->setDefaultSort('created_at', 'DESC')
            ->addFilter("date_created", NotificationLogDateCreatedFilter::class)
            // ->addEntityCommand("regenarete", DepositGenerateHistory::class)
            ->setPaginated();
    }

    /**
     * Retrieve all rows data as array.
     *
     * @param EntityListQueryParams $params
     * @return array
     */
    public function getListData(EntityListQueryParams $params)
    {
        $model = NotificationLog::distinct();

        if ($params->sortedBy()) {
            $model->orderBy($params->sortedBy(), $params->sortedDir());
        }

        if ($params->hasSearch()) {
            $words = '';
            foreach ($params->searchWords(false) as $key => $word) {
                if ($key != 0) {
                    $words = $words . ' ';
                }
                $words = $words . $word;
            }
            $words = '%' . $words . '%';
            $model->where(function ($query) use ($words) {
                $query->whereHas('customer', function (Builder $subquery) use ($words) {
                    $subquery
                        ->where('name', 'like', $words)
                        ->where('phone', 'like', $words)
                    ;
                })
                    ->orWhere('message', 'like', $words)
                ;
            });
        }

        if ($notification_schedule_id = $params->filterFor("notification_schedule_id")) {
            $model->where("notification_schedule_id", $notification_schedule_id);
        }

        if ($date_created = $params->filterFor("date_created")) {
            $model->whereBetween("created_at", [
                $date_created['start'],
                $date_created['end'],
            ]);
        }

        return $this
            ->setCustomTransformer("created_at", function ($value, $model) {
                return '<span style="font-size: 12px !important;">' . $model->created_at->format('jMy') . '</span>' . '<br><span style="font-size: 12px !important; opacity: 0.6;">' . $model->created_at->format('G:i') . '</span>';
            })
            ->setCustomTransformer("customer_id", function ($val, $model) {
                $name = $model->customer ? (new LinkToEntity($model->customer->name, "customer"))
                    ->setTooltip("See related Customer")
                    ->toShowOfInstance($model->customer)
                    ->render() : '';
                // $name = $model->customer ? '<div style="font-size: 12px !important;">' . $model->customer->name . '</div>' : '';
                $phone = $model->customer ? '<div style="font-size: 12px !important; opacity: 0.6;">' . $model->customer->phone . '</div>' : '';
                return $name . $phone;
            })
            ->setCustomTransformer("feedback_title", function ($val, $model) {
                $feedback_key = str_replace(['_', '-'], ' ', $val);
                return ucfirst($feedback_key);
            })
            ->setCustomTransformer("response", function ($val, $model) {
                $senderId = isset($model->response['senderId']) ? explode(':', $model->response['senderId'])[0] : '';
                $sender = $senderId ? '<div style="font-size: 10px !important;"><strong style="font-size: 10px !important;">SENDER: </strong>' . $senderId . '</div>' : '';
                return $sender;
            })
            ->transform($model->with(['customer'])->paginate(30));
    }
}
