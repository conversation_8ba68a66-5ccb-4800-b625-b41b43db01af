<?php

namespace App\Sharp;

use App\Models\Setting;
use Code16\Sharp\Form\SharpSingleForm;
// use App\Sharp\CustomFormFields\SharpFormCustomTextField;
// use Code16\Sharp\Form\Fields\SharpFormDateField;
use Code16\Sharp\Form\Layout\FormLayoutTab;
use Code16\Sharp\Form\Layout\FormLayoutColumn;
use Code16\Sharp\Form\Fields\SharpFormUploadField;
use Code16\Sharp\Form\Fields\SharpFormMarkdownField;
use Code16\Sharp\Form\Eloquent\WithSharpFormEloquentUpdater;
use Code16\Sharp\Form\Eloquent\Transformers\FormUploadModelTransformer;
// use App\Helper\Helper;

// use App\Sharp\CustomFormFields\SharpFormTextCustomField;

class SettingSharpForm extends SharpSingleForm
{
	use WithSharpFormEloquentUpdater;

	public function buildFormFields(): void
	{
		$this
			->addField(
				SharpFormMarkdownField::make("notifremindermessage01")
					->setLabel('Message Notif Reminder 01')
					->setToolbar([
						SharpFormMarkdownField::B,
						SharpFormMarkdownField::I,
						SharpFormMarkdownField::UL,
						SharpFormMarkdownField::OL,
					])
					->setHelpMessage('Gunakan tag {{nama_pelanggan}} untuk menampilkan nama pelanggan. Gunakan tag {{nama_toko}} untuk menampilkan nama toko.')
			)
			->addField(
				SharpFormMarkdownField::make("notifremindermessage02")
					->setLabel('Message Notif Reminder 02')
					->setToolbar([
						SharpFormMarkdownField::B,
						SharpFormMarkdownField::I,
						SharpFormMarkdownField::UL,
						SharpFormMarkdownField::OL,
					])
					->setHelpMessage('Gunakan tag {{nama_pelanggan}} untuk menampilkan nama pelanggan. Gunakan tag {{nama_toko}} untuk menampilkan nama toko.')
			)
			->addField(
				SharpFormMarkdownField::make("notifremindermessage03")
					->setLabel('Message Notif Reminder 03')
					->setToolbar([
						SharpFormMarkdownField::B,
						SharpFormMarkdownField::I,
						SharpFormMarkdownField::UL,
						SharpFormMarkdownField::OL,
					])
					->setHelpMessage('Gunakan tag {{nama_pelanggan}} untuk menampilkan nama pelanggan. Gunakan tag {{nama_toko}} untuk menampilkan nama toko.')
			)
			->addField(
				SharpFormUploadField::make("notifreminderimage01")
					->setLabel("Image Notif Reminder 01")
					->setFileFilterImages()
					->shouldOptimizeImage()
					// ->setCompactThumbnail()
					// ->setCropRatio("1:1", ["jpg","jpeg","png"])
					->setStorageDisk("public")
					->setStorageBasePath("img/notif")
			)
			->addField(
				SharpFormUploadField::make("notifreminderimage02")
					->setLabel("Image Notif Reminder 02")
					->setFileFilterImages()
					->shouldOptimizeImage()
					// ->setCompactThumbnail()
					// ->setCropRatio("1:1", ["jpg","jpeg","png"])
					->setStorageDisk("public")
					->setStorageBasePath("img/notif")
			)
			->addField(
				SharpFormUploadField::make("notifreminderimage03")
					->setLabel("Image Notif Reminder 03")
					->setFileFilterImages()
					->shouldOptimizeImage()
					// ->setCompactThumbnail()
					// ->setCropRatio("1:1", ["jpg","jpeg","png"])
					->setStorageDisk("public")
					->setStorageBasePath("img/notif")
			)
		;
	}

	public function buildFormLayout(): void
	{
		$this->addTab('Notif Reminder', function (FormLayoutTab $tab) {
			$tab->addColumn(6, function (FormLayoutColumn $column) {
				$column
					// ->withSingleField('youtube_api_key')
					->withSingleField('notifremindermessage01')
					->withSingleField('notifreminderimage01')
					->withSingleField('notifremindermessage02')
					->withSingleField('notifreminderimage02')
					->withSingleField('notifremindermessage03')
					->withSingleField('notifreminderimage03')
				;
			});
		});
	}

	protected function findSingle()
	{
		$setting = Setting::where('name', 'settings')->with([
			'notifreminderimage01',
			'notifreminderimage02',
			'notifreminderimage03',
		])->first();
		if (!$setting) {
			$setting = Setting::create([
				'name' => 'settings',
				'value' => [
					'notifremindermessage01' => '',
					'notifremindermessage02' => '',
					'notifremindermessage03' => '',
				],
			]);
		}
		$setting->notifremindermessage01 = $setting->value['notifremindermessage01'] ?? null;
		$setting->notifremindermessage02 = $setting->value['notifremindermessage02'] ?? null;
		$setting->notifremindermessage03 = $setting->value['notifremindermessage03'] ?? null;
		// $data = $setting->value;
		// $data['notifreminderimage01'] = $setting->notifreminderimage01 ?? null;
		return $this
			->setCustomTransformer(
				"notifreminderimage01",
				new FormUploadModelTransformer()
			)
			->setCustomTransformer(
				"notifreminderimage02",
				new FormUploadModelTransformer()
			)
			->setCustomTransformer(
				"notifreminderimage03",
				new FormUploadModelTransformer()
			)
			->transform($setting);
	}

	protected function updateSingle(array $data)
	{
		$setting = Setting::where('name', 'settings')->first();
		$setting->value = $data;
		// $setting->save();
		$this->ignore([
			'notifremindermessage01',
			'notifremindermessage02',
			'notifremindermessage03',
		])->save($setting, $data);
		$this->notify('Data berhasil disimpan!')->setLevelSuccess();
	}
}
