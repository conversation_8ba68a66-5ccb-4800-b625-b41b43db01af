<?php

namespace App\Sharp;

use Code16\Sharp\Form\Validator\SharpFormRequest;

class SettingSharpValidator extends SharpFormRequest
{
	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			// 'link_url_chat_admin' => ['url', 'nullable'],
			// 'video_log_interval_seconds' => ['numeric', 'nullable'],
			// 'link_btn_hide_title' => ['required'],
			// 'promo_delay' => ['required'],
			// 'email' => ['required', 'email', 'unique:users,email,' . auth()->user()->id],
		];
	}
}
