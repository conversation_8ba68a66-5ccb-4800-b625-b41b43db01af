<?php

namespace App\Sharp;

use App\Models\Employee;
use App\Models\User;
use App\Models\Armada;
use Code16\Sharp\Form\Eloquent\WithSharpFormEloquentUpdater;
use Code16\Sharp\Form\Fields\SharpFormHtmlField;
use Code16\Sharp\Form\Fields\SharpFormTextField;
use Code16\Sharp\Form\Fields\SharpFormSelectField;
use Code16\Sharp\Form\Fields\SharpFormCheckField;
use Code16\Sharp\Form\Fields\SharpFormTextareaField;
use Code16\Sharp\Form\Fields\SharpFormDateField;
use Code16\Sharp\Form\Fields\SharpFormUploadField;
use Code16\Sharp\Form\Fields\SharpFormListField;
use Code16\Sharp\Form\Layout\FormLayoutColumn;
use Code16\Sharp\Form\SharpForm;
use Code16\Sharp\Http\WithSharpContext;
use App\Sharp\CustomFormFields\SharpFormTextCustomField;
use Code16\Sharp\Form\Eloquent\Transformers\FormUploadModelTransformer;

class EmployeeSharpForm extends SharpForm
{
    use WithSharpFormEloquentUpdater, WithSharpContext;

    /**
     * Retrieve a Model for the form and pack all its data as JSON.
     *
     * @param $id
     * @return array
     */
    public function find($id): array
    {
        return $this
            ->setCustomTransformer("userstamp", function ($value, $model) {
                return [
                    "created_by" => $model->creator ? $model->creator->name : 'System',
                    "created_at" => $model->created_at ? $model->created_at->format('jMy(G:i)') : '-',
                    "updated_by" => $model->editor ? $model->editor->name : 'System',
                    "updated_at" => $model->updated_at ? $model->updated_at->format('jMy(G:i)') : '-',
                ];
            })
            ->setCustomTransformer("armada", function ($value, $model) {
                return $value ? $value["id"] : $value;
            })
            ->setCustomTransformer(
                "profilephoto",
                new FormUploadModelTransformer()
            )
            ->setCustomTransformer(
                "kkphoto",
                new FormUploadModelTransformer()
            )
            ->setCustomTransformer(
                "ktpphoto",
                new FormUploadModelTransformer()
            )
            ->setCustomTransformer(
                "simphoto",
                new FormUploadModelTransformer()
            )
            ->transform(
                Employee::with([
                    'armada',
                    'user',
                    'profilephoto',
                    'kkphoto',
                    'ktpphoto',
                    'simphoto',
                ])->findOrFail($id)
            );
    }

    // public function create(): array
    // {
    //     return $this->transform(new Employee([
    //         "user_id" => $_GET['user_id'],
    //     ]));
    // }

    /**
     * @param $id
     * @param array $data
     * @return mixed the instance id
     */
    public function update($id, array $data)
    {
        $model = $id ? Employee::findOrFail($id) : new Employee;

        $old_armada = null;
        $new_armada = null;
        if ($model->armada) {
            $old_armada = $model->armada;
        }
        if (isset($data["armada"])) {
            $new_armada = Armada::find($data["armada"]);
        }

        $model = $this->ignore(['userstamp', 'armada'])->save($model, $data);

        // Unset Old Armada
        if (($old_armada && !$new_armada) || ($old_armada && $new_armada && $old_armada->id !== $new_armada->id)) {
            $old_armada->update([
                'employee_id' => null
            ]);
        }
        // Set New Armada
        if ($new_armada) {
            $new_armada->update([
                'employee_id' => $model->id
            ]);
        }
    }

    /**
     * @param $id
     */
    public function delete($id)
    {
        Employee::findOrFail($id)->find($id)->delete();
    }

    /**
     * Build form fields using ->addField()
     *
     * @return void
     */
    public function buildFormFields()
    {

        $this
            ->addField(
                SharpFormSelectField::make(
                    "user_id",
                    User::whereIn('role_id', ['4', '5', '6'])->orderBy("name")->pluck("name", "id")->toArray()
                )
                    ->setDisplayAsDropdown()
                    ->setClearable()
                    ->setLabel("Akun *")
                // ->setCreatable(true)
                // ->setCreateAttribute("name")
                // ->setMaxTagCount(4)
            )
            ->addField(
                SharpFormTextField::make('full_name')
                    ->setLabel('Nama Lengkap *')
            )
            ->addField(
                SharpFormDateField::make("dob")
                    ->setLabel("Tanggal Lahir")
            )
            ->addField(
                SharpFormTextareaField::make('address')
                    ->setLabel('Alamat')
                    ->setRowCount(2)
            )
            ->addField(
                SharpFormTextField::make('kk_number')
                    ->setLabel('Nomor KK')
            )
            ->addField(
                SharpFormTextField::make('ktp_number')
                    ->setLabel('Nomor KTP (NIK)')
            )
            ->addField(
                SharpFormTextField::make('sim_number')
                    ->setLabel('Nomor SIM')
            )
            ->addField(
                SharpFormDateField::make("sim_valid_until")
                    ->setLabel("SIM Berlaku Sampai")
            )
            ->addField(
                SharpFormDateField::make("start_work_at")
                    ->setLabel("Tanggal Mulai Bekerja")
            )
            ->addField(
                SharpFormDateField::make("quit_work_at")
                    ->setLabel("Tanggal Berhenti Bekerja")
            )
            ->addField(
                SharpFormTextareaField::make('quit_note')
                    ->setLabel('Alasan Berhenti Bekerja')
                    ->setRowCount(2)
            )
            ->addField(
                SharpFormSelectField::make(
                    "armada",
                    // [],
                    Armada::pluck("licence_number", "id")->toArray()
                )
                    ->setLabel("Armada")
                    ->setDisplayAsDropdown()
                    ->setClearable()
                // ->setMultiple()
                // ->setDisplayAsList()
                // ->addConditionalDisplay('role_id', ['3']) // admin toko / driver
                // ->setInline()
                // ->setCreatable(true)
                // ->setCreateAttribute("name")
                // ->setMaxSelected(1)
            )
            ->addField(
                SharpFormTextareaField::make('note')
                    ->setLabel('Catatan')
                    ->setRowCount(2)
            )
            ->addField(
                SharpFormUploadField::make("profilephoto")
                    ->setLabel("Foto Selfie")
                    ->setFileFilterImages()
                    ->shouldOptimizeImage()
                    // ->setCompactThumbnail()
                    // ->setCropRatio("1:1", ["jpg","jpeg","png"])
                    ->setStorageDisk("public")
                    ->setStorageBasePath("img/sdm/selfie")
            )
            ->addField(
                SharpFormUploadField::make("ktpphoto")
                    ->setLabel("Foto KTP")
                    ->setFileFilterImages()
                    ->shouldOptimizeImage()
                    // ->setCompactThumbnail()
                    // ->setCropRatio("1:1", ["jpg","jpeg","png"])
                    ->setStorageDisk("public")
                    ->setStorageBasePath("img/sdm/ktp")
            )
            ->addField(
                SharpFormUploadField::make("kkphoto")
                    ->setLabel("Foto KK")
                    ->setFileFilterImages()
                    ->shouldOptimizeImage()
                    // ->setCompactThumbnail()
                    // ->setCropRatio("1:1", ["jpg","jpeg","png"])
                    ->setStorageDisk("public")
                    ->setStorageBasePath("img/sdm/kk")
            )
            ->addField(
                SharpFormUploadField::make("simphoto")
                    ->setLabel("Foto SIM")
                    ->setFileFilterImages()
                    ->shouldOptimizeImage()
                    // ->setCompactThumbnail()
                    // ->setCropRatio("1:1", ["jpg","jpeg","png"])
                    ->setStorageDisk("public")
                    ->setStorageBasePath("img/sdm/sim")
            )->addField(
                SharpFormHtmlField::make('userstamp')
                    ->setTemplatePath("/sharp/templates/userstamp.vue")
            );
    }

    /**
     * Build form layout using ->addTab() or ->addColumn()
     *
     * @return void
     */
    public function buildFormLayout()
    {
        $this->addColumn(6, function (FormLayoutColumn $column) {
            $column
                ->withSingleField("user_id")
                ->withSingleField("full_name")
                ->withSingleField("dob")
                ->withSingleField("address")
                ->withSingleField("profilephoto")
                ->withSingleField("kkphoto")
                ->withSingleField("kk_number")
                ->withSingleField("ktpphoto")
                ->withSingleField("ktp_number")
                ->withSingleField("simphoto")
                ->withSingleField("sim_number")
                ->withSingleField("sim_valid_until")
                ->withSingleField("start_work_at")
                ->withSingleField("quit_work_at")
                ->withSingleField("quit_note")
                ->withSingleField("note")
                ->withSingleField("armada");
            // ->withSingleField("notification_is_active")
            // ->withSingleField("subscriber_id");
        })->addColumn(12, function (FormLayoutColumn $column) {
            $column->withSingleField('userstamp');
        });
    }
}
