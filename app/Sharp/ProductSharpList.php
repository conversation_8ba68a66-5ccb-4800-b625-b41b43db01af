<?php

namespace App\Sharp;

use App\Models\Product;
use Code16\Sharp\EntityList\Containers\EntityListDataContainer;
use Code16\Sharp\EntityList\EntityListQueryParams;
use Code16\Sharp\EntityList\SharpEntityList;
use Code16\Sharp\Utils\LinkToEntity;
use App\Sharp\Filters\ProductCategoryFilter;
use Illuminate\Database\Eloquent\Builder;
use Code16\Sharp\EntityList\Eloquent\Transformers\SharpUploadModelAttributeTransformer;

class ProductSharpList extends SharpEntityList
{
    /**
     * Build list containers using ->addDataContainer()
     *
     * @return void
     */
    public function buildListDataContainers()
    {
        $this->addDataContainer(
            EntityListDataContainer::make("featurephoto")
                ->setLabel("")
        )->addDataContainer(
            EntityListDataContainer::make('code')
                ->setLabel('Kode')
                ->setSortable()
            // ->setHtml()
        )->addDataContainer(
            EntityListDataContainer::make("name")
                ->setSortable()
                ->setLabel("Nama")
        )->addDataContainer(
            EntityListDataContainer::make("categories")
                //   ->setSortable()
                ->setLabel("Kategori")
        )->addDataContainer(
            EntityListDataContainer::make("price")
                ->setSortable()
                ->setLabel("Harga Master")
        )->addDataContainer(
            EntityListDataContainer::make("sort_order")
                ->setSortable()
                ->setLabel("Urutan")
        );
    }

    /**
     * Build list layout using ->addColumn()
     *
     * @return void
     */

    public function buildListLayout()
    {
        $this->addColumn("featurephoto", 1, 12)
            ->addColumn("code", 2, 12)
            ->addColumn("name", 3, 12)
            ->addColumn("categories", 2, 12)
            ->addColumn("price", 2, 12)
            ->addColumn("sort_order", 2, 12);
    }

    /**
     * Build list config
     *
     * @return void
     */
    public function buildListConfig()
    {
        $this->setInstanceIdAttribute('id')
            ->setSearchable()
            ->setDefaultSort('sort_order', 'asc')
            ->setPaginated()
            ->addFilter("categories", ProductCategoryFilter::class)
            ->setReorderable(ProductSharpReorderHandler::class);
    }

    /**
     * Retrieve all rows data as array.
     *
     * @param EntityListQueryParams $params
     * @return array
     */
    public function getListData(EntityListQueryParams $params)
    {
        $products = Product::with(['categories']);

        if ($params->sortedBy()) {
            $products->orderBy($params->sortedBy(), $params->sortedDir());
        }

        if ($params->hasSearch()) {
            $words = '';
            foreach ($params->searchWords(false) as $key => $word) {
                if ($key != 0) {
                    $words = $words . ' ';
                }
                $words = $words . $word;
            }
            $words = '%' . $words . '%';
            $products->whereHas('categories', function (Builder $query) use ($words) {
                $query->where('name', 'like', $words);
            })
                ->orWhere('name', 'like', $words)
                ->orWhere('code', 'like', $words)
                ->orWhere('slug', 'like', $words)
                ->orWhere('price', 'like', $words);
        }

        if ($params->filterFor("categories")) {
            $ids = $params->filterFor("categories");
            $products->whereHas('categories', function (Builder $query) use ($ids) {
                $query->whereIn('id', (array)$ids);
            });
        }

        return $this
            ->setCustomTransformer(
                "featurephoto",
                new SharpUploadModelAttributeTransformer(30, 30, ["fit" => ["w" => 30, "h" => 30]])
            )
            ->setCustomTransformer("code", function ($value, $model) {
                $accurate = $model->accurate_item_id ? '<br><strong style="font-size: 10px !important; color: purple;">Accurate: ' . $model->accurate_item_id . ' / ' . $model->accurate_item_no . '</strong>' : '';
                return $value . $accurate;
            })
            ->setCustomTransformer("price", function ($value, $model) {
                $info = '';
                $link_iak_price = '';
                if ($model->categories->contains('slug', 'ppob')) {
                    $info = '<div style="font-size: 10px !important; color: gray;">' . 'Margin Master Gasplus' . '</div>';
                    $link_iak_price = '<a href="' . route('iak.pricelist', ['product_id' => $model->id]) . '" target="_blank" style="font-size: 12px !important; font-weight: bold;">Lihat harga IAK ↗️</a>';
                }
                return 'Rp' . number_format($value, 0, '.', '.') . $info . $link_iak_price;
            })
            ->setCustomTransformer("categories", function ($value, $product) {
                return $product->categories->map(function ($category) {
                    // return $category->title;
                    return (new LinkToEntity($category->name, "category"))
                        ->setTooltip("See related category")
                        // ->setSearch($category->name)
                        ->toFormOfInstance($category)
                        ->render();
                })->implode(" | ");
                // });
            })
            ->transform($products->withCount(["categories"])->paginate(30));
    }
}
