<?php

namespace App\Sharp;

use Code16\Sharp\Form\Validator\SharpFormRequest;
use Code16\Sharp\Http\WithSharpContext;

class StatusSharpValidator extends SharpFormRequest
{
    use WithSharpContext;

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => [
                'required',
                $this->context()->instanceId() ? 'unique:statuses,name,'.$this->context()->instanceId() : 'unique:statuses',
            ],
            'code' => [
                'required',
                $this->context()->instanceId() ? 'unique:statuses,slug,'.$this->context()->instanceId() : 'unique:statuses',
            ],
            'color' => [
                'required',
            ],
            'icon' => [
                'required',
            ],
        ];
    }
}
