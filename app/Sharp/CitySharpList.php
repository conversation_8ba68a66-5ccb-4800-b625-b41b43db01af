<?php

namespace App\Sharp;

use App\Models\City;
use Code16\Sharp\EntityList\Containers\EntityListDataContainer;
use Code16\Sharp\EntityList\EntityListQueryParams;
use Code16\Sharp\EntityList\SharpEntityList;

class CitySharpList extends SharpEntityList
{
    /**
    * Build list containers using ->addDataContainer()
    *
    * @return void
    */
    public function buildListDataContainers()
    {
        $this->addDataContainer(
            EntityListDataContainer::make('name')
                ->setLabel('Nama Kota')
                ->setSortable()
                // ->setHtml()
        )->addDataContainer(
            EntityListDataContainer::make("slug")
              ->setSortable()
              ->setLabel("Slug")
        )->addDataContainer(
            EntityListDataContainer::make("stores_count")
              ->setSortable()
              ->setLabel("Jumlah Toko")
        )->addDataContainer(
            EntityListDataContainer::make("sort_order")
              ->setSortable()
              ->setLabel("Urutan")
        );
    }

    /**
    * Build list layout using ->addColumn()
    *
    * @return void
    */

    public function buildListLayout()
    {
        $this->addColumn("name", 4, 12)
          ->addColumn("slug", 4, 12)
          ->addColumn("stores_count", 2, 12)
          ->addColumn("sort_order", 2, 12);
    }

    /**
    * Build list config
    *
    * @return void
    */
    public function buildListConfig()
    {
        $this->setInstanceIdAttribute('id')
            ->setSearchable()
            ->setDefaultSort('sort_order', 'asc')
            ->setPaginated()
            ->setReorderable(CitySharpReorderHandler::class);
    }

    /**
    * Retrieve all rows data as array.
    *
    * @param EntityListQueryParams $params
    * @return array
    */
    public function getListData(EntityListQueryParams $params)
    {
        $cities = City::distinct();

        if ($params->sortedBy()) {
            $cities->orderBy($params->sortedBy(), $params->sortedDir());
        }

        if ($params->hasSearch()) {
            foreach ($params->searchWords() as $word) {
                $cities->where('name', 'like', $word)
                  ->orWhere('slug', 'like', $word);
            }
        }

        return $this
          // ->setCustomTransformer("spaceship", function ($value, $travel) {
          //     return '<i class="fas fa-space-shuttle"></i> ' . $travel->spaceship->name;
          // })
          ->transform($cities->withCount(["stores"])->paginate(30));
    }
}
