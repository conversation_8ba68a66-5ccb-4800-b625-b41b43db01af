<?php

namespace App\Sharp;

use Code16\Sharp\Http\WithSharpContext;
use App\Helper\Helper;
use App\Models\Product;
use App\Models\Category;
use App\Models\Store;
use Code16\Sharp\Form\Eloquent\WithSharpFormEloquentUpdater;
use Code16\Sharp\Form\Fields\SharpFormHtmlField;
use Code16\Sharp\Form\Fields\SharpFormTextField;
use Code16\Sharp\Form\Fields\SharpFormNumberField;
use Code16\Sharp\Form\Fields\SharpFormTagsField;
use Code16\Sharp\Form\Fields\SharpFormSelectField;
use Code16\Sharp\Form\Fields\SharpFormUploadField;
use Code16\Sharp\Form\Fields\SharpFormListField;
use Code16\Sharp\Form\Layout\FormLayoutColumn;
use Code16\Sharp\Form\SharpForm;
use App\Sharp\CustomFormFields\SharpFormTextCustomField;
use Code16\Sharp\Form\Eloquent\Transformers\FormUploadModelTransformer;
use Code16\Sharp\Form\Eloquent\Uploads\Transformers\SharpUploadModelFormAttributeTransformer;

class ProductSharpForm extends SharpForm
{
    use WithSharpFormEloquentUpdater, WithSharpContext;

    /**
     * Retrieve a Model for the form and pack all its data as JSON.
     *
     * @param $id
     * @return array
     */
    public function find($id): array
    {
        // return $this->transform(
        //     Product::findOrFail($id)
        // );
        return $this->setCustomTransformer("userstamp", function ($value, $user) {
            return [
                "created_by" => $user->creator ? $user->creator->name : 'System',
                "created_at" => $user->created_at ? $user->created_at->format('jMy(G:i)') : '-',
                "updated_by" => $user->editor ? $user->editor->name : 'System',
                "updated_at" => $user->updated_at ? $user->updated_at->format('jMy(G:i)') : '-',
            ];
        })->setCustomTransformer(
            "featurephoto",
            new FormUploadModelTransformer()
        )->setCustomTransformer(
            "photos",
            new SharpUploadModelFormAttributeTransformer()
        )->transform(
            Product::with(['categories', 'stores', "featurephoto", "photos"])->findOrFail($id)
        );
    }

    public function create(): array
    {
        return $this->transform(new Product([
            "minimum_order" => 1
        ]));
    }

    /**
     * @param $id
     * @param array $data
     * @return mixed the instance id
     */
    public function update($id, array $data)
    {
        $product = $id ? Product::findOrFail($id) : new Product;

        function slugify($text)
        {
            // replace non letter or digits by -
            $text = preg_replace('~[^\pL\d]+~u', '-', $text);
            // transliterate
            $text = iconv('utf-8', 'us-ascii//TRANSLIT', $text);
            // remove unwanted characters
            $text = preg_replace('~[^-\w]+~', '', $text);
            // trim
            $text = trim($text, '-');
            // remove duplicate -
            $text = preg_replace('~-+~', '-', $text);
            // lowercase
            $text = strtolower($text);
            if (empty($text)) {
                return 'n-a';
            }
            return $text;
        }

        if (!$product || ($product && in_array($product->slug, [
            'emoney',
            'air-pdam',
            'pln-token-listrik',
        ]))) {
            $data['slug'] = slugify($data['name']);
        }

        // ? Sync with accurate
        $helper = new Helper;
        $params = [
            'no' => $data['code'],
            // 'sp.page' => $page,
            // 'sp.pageSize' => 1000,
        ];
        $body = [];
        $result_item = $helper->fetchApiAccurate('/accurate/api/item/detail.do', 'GET', $params, $body);
        if (is_object($result_item) && $result_item->s) {
            $item = $result_item->d;
            $data['accurate_item_id'] = $item->id;
            $data['accurate_item_no'] = $item->no;
        }

        $this->ignore(['userstamp'])->save($product, $data);
        $detail = Helper::revalidateBelanjaKantor();
        $detail = Helper::revalidateFrontEnd();
        $this->notify('Data berhasil disimpan!')
            // ->setDetail($detail)
            ->setLevelSuccess();
    }

    /**
     * @param $id
     */
    public function delete($id)
    {
        Product::findOrFail($id)->find($id)->delete();
    }

    /**
     * Build form fields using ->addField()
     *
     * @return void
     */
    public function buildFormFields()
    {
        $product = Product::find($this->context()->instanceId());
        $is_ppob = $product ? $product->is_ppob : false;

        $this->addField(
            SharpFormTextField::make('code')
                ->setLabel('Kode Produk *')
                ->setHelpMessage("Samakan dengan code/no item di Accurate.id")
                ->setReadOnly($is_ppob)
        )->addField(
            SharpFormTextField::make('name')
                ->setLabel('Nama Produk *')
            // )->addField(
            //     SharpFormTextCustomField::make("slug")
            //         ->setLabel("Slug *")
            //         ->setPrepend("https://gasplus.online/toko/palagan/produk/")
            //         ->setSlugify()
        )->addField(
            SharpFormTextCustomField::make("price")
                ->setLabel($is_ppob ? "Harga Margin *" : "Harga Master *")
                // ->setPrepend("Rp")
                ->setInputType('money')
        )->addField(
            SharpFormNumberField::make("minimum_order")
                ->setLabel("Minimal Order *")
                ->setMin(1)
                ->setShowControls(false)
        )->addField(
            SharpFormTextField::make('unit')
                ->setLabel('Satuan Unit *')
        )->addField(
            SharpFormTextField::make('link')
                ->setLabel('Link URL')
                ->setPlaceholder("https://www.instagram.com/12345")
        )->addField(
            SharpFormTagsField::make(
                "categories",
                Category::pluck("name", "id")->all()
            )
                ->setLabel("Kategori")
                ->setReadOnly($is_ppob)
            // ->setCreatable(true)
            // ->setCreateAttribute("name")
            // ->setMaxTagCount(4)
        )->addField(
            SharpFormSelectField::make(
                "stores",
                Store::pluck("name", "id")->all()
            )
                ->setLabel("Tersedia di Toko")
                ->setMultiple()
                ->setDisplayAsList()
            // ->setInline()
            // ->setCreatable(true)
            // ->setCreateAttribute("name")
            // ->setMaxTagCount(4)
        )->addField(
            SharpFormUploadField::make("featurephoto")
                ->setLabel("Foto Utama")
                ->setFileFilterImages()
                ->shouldOptimizeImage()
                // ->setCompactThumbnail()
                // ->setCropRatio("1:1", ["jpg","jpeg","png"])
                ->setStorageDisk("public")
                ->setStorageBasePath("img/products")
        )->addField(
            SharpFormListField::make("photos")
                ->setLabel("Foto Lain")
                ->setAddText("Tambah Foto")
                ->setAddable()
                ->setRemovable()
                ->setSortable(true)
                ->setItemIdAttribute("id")
                ->setOrderAttribute("sort_order")
                ->addItemField(
                    SharpFormUploadField::make("file")
                        ->setFileFilterImages()
                        ->shouldOptimizeImage()
                        // ->setCompactThumbnail()
                        // ->setCropRatio("16:9")
                        ->setStorageDisk("public")
                        ->setStorageBasePath("img/products/others")
                )
        )->addField(
            SharpFormHtmlField::make('userstamp')
                ->setTemplatePath("/sharp/templates/userstamp.vue")
        );
    }

    /**
     * Build form layout using ->addTab() or ->addColumn()
     *
     * @return void
     */
    public function buildFormLayout()
    {
        $product = Product::find($this->context()->instanceId());
        $is_ppob = $product ? $product->is_ppob : false;

        $this->addColumn(8, function (FormLayoutColumn $column) use ($is_ppob) {
            $column->withSingleField('code')
                ->withSingleField("name")
                // ->withSingleField("slug")
                ->withSingleField("price");

            if (!$is_ppob) {
                $column->withFields("minimum_order|6", "unit|6");
            }
            $column->withSingleField("link")
                ->withSingleField("categories")
                ->withSingleField("stores");
        })->addColumn(4, function (FormLayoutColumn $column) {
            $column->withSingleField("featurephoto")
                ->withSingleField("photos", function (FormLayoutColumn $listItem) {
                    $listItem->withSingleField("file");
                });
        })->addColumn(12, function (FormLayoutColumn $column) {
            $column->withSingleField('userstamp');
        });
    }
}
