<?php

namespace App\Sharp;

use Code16\Sharp\Form\Validator\SharpFormRequest;
use Code16\Sharp\Http\WithSharpContext;
use Illuminate\Validation\Rule;

class UserSharpValidator extends SharpFormRequest
{
    use WithSharpContext;

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name'     => [
                'required',
            ],
            'email'    => [
                'email',
                'required',
                $this->context()->instanceId() ? 'unique:users,email,' . $this->context()->instanceId() : 'unique:users',
            ],
            'phone'    => [
                'required',
                // $this->context()->instanceId() ? 'unique:users,phone,'.$this->context()->instanceId() : 'unique:users',
            ],
            'password' => [
                $this->context()->isCreation() ? 'required' : null,
            ],
            // 'roles.*'  => [
            //     'integer',
            // ],
            'role_id'    => [
                'required',
            ],
            'store_id'    => 'required_if:role_id,5', // admin toko / driver
            'stores'    => 'required_if:role_id,3,4,6' // admin toko / driver
        ];
    }
}
