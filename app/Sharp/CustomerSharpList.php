<?php

namespace App\Sharp;

use App\Models\Customer;
use Code16\Sharp\EntityList\Containers\EntityListDataContainer;
use Code16\Sharp\EntityList\EntityListQueryParams;
use Code16\Sharp\EntityList\SharpEntityList;
use Code16\Sharp\Utils\LinkToEntity;
use Illuminate\Database\Eloquent\Builder;
use App\Sharp\Filters\CustomerDateCreatedFilter;
// use App\Sharp\Filters\CustomerDateTypeFilter;
use App\Sharp\Filters\CustomerLastOrderFilter;
// use App\Sharp\Filters\CustomerCoordinateFilter;
use App\Sharp\Filters\CustomerStoreFilter;
use App\Sharp\Filters\CustomerDistanceStoreFilter;
use App\Sharp\Filters\CustomerMaxDistanceStoreFilter;
use App\Sharp\Filters\CustomerFilter;
// use App\Sharp\Commands\CustomerCountStoreDistance;
use App\Sharp\Commands\CustomerChangeStore;
// use App\Sharp\Commands\CustomerNotifReminder;
// use App\Sharp\Commands\ActivateCustomerLocaleProduct;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
// use App\Sharp\Commands\CustomerDepositGenerateHistory;

class CustomerSharpList extends SharpEntityList
{
    /**
     * Build list containers using ->addDataContainer()
     *
     * @return void
     */
    public function buildListDataContainers()
    {
        $this->addDataContainer(
            EntityListDataContainer::make('name')
                ->setLabel('Nama')
                ->setSortable()
        )->addDataContainer(
            EntityListDataContainer::make("phone")
                ->setSortable()
                ->setLabel("No. Whatsapp")
            // )->addDataContainer(
            //     EntityListDataContainer::make("area")
            //         // ->setSortable()
            //         ->setLabel("Perumahan")
        )->addDataContainer(
            EntityListDataContainer::make("addresses")
                // ->setSortable()
                ->setLabel("Pelanggan")
            // )->addDataContainer(
            //     EntityListDataContainer::make("orders_count")
            //         // ->setSortable()
            //         ->setLabel("Order")
        )
            ->addDataContainer(
                EntityListDataContainer::make("deposit_amount")
                    ->setSortable()
                    ->setLabel("Deposit")
            )
            // ->addDataContainer(
            //     EntityListDataContainer::make("last_order_created_at")
            //         ->setSortable()
            //         ->setLabel("Last Order")
            // )
            ->addDataContainer(
                EntityListDataContainer::make("blash_updated_at")
                    ->setSortable()
                    ->setLabel("Blash At")
            );
    }

    /**
     * Build list layout using ->addColumn()
     *
     * @return void
     */

    public function buildListLayout()
    {
        $this->addColumn("name", 2, 12)
            ->addColumn("phone", 2, 12)
            ->addColumn("addresses", 3, 12)
            ->addColumn("deposit_amount", 2, 12)
            // ->addColumn("last_order_created_at", 2, 12)
            ->addColumn("blash_updated_at", 2, 12);
    }

    /**
     * Build list config
     *
     * @return void
     */
    public function buildListConfig()
    {
        $this->setInstanceIdAttribute('id')
            ->setSearchable()
            // ->addEntityCommand("count_distance", CustomerCountStoreDistance::class)
            ->addEntityCommand("change_store", CustomerChangeStore::class)
            // ->addEntityCommand("notif_reminder", CustomerNotifReminder::class)
            // ->addInstanceCommand("activatecustomerlocaleproduct", ActivateCustomerLocaleProduct::class)
            ->setDefaultSort('id', 'desc')
            ->addFilter("stores", CustomerStoreFilter::class)
            ->addFilter("date_created", CustomerDateCreatedFilter::class)
            // ->addFilter("date_type", CustomerDateTypeFilter::class)
            // ->addFilter("coordinate", CustomerCoordinateFilter::class)
            ->addFilter("custom_filters", CustomerFilter::class)
            ->addFilter("last_order", CustomerLastOrderFilter::class)
            ->addFilter("distance_store", CustomerDistanceStoreFilter::class)
            ->addFilter("max_distance_store", CustomerMaxDistanceStoreFilter::class)
            // ->addEntityCommand("generate", CustomerDepositGenerateHistory::class)
            ->setPaginated();
    }

    /**
     * Retrieve all rows data as array.
     *
     * @param EntityListQueryParams $params
     * @return array
     */
    public function getListData(EntityListQueryParams $params)
    {
        $current_user = auth()->user();
        $customers = Customer::distinct();

        if ($current_user->role_id == 3) { // filter for Admin Toko and Driver
            $stores_id = $current_user->stores->pluck('id');
            $customers->whereHas('addresses', function (Builder $query) use ($stores_id) {
                $query->whereIn('store_id', $stores_id);
            });
        }

        if ($params->sortedBy()) {
            $customers->orderBy($params->sortedBy(), $params->sortedDir());
        }

        if ($params->hasSearch()) {
            $words = '';
            foreach ($params->searchWords(false) as $key => $word) {
                if ($key != 0) {
                    $words = $words . ' ';
                }
                $words = $words . $word;
            }
            $words = '%' . $words . '%';
            $customers->where(function ($query) use ($words) {
                $query->whereHas('addresses', function (Builder $subquery) use ($words) {
                    $subquery->where('address', 'like', $words)
                        ->orWhere('latlng', 'like', $words)
                        ->orWhere('label', 'like', $words);
                })
                    ->orWhere('name', 'like', $words)
                    ->orWhere('email', 'like', $words)
                    ->orWhere('phone', 'like', $words);
            });
        }

        if ($params->filterFor("stores")) {
            $ids = $params->filterFor("stores");
            $distance_store_id = $params->filterFor("distance_store");
            $max_distance_store = $params->filterFor("max_distance_store");
            $customers->whereHas('addresses', function (Builder $query) use ($ids, $distance_store_id, $max_distance_store) {
                $query->whereIn('store_id', (array)$ids);
                if ($distance_store_id) {
                    $query->whereHas('storedistances', function (Builder $subquery) use ($distance_store_id, $max_distance_store) {
                        $subquery->where('store_id', $distance_store_id);
                        if ((int)$max_distance_store > 0) {
                            $subquery->where('distance_meter_by_route', "<=", (int)$max_distance_store);
                            // $subquery->where(function ($subsubquery) use ($max_distance_store) {
                            //     $subsubquery
                            //         ->where('distance_meter', "<=", (int)$max_distance_store)
                            //         ->orWhere('distance_meter_by_route', "<=", (int)$max_distance_store);
                            // });
                        }
                    });
                }
            });
        }

        if ($date_created = $params->filterFor("date_created")) {
            // $date_type = $params->filterFor("date_type");
            // if ($date_type == 'custom') {
            $customers->whereBetween("created_at", [
                $date_created['start'],
                $date_created['end'],
            ]);
            // }
        }

        $last_order = $params->filterFor("last_order");

        if ($last_order) {
            $month01 = Carbon::now();
            $month02 = Carbon::now();
            $month03 = Carbon::now();
            $month06 = Carbon::now();
            $month01->subDays(30);
            $month02->subDays(60);
            $month03->subDays(90);
            $month06->subDays(180);
            // $latest_order = DB::table('orders')
            //     ->whereNull('deleted_at')
            //     ->where(function ($q) {
            //         $q->where('status_id', 4)
            //             ->orWhere('status_id', 6)
            //             ->orWhere('status_id', 7);
            //     })
            //     ->select('customer_id', DB::raw('MAX(created_at) as last_order_created_at'))
            //     ->groupBy('customer_id');
            // $date_type = $params->filterFor("date_type");
            // if ($date_type == 'custom') {
            //     $customers->whereBetween("created_at", [
            //         $date_created['start'],
            //         $date_created['end'],
            //     ]);
            // }
            // $customers->joinSub($latest_order, 'latest_order', function ($join) {
            //     $join->on('customers.id', '=', 'latest_order.customer_id');
            // });
            if ($last_order === 'blash_response') {
                $customers
                    // ->whereNull('socialchat_conversation_id')
                    ->whereDate('options->last_order_at', '>=', $month01->toDateString())
                    ->whereNotNull('blash_updated_at');
            } else if ($last_order === '<1') {
                $customers
                    // ->whereNull('socialchat_conversation_id')
                    ->whereDate('options->last_order_at', '>=', $month01->toDateString());
            } else if ($last_order === '>1') {
                $customers
                    // ->whereNull('socialchat_conversation_id')
                    ->whereDate('options->last_order_at', '<=', $month01->toDateString())
                    ->whereDate('options->last_order_at', '>', $month02->toDateString());
            } else if ($last_order === '>2') {
                $customers
                    // ->whereNull('socialchat_conversation_id')
                    ->whereDate('options->last_order_at', '<=', $month02->toDateString())
                    ->whereDate('options->last_order_at', '>', $month03->toDateString());
            } else if ($last_order === '>3') {
                $customers
                    // ->whereNull('socialchat_conversation_id')
                    ->whereDate('options->last_order_at', '<=', $month03->toDateString())
                    ->whereDate('options->last_order_at', '>', $month06->toDateString());
            } else if ($last_order === '>6') {
                $customers
                    // ->whereNull('socialchat_conversation_id')
                    ->where(function ($query) use ($month06) {
                        $query->whereDate('options->last_order_at', '<=', $month06->toDateString())
                            ->orWhereNull('options->last_order_at')
                        ;
                    });
            }
        }

        // if ($params->filterFor("coordinate") == 'empty') {
        //     $customers->whereHas('addresses', function (Builder $query) {
        //         $query->whereNull('latlng');
        //     });
        // }

        if ($params->filterFor("custom_filters")) {
            $custom_filters = $params->filterFor("custom_filters");
            if (in_array('empty_coordinate', (array)$custom_filters)) {
                $customers->whereHas('addresses', function (Builder $query) {
                    $query->whereNull('latlng');
                });
            }
            if (in_array('special_price', (array)$custom_filters)) {
                $customers->whereHas('products');
            }
            if (in_array('notif_wa_unset', (array)$custom_filters)) {
                $customers->where('notif_status', 'unset');
            }
            if (in_array('notif_wa_on', (array)$custom_filters)) {
                $customers->where('notif_status', 'on');
            }
            if (in_array('notif_wa_off', (array)$custom_filters)) {
                $customers->where('notif_status', 'off');
            }
            if (in_array('is_company', (array)$custom_filters)) {
                $customers->where('is_company', 1);
            }
            if (in_array('lt2', (array)$custom_filters)) {
                $customers->whereHas('addresses', function (Builder $subquery) {
                    $subquery->where('is_secondfloor', 1);
                });
            }
            if (in_array('duplicated_phone', (array)$custom_filters)) {
                $customers
                    // ->havingRaw('COUNT(phone) > 1')
                    // ->having(DB::raw('COUNT(phone)'), '>', 1)
                    ->whereIn('id', function ($subquery) {
                        $subquery->select('id')->from('customers')->groupBy('phone')->havingRaw('count(*) > 1');
                    });
            }
            if (in_array('blashed', (array)$custom_filters)) {
                $customers->whereNotNull('blash_updated_at');
            }
            if (in_array('unblash', (array)$custom_filters)) {
                $customers->whereNull('blash_updated_at');
            }
            if (in_array('notyet_sync_acc', (array)$custom_filters)) {
                $customers->whereHas('addresses', function (Builder $subquery) {
                    $subquery->whereNull('accurate_customer_id')
                        ->orWhereNull('accurate_customer_code');
                });
            }
            if (in_array('is_wbcl', (array)$custom_filters)) {
                $customers->whereNotNull('options->last_notified_at');
            }
            if (in_array('no_sc', (array)$custom_filters)) {
                $customers->whereNull('socialchat_conversation_id');
            }
        }

        return $this
            ->setCustomTransformer("name", function ($name, $customer) {
                $note_special = $customer->note_special ? '<br />📝 <span style="color: gray">' . $customer->note_special . '</span>' : null;
                $special_price = $customer->products->count() > 0 ? '<br />⭐️ <strong style="color: orange">HARGA KHUSUS</strong>' : null;
                $is_company = $customer->is_company ? '<br />🏬 <strong style="color: blue">PERUSAHAAN</strong>' : null;
                return $name . $note_special . $special_price . $is_company;
            })
            ->setCustomTransformer("phone", function ($phone, $customer) {
                $notif_status = null;
                if ($customer->notif_status === 'unset') {
                    // $notif_status = '   <strong style="color: limegreen">NOTIF ON</strong>';
                } else if ($customer->notif_status === 'on') {
                    $notif_status = '<br>📳 <strong style="color: limegreen">NOTIF ON</strong>';
                } else if ($customer->notif_status === 'off') {
                    $notif_status = '<br>🛑 <strong style="color: red">NOTIF OFF</strong>';
                }
                return $phone . $notif_status;
            })
            ->setCustomTransformer("area", function ($value, $customer) {
                return $customer->addresses->map(function ($address) {
                    $area = $address->area ? (new LinkToEntity("<strong>🏘 " . $address->area->name . '</strong>', 'area'))
                        ->setTooltip("See related area")
                        // ->setSearch($address->area->area)
                        ->toFormOfInstance($address->area)
                        ->render() : null;
                    $latlng = $address->area && $address->area->latlng ? '<br><a style="word-wrap: break-word;" href="http://www.google.com/maps/place/' . $address->area->latlng . '" target="_blank">📍' . $address->area->latlng . '</a>' : null;
                    return $area . $latlng;
                })->implode("<hr>");
            })
            ->setCustomTransformer("addresses", function ($addresses, $customer) use ($params) {
                $addresses =  $customer->addresses->map(function ($address) use ($params) {
                    // return $address->title;
                    $store = (new LinkToEntity("<strong>🛍 " . $address->store->name . '</strong>', 'store'))
                        ->setTooltip("See related store")
                        // ->setSearch($address->store->area)
                        ->toFormOfInstance($address->store)
                        ->render();
                    $latlng = $address->latlng ? '<br><a href="http://www.google.com/maps/place/' . $address->latlng . '" target="_blank">📍 ' . $address->latlng . '</a>' : null;
                    $short_address = '<br>🏠 ' . $address->address . ($address->accurate_customer_id || $address->accurate_customer_code ? '<br><strong style="font-size: 10px !important; color: purple;">Accurate: ' . $address->accurate_customer_id . ' / ' . $address->accurate_customer_code . '</strong>' : '');
                    $is_secondfloor = $address->is_secondfloor ? '<br><strong style="color: blue">⬆️2️⃣ Lantai 2</strong>' : null;

                    $custom_filters = $params->filterFor("custom_filters");
                    $distance_store_id = $params->filterFor("distance_store");
                    $distance = in_array('distance', (array)$custom_filters) && $address->storedistances->count() > 0 ? '<hr><h4 style="margin-bottom: 6px;">Jarak Toko:</h4>' . $address->storedistances
                        // ->take(4)
                        ->map(function ($str) use ($distance_store_id) {
                            if ($distance_store_id && (int)$str->id !== (int)$distance_store_id) return;
                            if ($str->pivot->distance_meter_by_route > 20000 || $str->pivot->distance_meter > 20000) return;
                            return '<div>' . ($str->pivot->distance_meter_by_route ? '📍 ' : '⚫️ ') . $str->name . ' (' . number_format($str->pivot->distance_meter_by_route ? $str->pivot->distance_meter_by_route : $str->pivot->distance_meter, 0, '', '.') . 'm)</div>';
                        })
                        ->implode('') : null;
                    return $store . $latlng . $short_address . $is_secondfloor . $distance;
                })->implode("<hr>");

                $areas = $customer->addresses->map(function ($address) {
                    $area = $address->area ? (new LinkToEntity("<strong>🏘 " . $address->area->name . '</strong>', 'area'))
                        ->setTooltip("See related area")
                        // ->setSearch($address->area->area)
                        ->toFormOfInstance($address->area)
                        ->render() : null;
                    $latlng = $address->area && $address->area->latlng ? '<br><a style="word-wrap: break-word;" href="http://www.google.com/maps/place/' . $address->area->latlng . '" target="_blank">📍' . $address->area->latlng . '</a>' : null;
                    return $area . $latlng;
                })->implode("<hr>");
                return $addresses . ($areas ? '<hr><h4>AREA</h4>' . $areas : '');
            })
            ->setCustomTransformer("deposit_amount", function ($val, $customer) use ($last_order) {
                $deposit_amount = '<span style="color: ' . ($val < 0 ? 'red' : '') . ($val > 0 ? 'limegreen' : '') . '">' . ($val < 0 ? '-' : '') . 'Rp' . number_format(abs($val), 0, '', '.') . '</span>';
                return $deposit_amount;
            })
            ->setCustomTransformer("blash_updated_at", function ($val, $customer) use ($last_order) {
                $last_order_created_at = $customer->orders->count() > 0 ? '<div style="color: black; font-size: 11px !important;"><strong style="font-size: 11px !important;">Order:</strong> <span style="font-size: 11px !important;">' . $customer->lastorder->created_at . '</span></div>' : null;
                $message_balsh = $customer->addresses->first()->store->message_blash;
                $message = 'https://wa.me/' . $customer->phone . '?text=' . urlencode($message_balsh);
                // $message = '#';
                $btn = '<a href="' . $message . '" target="_blank" onclick=\'fetch("/manager/ajax/blash-is-send?customer_id=' . $customer->id . '").then((res) => {console.log("SUCCESS", res);const time = new Date();const timeStr = time.toLocaleDateString("id") + " " + time.toLocaleTimeString();console.log("timeStr", timeStr);const el = document.getElementById("blash-date-' . $customer->id . '"); console.log("el", el);el.innerHTML = timeStr;}).catch((error) => {console.log("ERROR", error)})\' style="display: block; ' . ($customer->addresses->first()->store->message_blash ? '' : 'opacity: 0.5; pointer-events: none;') . 'margin-top: 10px; text-decoration: none; border-radius: 5px; font-weight: bold; color: white; background-color: limegreen; padding: 10px; width: 100%; text-align: center;" >📲 Send Reminder</a>';
                $btn .= $message_balsh ? '' : '<div style="color: blue; font-size: 11px !important;">"Pesan Pengingat Pelanggan" di Toko masih kosong!</div>';
                $btn .= '<div style="color: gray; font-size: 11px !important;">Sent: <span style="font-size: 11px !important;" id="blash-date-' . $customer->id . '">' . $customer->blash_updated_at . '</span></div>';
                $btn_reminder = $last_order ? $btn : null;
                return $last_order_created_at . $btn_reminder;
            })
            ->transform($customers->paginate(30));
    }
}
