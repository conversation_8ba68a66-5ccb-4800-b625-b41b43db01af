<?php

namespace App\Sharp;

// use App\Sharp\Commands\CustomerExternalLink;
// use App\Sharp\Commands\CustomerPreview;
// use App\Sharp\Commands\CustomerSendMessage;
// use App\Sharp\CustomShowFields\SharpShowTitleField;
// use App\Sharp\States\CustomerEntityState;
use App\Models\Customer;
// use Code16\Sharp\Form\Eloquent\Uploads\Transformers\SharpUploadModelFormAttributeTransformer;
use Code16\Sharp\Show\Fields\SharpShowEntityListField;
// use Code16\Sharp\Show\Fields\SharpShowFileField;
use Code16\Sharp\Utils\LinkToEntity;
// use Code16\Sharp\Show\Fields\SharpShowListField;
// use Code16\Sharp\Show\Fields\SharpShowPictureField;
use Code16\Sharp\Show\Fields\SharpShowTextField;
use Code16\Sharp\Show\Layout\ShowLayoutColumn;
use Code16\Sharp\Show\Layout\ShowLayoutSection;
use Code16\Sharp\Show\SharpShow;
use App\Sharp\Commands\DepositGenerateHistory;
use App\Sharp\Commands\ActivateCustomerLocaleProduct;
use App\Sharp\Commands\CustomerAccurateSync;
// use Code16\Sharp\Utils\Transformers\Attributes\Eloquent\SharpUploadModelThumbnailUrlTransformer;
// use Code16\Sharp\Utils\Transformers\Attributes\MarkdownAttributeTransformer;

class CustomerSharpShow extends SharpShow
{
    function buildShowFields()
    {
        $this
            ->addField(
                SharpShowTextField::make("name")
                    ->setLabel("Nama")
            )->addField(
                SharpShowTextField::make("special_price")
                    ->setLabel("Harga Khusus")
            )->addField(
                SharpShowTextField::make("phone")
                    ->setLabel("No. WhatsApp")
            )->addField(
                SharpShowTextField::make("cp_phone")
                    ->setLabel("No. WhatsApp (CP)")
            )->addField(
                SharpShowTextField::make("cp_name")
                    ->setLabel("Nama (CP)")
            )->addField(
                SharpShowTextField::make("email")
                    ->setLabel("Email")
            )->addField(
                SharpShowTextField::make("note_special")
                    ->setLabel("Catatan Khusus")
            )->addField(
                SharpShowTextField::make("deposit_amount")
                    ->setLabel("Deposit")
            )->addField(
                SharpShowTextField::make("addresses")
                    ->setLabel("List Alamat:")
            )->addField(
                SharpShowTextField::make("ppobcustomerdatas")
                    ->setLabel("List PPOB:")
            )->addField(
                SharpShowTextField::make("delmanexcludes")
                    ->setLabel("Exlude Delman:")
                // )->addField(
                //     SharpShowFileField::make("manual")
                //         ->setLabel("Manual")
                //         ->setStorageDisk("local")
                //         ->setStorageBasePath("data/Customer/{id}/Manual")
                // )->addField(
                // SharpShowPictureField::make("picture")
                // )->addField(
                //     SharpShowTextField::make("description")
                //         ->collapseToWordCount(50)
                // )->addField(
                //     SharpShowListField::make("pictures")
                //         ->setLabel("additional pictures")
                //         ->addItemField(
                //             SharpShowFileField::make("file")
                //                 ->setStorageDisk("local")
                //                 ->setStorageBasePath("data/Customer/{id}/Pictures")
                //         )
                //         ->addItemField(SharpShowTextField::make("legend")->setLabel("Legend"))
            )->addField(
                SharpShowEntityListField::make("deposits", "customer_deposit")
                    ->setLabel("Deposit History")
                    ->hideFilterWithValue("customer", function ($instanceId) {
                        return $instanceId;
                    })
                    ->showEntityState(false)
                    //                    ->hideEntityCommand("updateXP")
                    //                    ->hideInstanceCommand("download")
                    ->showReorderButton(true)
                    ->showCreateButton()
            );
    }

    /**
     * @throws \Code16\Sharp\Exceptions\SharpException
     */
    function buildShowConfig()
    {
        $this->addInstanceCommand("regenerate_deposit", DepositGenerateHistory::class)
            ->addInstanceCommand("aclp", ActivateCustomerLocaleProduct::class)
            ->addInstanceCommand("accsync", CustomerAccurateSync::class);
        // ->addInstanceCommand("message", CustomerSendMessage::class)
        // ->addInstanceCommand("preview", CustomerPreview::class)
        // ->addInstanceCommandSeparator()
        // ->addInstanceCommand("external", CustomerExternalLink::class)
        // ->setEntityState("state", CustomerEntityState::class);
    }

    function buildShowLayout()
    {
        $this
            ->addSection('Data Pelanggan', function (ShowLayoutSection $section) {
                $section
                    ->addColumn(7, function (ShowLayoutColumn $column) {
                        $column
                            ->withSingleField("name")
                            ->withSingleField("special_price")
                            ->withSingleField("phone")
                            ->withSingleField("cp_name")
                            ->withSingleField("cp_phone")
                            ->withSingleField("email")
                            ->withSingleField("note_special")
                            ->withSingleField("deposit_amount")
                            ->withSingleField("addresses")
                            ->withSingleField("ppobcustomerdatas")
                            ->withSingleField("delmanexcludes");
                    });
                // ->addColumn(5, function(ShowLayoutColumn $column) {
                //     $column->withSingleField("picture");
                // });
            })
            // ->addSection('Description', function(ShowLayoutSection $section) {
            //     $section
            //         ->addColumn(6, function(ShowLayoutColumn $column) {
            //             $column->withSingleField("description");
            //         })
            //         ->addColumn(6, function(ShowLayoutColumn $column) {
            //             $column->withSingleField("pictures", function(ShowLayoutColumn $listItem) {
            //                 $listItem->withSingleField("file")
            //                     ->withSingleField("legend");
            //             });
            //         });
            // })
            ->addEntityListSection("deposits");
    }

    function find($id): array
    {
        return $this
            ->setCustomTransformer("name", function ($value, $model) {
                $is_company = $model->is_company ? '🏬 <strong style="color: blue">PERUSAHAAN: </strong>' : null;
                return $is_company . $value;
            })
            ->setCustomTransformer("phone", function ($value, $model) {
                $wa = $value ? '<a href="https://wa.me/' . $value . '" target="_blank">📲 ' . $value . '</a>' : null;
                $notif_status = null;
                if ($model->notif_status === 'unset') {
                    // $notif_status = '   <strong style="color: limegreen">NOTIF ON</strong>';
                } else if ($model->notif_status === 'on') {
                    $notif_status = ' / 📳 <strong style="color: limegreen">NOTIF ON</strong>';
                } else if ($model->notif_status === 'off') {
                    $notif_status = ' / 🛑 <strong style="color: red">NOTIF OFF</strong>';
                }
                $ignore_notif_reminder = isset($model->options['ignore_notif_reminder']) && $model->options['ignore_notif_reminder'] ? '  / 🚫 📢 <strong style="color: red">TIDAK TERIMA NOTIF REMINDER</strong>' : null;
                return $wa . $notif_status . $ignore_notif_reminder;
            })
            ->setCustomTransformer("special_price", function ($value, $model) {
                return $model->products->count() == 0 ? 'Tidak aktif' : '<strong style="color: orange; margin-right: 3px;">AKTIF</strong> <small style="color: gray;">(EDIT... untuk melihat list harga khusus)</small>';
            })
            ->setCustomTransformer("deposit_amount", function ($value, $model) {
                if ($value < 0) {
                    return '<span style="color: red;">-Rp' . number_format(abs($value), 0, '', '.') . '</span>';
                } elseif ($value > 0) {
                    return '<span style="color: limegreen;">Rp' . number_format(abs($value), 0, '', '.') . '</span>';
                } else {
                    return 'Rp0';
                }
            })
            ->setCustomTransformer("addresses", function ($value, $model) {
                return '<hr>' . $model->addresses->map(function ($address) {
                    // return $address->title;
                    $title = "<strong>" . $address->label . ($address->accurate_customer_id || $address->accurate_customer_code ? ' (' . $address->accurate_customer_id . ' / ' . $address->accurate_customer_code . ')' : '') . "</strong><br>";
                    $store = (new LinkToEntity("🛍 " . $address->store->name, 'store'))
                        ->setTooltip("See related store")
                        // ->setSearch($address->store->area)
                        ->toFormOfInstance($address->store)
                        ->render();
                    $latlng = $address->latlng ? '<br><a href="http://www.google.com/maps/place/' . $address->latlng . '" target="_blank">📍 ' . $address->latlng . '</a>' : null;
                    $short_address = '<br>🏠 ' . $address->address;
                    $area = $address->area ? '<br><br><strong>Grup Perumahan:</strong><br>🏘 ' . $address->area->name . '' : "";
                    $latlng_area = $address->area ? '<br><a href="http://www.google.com/maps/place/' . $address->area->latlng . '" target="_blank">📍 ' . $address->area->latlng . '</a>' : null;
                    $marketing = $address->is_need_marketing ? '<br>📢 <span style="color: red;">Sekalian bagi BROSUR</span>' : null;
                    $is_secondfloor = $address->is_secondfloor ? '<br><strong style="color: blue">⬆️2️⃣ Lantai 2</strong>' : null;
                    return $title . $store . $latlng . $short_address . $area . $latlng_area . $marketing . $is_secondfloor;
                })->implode("<hr>");
            })
            ->setCustomTransformer("ppobcustomerdatas", function ($value, $model) {
                if ($model->ppobcustomerdatas->count() == 0) {
                    return '';
                }
                return '<hr>' . $model->ppobcustomerdatas->map(function ($ppobcustomerdata) {
                    $bullet = $ppobcustomerdata->inquiry_data && $ppobcustomerdata->inquiry_data['status'] === '1' ? '🟡 ' : '🔴 ';
                    $product = '<strong>(' . $ppobcustomerdata->product->code . ') ' . $ppobcustomerdata->product->name . '</strong>';
                    $ppobkey = ' - ' . $ppobcustomerdata->key;
                    $inquiry_data = '';
                    foreach ($ppobcustomerdata->inquiry_data as $key => $value) {
                        $inquiry_data .= '<li>' . $key . ': ' . $value . '</li>';
                    }
                    // $inquiry_data = '<pre>' . $ppobcustomerdata->inquiry_data . '</pre>';
                    return '<div style="margin-bottom: 12px;">' . $bullet . $product . $ppobkey . '<ul style="font-size: 12px;color:gray;margin-top:6px;">'  . $inquiry_data . '</ul></div>';
                })->implode("");
            })
            ->setCustomTransformer("delmanexcludes", function ($value, $model) {
                return '<hr>' . $model->delmanexcludes->map(function ($delman) {
                    // return $address->title;
                    $name = $delman ? "<strong>" . $delman->name . "</strong><br>" : null;
                    $store = $delman && $delman->store ? '🛍 ' . $delman->store->name : null;
                    return $name . $store;
                })->implode("<hr>");
            })
            // ->setCustomTransformer("manual", new SharpUploadModelFormAttributeTransformer(false))
            // ->setCustomTransformer("picture", new SharpUploadModelThumbnailUrlTransformer(140))
            // ->setCustomTransformer("pictures", new SharpUploadModelFormAttributeTransformer(true, 200, 200))
            // ->setCustomTransformer("pictures[legend]", function($value, $instance) {
            //     return $instance->legend["en"] ?? "";
            // })
            // ->setCustomTransformer("description", (new MarkdownAttributeTransformer())->handleImages(200))
            ->transform(Customer::with("addresses", "deposits", "delmanexcludes")->findOrFail($id));
    }
}
