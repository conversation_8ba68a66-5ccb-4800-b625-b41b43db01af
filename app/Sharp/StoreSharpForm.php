<?php

namespace App\Sharp;

use App\Helper\Helper;
use App\Models\City;
use App\Models\Store;
use App\Models\Product;
use App\Models\Category;
use App\Models\Bank;
use Code16\Sharp\Form\SharpForm;
use Code16\Sharp\Http\WithSharpContext;
use Code16\Sharp\Form\Layout\FormLayoutTab;
use Code16\Sharp\Form\Layout\FormLayoutColumn;
// use Code16\Sharp\Form\Fields\SharpFormWysiwygField;
use Code16\Sharp\Form\Fields\SharpFormDateField;
use Code16\Sharp\Form\Fields\SharpFormHtmlField;
use Code16\Sharp\Form\Fields\SharpFormListField;
use Code16\Sharp\Form\Fields\SharpFormTextField;
use Code16\Sharp\Form\Fields\SharpFormCheckField;
use Code16\Sharp\Form\Fields\SharpFormNumberField;
use Code16\Sharp\Form\Fields\SharpFormSelectField;
// use Code16\Sharp\Form\Fields\SharpFormAutocompleteListField;
use Code16\Sharp\Form\Fields\SharpFormUploadField;
use Code16\Sharp\Form\Fields\SharpFormWysiwygField;
use Code16\Sharp\Form\Fields\SharpFormTextareaField;
use Code16\Sharp\Form\Fields\SharpFormGeolocationField;
use App\Sharp\CustomFormFields\SharpFormTextCustomField;
use Code16\Sharp\Form\Fields\SharpFormAutocompleteField;
use Code16\Sharp\Form\Eloquent\WithSharpFormEloquentUpdater;
use Code16\Sharp\Form\Eloquent\Transformers\FormUploadModelTransformer;

class StoreSharpForm extends SharpForm
{
    use WithSharpFormEloquentUpdater, WithSharpContext;

    /**
     * Retrieve a Model for the form and pack all its data as JSON.
     *
     * @param $id
     * @return array
     */
    public function find($id): array
    {
        $model = Store::with([
            'city',
            'banks',
            'storesearchexcludes',
            'featurephoto',
            'productstores',
            'categorystores',
            // 'productstores.product' => function($q){
            //     $q->where('deleted_at', 'oajweof');
            // }
        ])->findOrFail($id);
        $model->msg_review_invitation = isset($model->meta['msg_review_invitation']) ? $model->meta['msg_review_invitation'] : '';
        $model->url_gmap = isset($model->meta['url_gmap']) ? $model->meta['url_gmap'] : '';
        // return $this->transform(
        //     Store::findOrFail($id)
        // );
        return $this->setCustomTransformer("userstamp", function ($value, $user) {
            return [
                "created_by" => $user->creator ? $user->creator->name : 'System',
                "created_at" => $user->created_at ? $user->created_at->format('jMy(G:i)') : '-',
                "updated_by" => $user->editor ? $user->editor->name : 'System',
                "updated_at" => $user->updated_at ? $user->updated_at->format('jMy(G:i)') : '-',
            ];
        })->setCustomTransformer(
            "featurephoto",
            new FormUploadModelTransformer()
        )
            // ->setCustomTransformer("productstores.product_info", function ($product_info) {
            //     return [
            //         // "store_id" => $productstore->store_id,
            //         // "product_id" => $productstore->product_id,
            //         // "stock" => $productstore->stock,
            //         // "local_price" => $productstore->local_price,
            //         // "available" => $productstore->available,
            //         // "product" => [
            //             "id" => 123
            //             // "id" => $product->id,
            //             // "name" => $product->name,
            //             // "price" => $product->price,
            //         // ]
            //     ];
            // })
            ->transform($model);
    }

    public function create(): array
    {
        return $this->transform(new Store([
            // "product_list" => [1,2,3]
        ]));
    }

    /**
     * @param $id
     * @param array $data
     * @return mixed the instance id
     */
    public function update($id, array $data)
    {
        $store = $id ? Store::findOrFail($id) : new Store;

        if (isset($data['whatsapp_1'])) {
            $phone = $data['whatsapp_1'];
            $phone = str_replace(' ', '', $phone);
            $phone = str_replace('-', '', $phone);
            $phone = str_replace('+', '', $phone);
            $phone = str_replace('(', '', $phone);
            $phone = str_replace(')', '', $phone);
            if (substr($phone, 0, 1) == '0') {
                $phone = '62' . substr($phone, 1);
            }
            $data['whatsapp_1'] = $phone;
        }
        if (isset($data['whatsapp_2'])) {
            $phone = $data['whatsapp_2'];
            $phone = str_replace(' ', '', $phone);
            $phone = str_replace('-', '', $phone);
            $phone = str_replace('+', '', $phone);
            $phone = str_replace('(', '', $phone);
            $phone = str_replace(')', '', $phone);
            if (substr($phone, 0, 1) == '0') {
                $phone = '62' . substr($phone, 1);
            }
            $data['whatsapp_2'] = $phone;
        }

        function slugify($text)
        {
            // replace non letter or digits by -
            $text = preg_replace('~[^\pL\d]+~u', '-', $text);
            // transliterate
            $text = iconv('utf-8', 'us-ascii//TRANSLIT', $text);
            // remove unwanted characters
            $text = preg_replace('~[^-\w]+~', '', $text);
            // trim
            $text = trim($text, '-');
            // remove duplicate -
            $text = preg_replace('~-+~', '-', $text);
            // lowercase
            $text = strtolower($text);
            if (empty($text)) {
                return 'n-a';
            }
            return $text;
        }
        $data['slug'] = slugify($data['name']);

        if (isset($data["banks"])) {
            foreach ($data["banks"] as $bank) {
                Bank::where('id', $bank['id'])
                    ->update([
                        'bank_name' => $bank['bank_name'],
                        'account_number' => $bank['account_number'],
                        'holder_name' => $bank['holder_name'],
                        'accurate_glaccount_id' => $bank['accurate_glaccount_id'],
                        'sort_order' => $bank['sort_order'],
                    ]);
            }
        }

        $data['meta'] = [
            ...($store->meta ?? []),
            'msg_review_invitation' => $data['msg_review_invitation'] ?? '',
            'url_gmap' => $data['url_gmap'] ?? '',
        ];

        $store = $this->ignore([
            'userstamp',
            'product_list',
            'category_list',
            'msg_review_invitation',
            'url_gmap',
        ])->save($store, $data);
        $detail = $data['slug'] === 'belanjakantor-online' ? Helper::revalidateBelanjaKantor() : Helper::revalidateFrontEnd($data['slug']);
        $this->notify('Data berhasil disimpan!')
            ->setDetail($detail)
            // ->setDetail($data['banks'][0]['id'] . ' / ' . $data['banks'][0]['accurate_glaccount_id'])
            ->setLevelSuccess();

        // on Create
        // if ($this->context()->isCreation() && isset($data["product_list"])) {
        //     foreach (Product::all() as $product) {
        //         $is_available = 0;
        //         foreach ($data["product_list"] as $selected_product) {
        //             if ((int)$selected_product['id'] === (int)$product->id) {
        //                 $is_available = 1;
        //                 break;
        //             }
        //         }
        //         $store->productstores()->create([
        //             'product_id' => $product->id,
        //             'is_available' => $is_available,
        //         ]);
        //     }
        //     // ProductStore::findOrFail($store->id)->products()->attach($data["product_list"]);
        //     // Workaround to display this only once, in case of a double pass in this method
        //     // by Sharp, to handle relationships in a creation case.
        //     $this->notify("Test Notif")
        //         ->setDetail(json_encode($data["product_list"]))
        //         ->setLevelSuccess()
        //         ->setAutoHide(false);
        // }
    }

    /**
     * @param $id
     */
    public function delete($id)
    {
        Store::findOrFail($id)->find($id)->delete();
    }

    /**
     * Build form fields using ->addField()
     *
     * @return void
     */
    public function buildFormFields()
    {
        $this->addField(
            SharpFormSelectField::make(
                "city_id",
                City::pluck("name", "id")->all()
            )
                ->setLabel("Kota *")
                ->setDisplayAsDropdown()
        )->addField(
            SharpFormTextField::make('area')
                ->setLabel('Area Lokasi *')
            // )->addField(
            //     SharpFormTextCustomField::make("slug")
            //         ->setLabel("Slug *")
            //         ->setPrepend("https://gasplus.online/toko/")
            //         ->setSlugify()
        )->addField(
            SharpFormTextField::make('name')
                ->setLabel('Nama Toko *')
        )->addField(
            SharpFormTextField::make('accurate_cash_glaccount_id')
                ->setLabel('ID Kas Tunai (Accurate)')
                ->setHelpMessage('Khusus untuk kas tunai (CASH). Contoh: 1101xxx')
        )->addField(
            SharpFormTextField::make('accurate_warehouse_name')
                ->setLabel('Nama Gudang (Accurate)')
                ->setHelpMessage('Perhatian! BESAR kecil huruf berpengaruh!')
        )->addField(
            SharpFormTextareaField::make('address')
                ->setLabel('Alamat Lengkap Toko')
        )->addField(
            SharpFormGeolocationField::make("latlng")
                ->setDisplayUnitDecimalDegrees()
                ->setGeocoding()
                ->setInitialPosition(-7.7921967, 110.3699972)
                ->setZoomLevel(14)
                ->setMapsProvider('osm')
                ->setGeocodingProvider('osm')
                // ->setApiKey(env("GMAPS_KEY", "AIzaSyB7Yvda9jQtLu_UmFUh6bBouhxquJfNwtc"))
                ->setLabel("Koordinat")
        )->addField(
            SharpFormWysiwygField::make("description")
                ->setLabel("Deskripsi")
                ->setToolbar([
                    SharpFormWysiwygField::B,
                    SharpFormWysiwygField::I,
                    SharpFormWysiwygField::UL,
                    SharpFormWysiwygField::OL,
                    SharpFormWysiwygField::A,
                    SharpFormWysiwygField::QUOTE,
                ])
        )->addField(
            SharpFormWysiwygField::make("holiday_note")
                ->setLabel("Catatan Libur")
                ->setToolbar([
                    SharpFormWysiwygField::B,
                    SharpFormWysiwygField::I,
                    SharpFormWysiwygField::UL,
                    SharpFormWysiwygField::OL,
                    SharpFormWysiwygField::A,
                    SharpFormWysiwygField::QUOTE,
                ])
                ->setPlaceholder('Maaf hari ini toko LIBUR 🙏')
            // ->addConditionalDisplay('holiday_start', true)
            // ->setConditionalDisplayOrOperator()
            // ->addConditionalDisplay('holiday_end')
        )->addField(
            SharpFormTextCustomField::make('whatsapp_1')
                ->setLabel('No. WhatsApp (CS) *')
                ->setInputType('phone')
        )->addField(
            SharpFormTextCustomField::make('whatsapp_2')
                ->setLabel('No. WhatsApp (Admin Toko)')
                ->setInputType('phone')
        )->addField(
            SharpFormCheckField::make("is_notif_wa", "Aktifkan")
                ->setLabel("Notifikasi WhatsApp?")
        )->addField(
            SharpFormCheckField::make("is_comingsoon", "Ya")
                ->setLabel("Toko Belum Beroperasi?")
        )->addField(
            SharpFormCheckField::make("hide_freejob_fordelman", "Ya")
                ->setLabel("Sebunyikan job bebas untuk delman?")
        )->addField(
            SharpFormDateField::make("open_hour")
                ->setLabel("Jam Buka *")
                // ->setDisplayFormat("HH:mm:ss")
                // ->setStepTime(1)
                ->setHasDate(false)
                ->setHasTime(true)
        )->addField(
            SharpFormDateField::make("close_hour")
                ->setLabel("Jam Tutup *")
                // ->setDisplayFormat("HH:mm:ss")
                // ->setStepTime(1)
                ->setHasDate(false)
                ->setHasTime(true)
        )->addField(
            SharpFormDateField::make("holiday_start")
                ->setLabel("Libur Mulai")
                // ->setDisplayFormat("HH:mm:ss")
                // ->setStepTime(1)
                // ->setHasDate(false)
                ->setHasTime(true)
        )->addField(
            SharpFormDateField::make("holiday_end")
                ->setLabel("Libur Sampai")
                // ->setDisplayFormat("HH:mm:ss")
                // ->setStepTime(1)
                // ->setHasDate(false)
                ->setHasTime(true)
        )->addField(
            SharpFormTextField::make('url_gmap')
                ->setLabel('URL Gmap')
        )->addField(
            SharpFormTextareaField::make('msg_review_invitation')
                ->setLabel('Pesan Notif (AJAKAN REVIEW)')
                ->setPlaceholder('Kalau kamu puas, yuk bantu kami berkembang dengan kasih review-nya 🙏 di link berikut: ')
        )->addField(
            SharpFormTextareaField::make('msg_order_new')
                ->setLabel('Pesan Notif (NEW JOB)')
                ->setPlaceholder('Mohon menunggu. Pesanan sedang kami proses.')
        )->addField(
            SharpFormTextareaField::make('message_blash')
                ->setLabel('Pesan Pengingat Pelanggan')
                ->setPlaceholder('Your message here...')
        )->addField(
            SharpFormTextareaField::make('msg_order_delivered')
                ->setLabel('Pesan Notif (JOB DELIVERED)')
                ->setPlaceholder('Yeay pesanan sudah sampai. Terima kasih sudah berbelanja di Gasplus.')
        )->addField(
            SharpFormTextareaField::make('msg_order_canceled')
                ->setLabel('Pesan Notif (JOB CANCELED)')
                ->setPlaceholder('Order *{code_job}* dibatalkan 🙏. Terima kasih sudah berbelanja di Gasplus. _Catatan: {driver_note}_')
        )->addField(
            SharpFormTextCustomField::make('email')
                ->setLabel('Email')
                ->setInputType('email')
        )->addField(
            SharpFormSelectField::make(
                'chat_server_url',
                [
                    "notif" => "Notif (Default) / 0813-29806-239",
                    "concat" => "CS Concat / 0811-2716-455",
                    "palagan" => "CS Palagan / 0811-2936-455",
                    "kedungmundu" => "CS Kedungmundu / 0811-2916-455",
                    "tembalang" => "CS Tembalang / 0811-2926-455",
                    "kbm" => "CS KBM / 0811-2706-455",
                ]
            )
                ->setDisplayAsDropdown()
                ->setLabel('No. WhatsApp Pengirim Notif')
        )->addField(
            SharpFormUploadField::make("featurephoto")
                ->setLabel("Foto Toko")
                ->setFileFilterImages()
                ->shouldOptimizeImage()
                // ->setCompactThumbnail()
                // ->setCropRatio("1:1", ["jpg","jpeg","png"])
                ->setStorageDisk("public")
                ->setStorageBasePath("img/stores")
        )->addField(
            SharpFormListField::make("banks")
                // ->setLabel("Rekening Bank")
                ->setAddText("Tambah Rekening")
                ->setAddable()
                ->setRemovable()
                // ->setSortable(true)
                // ->setReorderable(CategorySharpReorderHandler::class)
                ->setItemIdAttribute("id")
                ->setOrderAttribute("sort_order")
                ->addItemField(
                    SharpFormTextField::make('bank_name')
                        ->setLabel('Nama Bank *')
                )
                ->addItemField(
                    SharpFormTextField::make('account_number')
                        ->setLabel('Nomor Rekening *')
                )
                ->addItemField(
                    SharpFormTextField::make('holder_name')
                        ->setLabel('Atas Nama *')
                )
                ->addItemField(
                    SharpFormTextField::make('accurate_glaccount_id')
                        ->setLabel('ID Kas/Bank (Accurate)')
                        ->setHelpMessage('Contoh: 1101xxx')
                )
                ->addItemField(
                    SharpFormNumberField::make('sort_order')
                        ->setLabel('Urutan ke')
                )
        )
            ->addField(
                SharpFormSelectField::make(
                    "storesearchexcludes",
                    Store::where('id', '!=', $this->context()->instanceId())->orderBy("sort_order")->pluck("name", "id")->toArray()
                )
                    ->setLabel("Exclude Search *")
                    ->setMultiple()
                    ->setDisplayAsList()
                // ->addConditionalDisplay('role_id', ['3']) // admin toko / driver
                // ->setInline()
                // ->setCreatable(true)
                // ->setCreateAttribute("name")
                // ->setMaxTagCount(4)
            )
            ->addField(
                SharpFormDateField::make('marketing_start')
                    ->setLabel('B-Bro Mulai')
                    ->setHasTime(false)
            )
            ->addField(
                SharpFormNumberField::make('marketing_each_day')
                    ->setLabel('Jumlah B-Bro Wajib')
                    ->setHelpMessage('Dalam 1 hari per toko per delman')
            )
            ->addField(
                SharpFormHtmlField::make('userstamp')
                    ->setTemplatePath("/sharp/templates/userstamp.vue")
            );

        // on Create
        if ($this->context()->isCreation()) {
            $this->addField(
                SharpFormSelectField::make(
                    "product_list",
                    Product::orderBy("sort_order")->get()->map(function ($product) {
                        return [
                            "id" => $product->id,
                            "label" => $product->code
                                . " - " . $product->name
                        ];
                    })->all()
                )
                    ->setMultiple()
                    ->setDisplayAsList()
                    ->allowSelectAll()
            );

            // on Update
        } else {
            $this
                ->addField(
                    SharpFormListField::make("productstores")
                        // ->setLabel("Produk")
                        // ->setAddText("Tambah Produk")
                        // ->setAddable()
                        // ->setRemovable()
                        ->setItemIdAttribute("id")
                        ->addItemField(
                            SharpFormAutocompleteField::make("product_id", "local")
                                ->setLabel("_")
                                // ->setLabel("Kode | Nama Produk - Harga Master")
                                ->setLocalSearchKeys(["code", "name"])
                                ->setItemIdAttribute('id')
                                ->setReadOnly()
                                ->setListItemTemplatePath("/sharp/templates/product_list.vue")
                                ->setResultItemTemplatePath("/sharp/templates/product_result.vue")
                                ->setLocalValues(
                                    Product::orderBy("sort_order")->get()
                                )
                        )
                        // ->addItemField(
                        //     SharpFormHtmlField::make("product_info")
                        //         // ->setLabel("")
                        //         ->setInlineTemplate(
                        //             "<strong>{{ id }}</strong> Nama Produk <em>- Rp123.000</em>"
                        //         )
                        // )
                        ->addItemField(
                            SharpFormNumberField::make("stock")
                                ->setLabel("Stok Barang *")
                                ->setMin(1)
                                // ->setPlaceholder('999')
                                ->setShowControls(false)
                        )
                        ->addItemField(
                            SharpFormTextCustomField::make("local_price")
                                ->setLabel("Harga Toko")
                                // ->setPrepend("Rp")
                                ->setInputType('money')
                        )
                        ->addItemField(
                            SharpFormCheckField::make("is_available", "Ya")
                                ->setLabel("Tersedia?")
                        )
                )
                ->addField(
                    SharpFormListField::make("categorystores")
                        // ->setLabel("Produk")
                        // ->setAddText("Tambah Produk")
                        // ->setAddable()
                        // ->setRemovable()
                        ->setItemIdAttribute("id")
                        ->addItemField(
                            SharpFormAutocompleteField::make("category_id", "local")
                                ->setLabel("_")
                                // ->setLabel("Kode | Nama Produk - Harga Master")
                                ->setLocalSearchKeys(["name"])
                                ->setItemIdAttribute('id')
                                ->setReadOnly()
                                ->setListItemTemplatePath("/sharp/templates/category_item.vue")
                                ->setResultItemTemplatePath("/sharp/templates/category_item.vue")
                                ->setLocalValues(
                                    Category::orderBy("sort_order")->get()
                                )
                        )
                        ->addItemField(
                            SharpFormTextCustomField::make("insentive")
                                ->setLabel("Insentive Toko")
                                // ->setPrepend("Rp")
                                ->setInputType('money')
                        )
                )
                ->addField(
                    SharpFormTextCustomField::make("additionalcost_secondfloor")
                        ->setLabel("Biaya Dasar (jasa antar lantai atas) *")
                        // ->setPrepend("Rp")
                        ->setInputType('money')
                );
        }
    }

    /**
     * Build form layout using ->addTab() or ->addColumn()
     *
     * @return void
     */
    public function buildFormLayout()
    {
        $this->addTab("Info Toko", function (FormLayoutTab $tab) {
            $tab->addColumn(7, function (FormLayoutColumn $column) {
                $column->withSingleField("city_id")
                    ->withSingleField("area")
                    // ->withSingleField("slug")
                    ->withSingleField("name")
                    ->withFields('open_hour|6', 'close_hour|6')
                    ->withSingleField("address")
                    ->withSingleField("description")
                    ->withSingleField("whatsapp_1")
                    ->withSingleField("whatsapp_2")
                    ->withSingleField("email")
                    ->withSingleField("url_gmap")
                    ->withSingleField("chat_server_url")
                    ->withSingleField("is_notif_wa")
                    ->withSingleField("msg_order_new")
                    ->withSingleField("msg_order_delivered")
                    ->withSingleField("msg_order_canceled")
                    ->withSingleField("msg_review_invitation")
                    ->withSingleField("message_blash")
                    // ->withSingleField("marketing_start")
                    ->withSingleField("marketing_each_day")
                    ->withSingleField("is_comingsoon")
                    ->withFields('holiday_start|6', 'holiday_end|6')
                    ->withSingleField("holiday_note")
                    ->withSingleField("hide_freejob_fordelman")
                    ->withSingleField("additionalcost_secondfloor")
                    ->withSingleField("storesearchexcludes")
                    ->withSingleField("accurate_warehouse_name")
                    ->withSingleField("accurate_cash_glaccount_id");
            })->addColumn(5, function (FormLayoutColumn $column) {
                $column->withSingleField("featurephoto")
                    ->withSingleField("latlng");
            })->addColumn(12, function (FormLayoutColumn $column) {
                $column->withSingleField('userstamp');
            });
        })->addTab("Rekening", function (FormLayoutTab $tab) {
            $tab->addColumn(12, function (FormLayoutColumn $column) {
                $column->withSingleField("banks", function (FormLayoutColumn $listItem) {
                    $listItem
                        ->withFields("bank_name|4", "account_number|4", "holder_name|4")
                        ->withFields("accurate_glaccount_id|4", "sort_order|4");
                });
            });
        });

        // on Create
        if ($this->context()->isCreation()) {
            // $this->addTab("Produk", function (FormLayoutTab $tab) {
            //     $tab->addColumn(12, function (FormLayoutColumn $column) {
            //         $column->withSingleField("product_list");
            //     });
            // });

            // on Update
        } else {
            $this
                ->addTab("Produk", function (FormLayoutTab $tab) {
                    $tab->addColumn(12, function (FormLayoutColumn $column) {
                        $column->withSingleField("productstores", function (FormLayoutColumn $listItem) {
                            $listItem->withFields("product_id|5", "stock|2", "local_price|3", "is_available|2");
                        });
                    });
                })
                ->addTab("Insentive", function (FormLayoutTab $tab) {
                    $tab->addColumn(12, function (FormLayoutColumn $column) {
                        $column->withSingleField("categorystores", function (FormLayoutColumn $listItem) {
                            $listItem->withFields("category_id|9", "insentive|3");
                        });
                    });
                });
        }
    }
}
