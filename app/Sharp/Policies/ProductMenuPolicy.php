<?php

namespace App\Sharp\Policies;

use App\Models\User;
use App\Models\Product;

class ProductMenuPolicy
{

    /**
     * @param User $user
     * @return bool
     */
    public function entity(User $user)
    {
        return $user->role_id <= 2;
    }

    public function delete(User $user, $productId)
    {
        $product = Product::findOrFail($productId);
        return !$product->is_ppob;
    }
}
