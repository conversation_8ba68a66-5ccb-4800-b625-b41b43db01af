<?php

namespace App\Sharp\Policies;

use App\Models\User;
use App\Models\Deposit;

class DepositPolicy
{

    /**
     * @param User $user
     * @return bool
     */
    public function entity(User $user)
    {
        return true;
    }

    public function view(User $user, $modelId)
    {
        return true;
    }

    public function update(User $user, $modelId)
    {
        // return true;
        if ($user->role_id <= 2) {
            return true;
        } else {
            return false;
            // $deposit = Deposit::find($modelId);
            // return $deposit->order ? false : true;
        }
    }

    public function delete(User $user, $modelId)
    {
        if ($user->role_id <= 2) {
            $deposit = Deposit::find($modelId);
            return $deposit->order ? false : true;
        } else {
            return false;
            // $deposit = Deposit::find($modelId);
            // return $deposit->order ? false : true;
        }
    }

    public function create(User $user)
    {
        if ($user->role_id <= 2) {
            return true;
        } else {
            return false;
        }
    }
}