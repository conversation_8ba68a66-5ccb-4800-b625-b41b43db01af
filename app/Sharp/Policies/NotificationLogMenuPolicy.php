<?php

namespace App\Sharp\Policies;

use App\Models\User;

class NotificationLogMenuPolicy
{

    /**
     * @param User $user
     * @return bool
     */
    public function entity(User $user)
    {
        return $user->role_id <= 2;
    }

    public function view(User $user, $userId)
    {
        return false;
    }

    public function update(User $user, $userId)
    {
        return false;
    }

    public function delete(User $user, $userId)
    {
        return false;
    }

    public function create(User $user)
    {
        return false;
    }
}
