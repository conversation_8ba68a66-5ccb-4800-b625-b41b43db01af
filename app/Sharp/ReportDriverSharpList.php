<?php

namespace App\Sharp;

use App\Models\User;
use Code16\Sharp\EntityList\Containers\EntityListDataContainer;
use Code16\Sharp\EntityList\EntityListQueryParams;
use Code16\Sharp\EntityList\SharpEntityList;
use App\Sharp\Filters\OrderStoreFilter;
use App\Sharp\Filters\ReportDelmanDateFilter;
use App\Sharp\Filters\OrderDriverFilter;
use Illuminate\Database\Eloquent\Builder;

class ReportDriverSharpList extends SharpEntityList
{
    /**
    * Build list containers using ->addDataContainer()
    *
    * @return void
    */
    public function buildListDataContainers()
    {
        $this->addDataContainer(
            EntityListDataContainer::make('store')
                ->setLabel('Toko')
                // ->setSortable()
        )->addDataContainer(
            EntityListDataContainer::make("name")
                // ->setSortable()
                ->setLabel("Delman")
        )->addDataContainer(
            EntityListDataContainer::make("delivered_count")
                ->setSortable()
                ->setLabel("Terkirim")
        );
    }

    /**
    * Build list layout using ->addColumn()
    *
    * @return void
    */

    public function buildListLayout()
    {
        $this->addColumn("store", 4, 12)
            ->addColumn("name", 4, 12)
            ->addColumn("delivered_count", 4, 12);
    }

    /**
    * Build list config
    *
    * @return void
    */
    public function buildListConfig()
    {
        $this->setInstanceIdAttribute('id')
            ->setSearchable()
            ->setDefaultSort('store_id', 'asc')
            ->addFilter("store_ids", OrderStoreFilter::class)
            ->addFilter("date_range", ReportDelmanDateFilter::class)
            ->addFilter("driver_ids", OrderDriverFilter::class)
            ->setPaginated();
    }

    /**
    * Retrieve all rows data as array.
    *
    * @param EntityListQueryParams $params
    * @return array
    */
    public function getListData(EntityListQueryParams $params)
    {
        $users = User::distinct()
            ->where('role_id', 5);

        if ($params->sortedBy()) {
            $users->orderBy($params->sortedBy(), $params->sortedDir());
        }

        if ($params->hasSearch()) {
            foreach ($params->searchWords() as $word) {
                $users->where('name', 'like', $word)
                    ->orWhere('phone', 'like', $word)
                    ->orWhere('email', 'like', $word);
            }
        }

        if ($store_ids = $params->filterFor("store_ids")) {
            $users->whereIn("store_id", (array)$store_ids);
                // ->orWhereHas('stores', function (Builder $query) use ($store_ids) {
                //     $query->whereIn('id', (array)$store_ids);
                // });
        }

        $date_range = $params->filterFor("date_range");
        $users->whereHas('orders', function (Builder $query) use ($date_range) {
                $query->whereBetween("created_at", [
                    $date_range['start'],
                    $date_range['end'],
                ]);
            });

        if ($driver_ids = $params->filterFor("driver_ids")) {
            $users->whereIn('id', (array)$driver_ids);
        }

        return $this
            ->setCustomTransformer("store", function ($value, $user) {
                return $user->store->name;
            })
            // ->setCustomTransformer("delivered", function ($value, $user) use ($date_range) {
            //     return $user->orders
            //         ->whereIn('status_id', [4, 6, 7])
            //         ->whereHas('orders', function (Builder $query) use ($date_range) {
            //             $query->whereBetween("created_at", [
            //                 $date_range['start'],
            //                 $date_range['end'],
            //             ]);
            //         })
            //         ->count();
            // })
            ->transform(
                $users
                ->with(["orders", "store"])
                ->withCount([
                    'orders AS delivered_count' => function ($query) use ($date_range) {
                        $query
                        ->whereIn('orders.status_id', [4, 6, 7])
                        ->whereBetween("orders.created_at", [
                            $date_range['start'],
                            $date_range['end'],
                        ]);
                    }])
                ->paginate(30)
            );
    }
}