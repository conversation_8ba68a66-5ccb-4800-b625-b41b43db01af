<?php

namespace App\Sharp;

use App\Models\Armada;
use App\Models\Employee;
use App\Models\VhBrand;
use App\Models\VhModel;
use Code16\Sharp\Form\Eloquent\WithSharpFormEloquentUpdater;
use Code16\Sharp\Form\Fields\SharpFormHtmlField;
use Code16\Sharp\Form\Fields\SharpFormTextField;
use Code16\Sharp\Form\Fields\SharpFormSelectField;
use Code16\Sharp\Form\Fields\SharpFormCheckField;
use Code16\Sharp\Form\Fields\SharpFormTextareaField;
use Code16\Sharp\Form\Fields\SharpFormDateField;
use Code16\Sharp\Form\Fields\SharpFormUploadField;
use Code16\Sharp\Form\Fields\SharpFormListField;
use Code16\Sharp\Form\Fields\SharpFormAutocompleteField;
use Code16\Sharp\Form\Layout\FormLayoutColumn;
use Code16\Sharp\Form\SharpForm;
use Code16\Sharp\Http\WithSharpContext;
use Code16\Sharp\Form\Eloquent\Transformers\FormUploadModelTransformer;
use Code16\Sharp\Form\Eloquent\Uploads\Transformers\SharpUploadModelFormAttributeTransformer;

class ArmadaSharpForm extends SharpForm
{
    use WithSharpFormEloquentUpdater, WithSharpContext;

    /**
     * Retrieve a Model for the form and pack all its data as JSON.
     *
     * @param $id
     * @return array
     */
    public function find($id): array
    {
        return $this->setCustomTransformer("userstamp", function ($value, $model) {
            return [
                "created_by" => $model->creator ? $model->creator->name : 'System',
                "created_at" => $model->created_at ? $model->created_at->format('jMy(G:i)') : '-',
                "updated_by" => $model->editor ? $model->editor->name : 'System',
                "updated_at" => $model->updated_at ? $model->updated_at->format('jMy(G:i)') : '-',
            ];
        })
            ->setCustomTransformer('vh_brand_id', function ($value, $model) {
                return $value ? $model->brand : null;
            })
            ->setCustomTransformer('vh_model_id', function ($value, $model) {
                return $value ? $model->model : null;
                // })->setCustomTransformer("vh_type_id", function ($value, $model) {
                //     return $value ? $model->type : null;
            })
            ->setCustomTransformer(
                "stnkphoto",
                new FormUploadModelTransformer()
            )
            ->setCustomTransformer(
                "frontphotos",
                new SharpUploadModelFormAttributeTransformer()
            )
            ->setCustomTransformer(
                "sidephotos",
                new SharpUploadModelFormAttributeTransformer()
            )
            ->setCustomTransformer(
                "backphotos",
                new SharpUploadModelFormAttributeTransformer()
            )
            ->transform(
                Armada::with([
                    'employee',
                    'stnkphoto',
                    'frontphotos',
                    'sidephotos',
                    'backphotos',
                ])->findOrFail($id)
            );
    }

    // public function create(): array
    // {
    //     return $this->transform(new Armada([
    //         "notification_is_active" => 1
    //     ]));
    // }

    /**
     * @param $id
     * @param array $data
     * @return mixed the instance id
     */
    public function update($id, array $data)
    {
        $model = $id ? Armada::findOrFail($id) : new Armada;

        if (substr($data['vh_brand_id'], 0, 6) === 'new-=>') {
            $data['vh_brand_id'] = VhBrand::create([
                'name' => substr($data['vh_brand_id'], 6),
            ])->id;
        }
        if (substr($data['vh_model_id'], 0, 6) === 'new-=>') {
            $data['vh_model_id'] = VhModel::create([
                'name' => substr($data['vh_model_id'], 6),
                'vh_brand_id' => $data['vh_brand_id'],
            ])->id;
        }

        $model = $this->ignore(['userstamp'])->save($model, $data);

        // Unset Old Employee
        if ($model->employee) {
            Armada::where('employee_id', $model->employee->id)
                ->where('id', '!=', $model->id)
                ->update([
                    'employee_id' => null
                ]);
        }
    }

    /**
     * @param $id
     */
    public function delete($id)
    {
        Armada::findOrFail($id)->find($id)->delete();
    }

    /**
     * Build form fields using ->addField()
     *
     * @return void
     */
    public function buildFormFields()
    {

        $this
            ->addField(
                SharpFormUploadField::make("stnkphoto")
                    ->setLabel("Foto STNK")
                    ->setFileFilterImages()
                    ->shouldOptimizeImage()
                    // ->setCompactThumbnail()
                    // ->setCropRatio("1:1", ["jpg","jpeg","png"])
                    ->setStorageDisk("public")
                    ->setStorageBasePath("img/armada/stnk")
            )
            ->addField(
                SharpFormTextField::make('licence_number')
                    ->setLabel('Nomor Polisi *')
            )
            ->addField(
                SharpFormDateField::make("stnk_valid_until")
                    ->setLabel("STNK Berlaku Sampai")
            )
            ->addField(
                SharpFormTextField::make('chassis_number')
                    ->setLabel('Nomor Rangka')
            )
            ->addField(
                SharpFormTextField::make('machine_number')
                    ->setLabel('Nomor Mesin')
            )
            ->addField(
                SharpFormAutocompleteField::make('vh_brand_id', 'remote')
                    ->setLabel('Merek Kendaraan')
                    ->setListItemTemplatePath('sharp/templates/vh_brand.vue')
                    ->setResultItemTemplatePath('sharp/templates/vh_brand.vue')
                    ->setRemoteEndpoint('/search/vhbrands')
                    ->setSearchMinChars(2)
                    ->setPlaceholder('Cari merek...')
            )
            ->addField(
                SharpFormAutocompleteField::make('vh_model_id', 'remote')
                    ->setLabel('Model Kendaraan')
                    ->setListItemTemplatePath('sharp/templates/vh_model.vue')
                    ->setResultItemTemplatePath('sharp/templates/vh_model.vue')
                    ->setDynamicRemoteEndpoint('/search/vhmodels/{{vh_brand_id}}')
                    ->setSearchMinChars(2)
                    ->setPlaceholder('Cari model...')
            )
            ->addField(
                SharpFormTextField::make('year')
                    ->setInputTypeText('number')
                    ->setLabel('Tahun Kendaraan')
            )
            ->addField(
                SharpFormTextareaField::make('note')
                    ->setLabel('Catatan')
                    ->setRowCount(2)
            )
            ->addField(
                SharpFormListField::make("frontphotos")
                    ->setLabel("Foto Tampak Depan")
                    ->setAddText("Tambah Foto")
                    ->setAddable()
                    ->setRemovable()
                    // ->setSortable(true)
                    ->setItemIdAttribute("id")
                    // ->setOrderAttribute("sort_order")
                    ->addItemField(
                        SharpFormUploadField::make("file")
                            ->setFileFilterImages()
                            ->shouldOptimizeImage()
                            // ->setCompactThumbnail()
                            // ->setCropRatio("16:9")
                            ->setStorageDisk("public")
                            ->setStorageBasePath("img/armada/foto")
                    )
            )
            ->addField(
                SharpFormListField::make("sidephotos")
                    ->setLabel("Foto Tampak Samping")
                    ->setAddText("Tambah Foto")
                    ->setAddable()
                    ->setRemovable()
                    // ->setSortable(true)
                    ->setItemIdAttribute("id")
                    // ->setOrderAttribute("sort_order")
                    ->addItemField(
                        SharpFormUploadField::make("file")
                            ->setFileFilterImages()
                            ->shouldOptimizeImage()
                            // ->setCompactThumbnail()
                            // ->setCropRatio("16:9")
                            ->setStorageDisk("public")
                            ->setStorageBasePath("img/armada/foto")
                    )
            )
            ->addField(
                SharpFormListField::make("backphotos")
                    ->setLabel("Foto Tampak belakang")
                    ->setAddText("Tambah Foto")
                    ->setAddable()
                    ->setRemovable()
                    // ->setSortable(true)
                    ->setItemIdAttribute("id")
                    // ->setOrderAttribute("sort_order")
                    ->addItemField(
                        SharpFormUploadField::make("file")
                            ->setFileFilterImages()
                            ->shouldOptimizeImage()
                            // ->setCompactThumbnail()
                            // ->setCropRatio("16:9")
                            ->setStorageDisk("public")
                            ->setStorageBasePath("img/armada/foto")
                    )
            )
            ->addField(
                SharpFormSelectField::make(
                    "employee_id",
                    Employee::orderBy("full_name")->pluck("full_name", "id")->toArray()
                )
                    ->setDisplayAsDropdown()
                    ->setClearable()
                    ->setLabel("SDM")
                // ->setCreatable(true)
                // ->setCreateAttribute("name")
                // ->setMaxTagCount(4)
            )
            ->addField(
                SharpFormHtmlField::make('userstamp')
                    ->setTemplatePath("/sharp/templates/userstamp.vue")
            );
    }

    /**
     * Build form layout using ->addTab() or ->addColumn()
     *
     * @return void
     */
    public function buildFormLayout()
    {
        $this->addColumn(6, function (FormLayoutColumn $column) {
            $column
                ->withSingleField("licence_number")
                ->withSingleField("stnkphoto")
                ->withSingleField("stnk_valid_until")
                ->withSingleField("chassis_number")
                ->withSingleField("machine_number")
                ->withSingleField("vh_brand_id")
                ->withSingleField("vh_model_id")
                ->withSingleField("year")

                ->withSingleField("frontphotos", function (FormLayoutColumn $listItem) {
                    $listItem->withSingleField("file");
                })
                ->withSingleField("sidephotos", function (FormLayoutColumn $listItem) {
                    $listItem->withSingleField("file");
                })
                ->withSingleField("backphotos", function (FormLayoutColumn $listItem) {
                    $listItem->withSingleField("file");
                })
                ->withSingleField("note")
                ->withSingleField("employee_id");
            // ->withSingleField("notification_is_active")
            // ->withSingleField("subscriber_id");
        })->addColumn(12, function (FormLayoutColumn $column) {
            $column->withSingleField('userstamp');
        });
    }
}
