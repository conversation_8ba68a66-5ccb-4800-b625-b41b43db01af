<?php

namespace App\Sharp;

use Code16\Sharp\Form\Validator\SharpFormRequest;
use Code16\Sharp\Http\WithSharpContext;

class ProductSharpValidator extends SharpFormRequest
{
    use WithSharpContext;

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => [
                'required',
                $this->context()->instanceId() ? 'unique:products,name,'.$this->context()->instanceId() : 'unique:products',
            ],
            // 'slug' => [
            //     'required',
            //     $this->context()->instanceId() ? 'unique:products,slug,'.$this->context()->instanceId() : 'unique:products',
            // ],
            'code' => [
                'required',
                $this->context()->instanceId() ? 'unique:products,code,'.$this->context()->instanceId() : 'unique:products',
            ],
            'price' => 'required',
            'minimum_order' => 'required',
            'unit' => 'required',
            'photos.*.file' => 'required',
            // 'featurephoto' => 'max:3000|image'
        ];
    }
}
