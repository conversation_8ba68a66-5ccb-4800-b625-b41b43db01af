<?php

namespace App\Sharp;

use Code16\Sharp\Form\Validator\SharpFormRequest;
use Code16\Sharp\Http\WithSharpContext;

class CitySharpValidator extends SharpFormRequest
{
    use WithSharpContext;

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => [
                'required',
                $this->context()->instanceId() ? 'unique:cities,name,'.$this->context()->instanceId() : 'unique:cities',
            ],
            // 'slug' => [
            //     'required',
            //     $this->context()->instanceId() ? 'unique:cities,slug,'.$this->context()->instanceId() : 'unique:cities',
            // ],
        ];
    }
}
