<?php

namespace App\Sharp;

// use App\Sharp\Commands\CustomerExternalLink;
// use App\Sharp\Commands\CustomerPreview;
// use App\Sharp\Commands\CustomerSendMessage;
// use App\Sharp\CustomShowFields\SharpShowTitleField;
// use App\Sharp\States\CustomerEntityState;
use App\Models\Store;
// use Code16\Sharp\Form\Eloquent\Uploads\Transformers\SharpUploadModelFormAttributeTransformer;
use Code16\Sharp\Show\SharpShow;
// use Code16\Sharp\Show\Fields\SharpShowFileField;
use App\Sharp\Commands\WbclRunOnce;
// use Code16\Sharp\Show\Fields\SharpShowListField;
// use Code16\Sharp\Show\Fields\SharpShowPictureField;
use App\Models\NotificationSchedule;
use App\Sharp\Commands\WbclActivate;
use App\Sharp\Commands\WbclTestNotif;
use App\Sharp\Commands\WbclDeactivate;
use App\Sharp\Commands\WbclSeeCurrentList;
use Code16\Sharp\Show\Layout\ShowLayoutColumn;
use Code16\Sharp\Show\Layout\ShowLayoutSection;
use Code16\Sharp\Show\Fields\SharpShowTextField;
use Code16\Sharp\Show\Fields\SharpShowPictureField;
use Code16\Sharp\Show\Fields\SharpShowEntityListField;
use Code16\Sharp\Utils\Transformers\Attributes\Eloquent\SharpUploadModelThumbnailUrlTransformer;
// use Code16\Sharp\Utils\Transformers\Attributes\Eloquent\SharpUploadModelThumbnailUrlTransformer;
// use Code16\Sharp\Utils\Transformers\Attributes\MarkdownAttributeTransformer;

class WbclSharpShow extends SharpShow
{
    function buildShowFields()
    {
        $this
            ->addField(
                SharpShowTextField::make("title")
                    ->setLabel("Nama")
            )
            ->addField(
                SharpShowTextField::make("store_ids")
                    ->setLabel("Toko")
            )
            ->addField(
                SharpShowTextField::make("criteria")
                    ->setLabel("Target")
            )
            ->addField(
                SharpShowTextField::make("message_template")
                    ->setLabel("Message Notif")
            )
            ->addField(
                SharpShowPictureField::make('notifreminderimage')
            )
            ->addField(
                SharpShowTextField::make("is_active")
                    ->setLabel("Status")
            )
            ->addField(
                SharpShowEntityListField::make("logs", "notification_log")
                    ->setLabel("Logs")
                    ->hideFilterWithValue("notification_schedule_id", function ($instanceId) {
                        return $instanceId;
                    })
                    ->showEntityState(false)
                //                    ->hideEntityCommand("updateXP")
                //                    ->hideInstanceCommand("download")
                // ->showReorderButton(true)
                // ->showCreateButton()
            );
    }

    /**
     * @throws \Code16\Sharp\Exceptions\SharpException
     */
    function buildShowConfig()
    {
        $this
            ->addInstanceCommand("test_notif", WbclTestNotif::class)
            ->addInstanceCommand("activate", WbclActivate::class)
            ->addInstanceCommand("deactivate", WbclDeactivate::class)
            ->addInstanceCommand("run_once", WbclRunOnce::class)
            ->addInstanceCommand("see_current_list", WbclSeeCurrentList::class)
        ;
    }

    function buildShowLayout()
    {
        $this
            ->addSection('WBCL (Win Back Customer Lost)', function (ShowLayoutSection $section) {
                $section
                    ->addColumn(7, function (ShowLayoutColumn $column) {
                        $column
                            ->withSingleField("title")
                            ->withSingleField("store_ids")
                            ->withSingleField("criteria")
                            ->withSingleField("message_template")
                            ->withSingleField("notifreminderimage")
                            ->withSingleField("is_active")
                        ;
                    });
            })
            ->addEntityListSection("logs")
        ;
    }

    function find($id): array
    {
        return $this
            ->setCustomTransformer("store_ids", function ($value, $model) {
                $store_ids = isset($model->others['store_ids']) ? $model->others['store_ids'] :  null;
                if ($store_ids) {
                    $stores = Store::whereIn('id', $store_ids)->get();
                    $storeNames = $stores->pluck('name')->toArray();
                    return '<ul><li>' . implode('</li><li>', $storeNames) . '</li></ul>';
                } else {
                    return '-';
                }
            })
            ->setCustomTransformer("notifreminderimage", new SharpUploadModelThumbnailUrlTransformer(140))
            ->setCustomTransformer("criteria", function ($value, $model) {
                $title = [
                    "test" => "Super Admin & Owner",
                    "gt1-lt2-month" => "1-2 bulan tidak order",
                    "gt2-lt3-month" => "2-3 bulan tidak order",
                    "gt3-lt6-month" => "3-6 bulan tidak order",
                    "gt6-month" => "> 6 bulan tidak order",
                ];
                return $title[$value];
            })
            ->setCustomTransformer("is_active", function ($value, $model) {
                return $value ? '🟢 ON' : '🔴 OFF';
            })
            ->transform(NotificationSchedule::with("notifreminderimage")->findOrFail($id));
    }
}
