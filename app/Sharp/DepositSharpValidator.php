<?php

namespace App\Sharp;

use Code16\Sharp\Form\Validator\SharpFormRequest;
use Code16\Sharp\Http\WithSharpContext;
use App\Models\Deposit;

class DepositSharpValidator extends SharpFormRequest
{
    use WithSharpContext;

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $deposit = Deposit::find($this->context()->instanceId());
        $is_has_order = $deposit && $deposit->order;
        $is_has_invoice = $deposit && $deposit->invoice;
        if ($deposit && $deposit->order) {
            return [
                'created_at' => 'required',
                'transfer' => 'required',
                'note' => 'required',
            ];
        } else if ($deposit && $deposit->invoice) {
            return [
                'created_at' => 'required',
                'invoice_transfer' => 'required',
                // 'invoice_note_con' => 'required',
            ];
        } else {
            return [
                'created_at' => 'required',
                'amount' => 'required',
                'note' => 'required',
            ];
        }
    }
}