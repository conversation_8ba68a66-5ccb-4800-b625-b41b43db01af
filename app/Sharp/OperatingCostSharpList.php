<?php

namespace App\Sharp;

use App\Models\OperatingCost;
use Code16\Sharp\EntityList\Containers\EntityListDataContainer;
use Code16\Sharp\EntityList\EntityListQueryParams;
use Code16\Sharp\EntityList\SharpEntityList;
use App\Sharp\Filters\OperatingCostStoreFilter;
use App\Sharp\Filters\OperatingCostDateCreatedFilter;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class OperatingCostSharpList extends SharpEntityList
{
    /**
     * Build list containers using ->addDataContainer()
     *
     * @return void
     */
    public function buildListDataContainers()
    {
        $this->addDataContainer(
            EntityListDataContainer::make('submit_at')
                ->setLabel('Submit At')
                ->setSortable()
            // ->setHtml()
        )->addDataContainer(
            EntityListDataContainer::make("cost_category_id")
                ->setLabel("Kategori")
                ->setSortable()
        )->addDataContainer(
            EntityListDataContainer::make("items")
                ->setLabel("Details")
            // ->setSortable()
        )->addDataContainer(
            EntityListDataContainer::make("user")
                ->setLabel("By")
                ->setSortable()
        );
    }

    /**
     * Build list layout using ->addColumn()
     *
     * @return void
     */

    public function buildListLayout()
    {
        $this->addColumn("submit_at", 3, 12)
            ->addColumn("cost_category_id", 3, 12)
            ->addColumn("items", 4, 12)
            ->addColumn("user", 2, 12);
    }

    /**
     * Build list config
     *
     * @return void
     */
    public function buildListConfig()
    {
        $this->setInstanceIdAttribute('id')
            ->setSearchable()
            ->addFilter("stores", OperatingCostStoreFilter::class)
            ->addFilter("date_created", OperatingCostDateCreatedFilter::class)
            ->setDefaultSort('submit_at', 'desc')
            ->setPaginated();
    }

    /**
     * Retrieve all rows data as array.
     *
     * @param EntityListQueryParams $params
     * @return array
     */
    public function getListData(EntityListQueryParams $params)
    {
        $current_user = auth()->user();
        $operatingcosts = OperatingCost::distinct();

        if ($current_user->role_id == 3) { // filter for Admin Toko and Driver
            $store_ids = $current_user->stores->pluck('id');
            $operatingcosts->whereIn('store_id', $store_ids);
        }

        if ($params->sortedBy()) {
            $operatingcosts->orderBy($params->sortedBy(), $params->sortedDir());
        }

        if ($params->hasSearch()) {
            foreach ($params->searchWords() as $word) {
                $operatingcosts->where(function ($q) use ($word) {
                    $q->where('note', 'like', '%' . $word . '%');
                });
            }
        }

        if ($params->filterFor("stores")) {
            $ids = $params->filterFor("stores");
            $operatingcosts->whereIn('store_id', (array)$ids);
        }

        if ($date_created = $params->filterFor("date_created")) {
            $operatingcosts->whereBetween("submit_at", [
                $date_created['start'],
                $date_created['end'],
            ]);
        }

        return $this
            ->setCustomTransformer("submit_at", function ($value, $model) {
                $submit_at = '<div>' . date_format(date_create($model->submit_at), 'jMy(G:i)') . '</div>';
                $store = $model->store ? '<div>🛍 ' . $model->store->name . '</div>' : null;
                return $submit_at . $store;
            })
            ->setCustomTransformer("cost_category_id", function ($value, $model) {
                return $model->costcategory->title;
            })
            ->setCustomTransformer("items", function ($value, $model) {
                $armada = empty($model->armada) ? '' : '<div>🛵 ' . $model->armada->licence_number . ($model->armada->brand ? ' ➡️ ' . $model->armada->brand->name : '') . ($model->armada->model ? ' ' . $model->armada->model->name : '') . ')</div>';
                $employee = empty($model->employee) ? '' : '<div>🚹 ' . $model->employee->full_name . '</div>';
                $items = $model->items->map(function ($item) {
                    return '<strong style="color: blue;">🔵 ' . $item->costcategory->title . ' (Rp' . number_format($item->price, 0, '', '.') . ')</strong>';
                })->implode("<br>");
                $note = empty($model->note) ? '' : '<div>📝 ' . $model->note . '</div>';
                return $armada . $employee . $items . $note;
            })
            ->setCustomTransformer("user", function ($value, $model) {
                return $model->user ? $model->user->name : 'System';
            })
            ->transform($operatingcosts->paginate(30));
    }
}
