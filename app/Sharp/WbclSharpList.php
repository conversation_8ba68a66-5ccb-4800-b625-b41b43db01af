<?php

namespace App\Sharp;

use Carbon\Carbon;
use App\Models\Store;
use App\Models\Customer;
use App\Sharp\Commands\WbclRunOnce;
use App\Models\NotificationSchedule;
// use Code16\Sharp\Utils\LinkToEntity;
use App\Sharp\Commands\WbclActivate;
// use Illuminate\Database\Eloquent\Builder;
use App\Sharp\Commands\WbclTestNotif;
// use App\Sharp\Filters\WbclRoleFilter;
// use App\Sharp\Filters\WbclStoreFilter;
use App\Sharp\Commands\WbclDeactivate;
use App\Sharp\Commands\WbclSeeCurrentList;
// use Code16\Sharp\Utils\Transformers\Attributes\MarkdownAttributeTransformer;
use Code16\Sharp\EntityList\SharpEntityList;
use Code16\Sharp\EntityList\EntityListQueryParams;
use Code16\Sharp\EntityList\Containers\EntityListDataContainer;
use Code16\Sharp\EntityList\Eloquent\Transformers\SharpUploadModelAttributeTransformer;

class WbclSharpList extends SharpEntityList
{
    /**
     * Build list containers using ->addDataContainer()
     *
     * @return void
     */
    public function buildListDataContainers()
    {
        $this
            ->addDataContainer(
                EntityListDataContainer::make('title')
                    ->setLabel('Nama')
                    ->setSortable()
            )
            ->addDataContainer(
                EntityListDataContainer::make('criteria')
                    ->setLabel('Target')
                    ->setSortable()
            )
            ->addDataContainer(
                EntityListDataContainer::make("message_template")
                    // ->setSortable()
                    ->setLabel("Template Pesan")
            )
            ->addDataContainer(
                EntityListDataContainer::make("is_active")
                    // ->setSortable()
                    ->setLabel("STS")
            )
            ->addDataContainer(
                EntityListDataContainer::make("notifreminderimage")
                    ->setLabel("Image")
            )
            ->addDataContainer(
                EntityListDataContainer::make("notificationlogs_count")
                    // ->setSortable()
                    ->setLabel("Info")
            )
        ;
    }

    /**
     * Build list layout using ->addColumn()
     *
     * @return void
     */

    public function buildListLayout()
    {
        $this
            ->addColumn("title", 2, 12)
            ->addColumn("criteria", 2, 12)
            ->addColumn("message_template", 3, 12)
            ->addColumn("notifreminderimage", 2, 12)
            ->addColumn("is_active", 1, 12)
            ->addColumn("notificationlogs_count", 2, 12)
        ;
    }

    /**
     * Build list config
     *
     * @return void
     */
    public function buildListConfig()
    {
        $this->setInstanceIdAttribute('id')
            // ->setSearchable()
            // ->setDefaultSort('licence_number', 'asc')
            // ->addFilter("role", WbclRoleFilter::class)
            // ->addFilter("store", WbclStoreFilter::class)
            ->setPaginated()
            ->addInstanceCommand("test_notif", WbclTestNotif::class)
            ->addInstanceCommand("activate", WbclActivate::class)
            ->addInstanceCommand("deactivate", WbclDeactivate::class)
            ->addInstanceCommand("run_once", WbclRunOnce::class)
            ->addInstanceCommand("see_current_list", WbclSeeCurrentList::class)
        ;
    }

    /**
     * Retrieve all rows data as array.
     *
     * @param EntityListQueryParams $params
     * @return array
     */
    public function getListData(EntityListQueryParams $params)
    {
        $model = NotificationSchedule::distinct();

        if ($params->sortedBy()) {
            $model->orderBy($params->sortedBy(), $params->sortedDir());
        }

        // if ($params->hasSearch()) {
        //     foreach ($params->searchWords() as $word) {
        //         $model->where('licence_number', 'like', $word);
        //     }
        // }

        // if ($role_filter = $params->filterFor("role")) {
        //     $model->where("role_id", $role_filter);
        // }

        // if ($stores_filter = $params->filterFor("store")) {
        //     $model->where(function (Builder $query) use ($stores_filter) {
        //         $query->where("store_id", $stores_filter)
        //             ->orWhereHas('stores', function (Builder $subquery) use ($stores_filter) {
        //                 $subquery->where('id', $stores_filter);
        //             });
        //     });
        // }

        return $this
            ->setCustomTransformer(
                "notifreminderimage",
                new SharpUploadModelAttributeTransformer(100, 100, ["fit" => ["w" => 100, "h" => 100]])
            )
            ->setCustomTransformer("criteria", function ($value, $model) {
                $store_list = '<br /><br />🏬 SEMUA TOKO';
                $store_ids = isset($model->others['store_ids']) ? $model->others['store_ids'] :  null;
                if ($store_ids) {
                    $stores = Store::whereIn('id', $store_ids)->get();
                    $storeNames = $stores->pluck('name')->toArray();
                    $store_list = '<br /><br /><ul><li>🏬 ' . implode('</li><li>🏬 ', $storeNames) . '</li></ul>';
                }
                $criteria = '🎯 ';
                switch ($value) {
                    case 'gt1-lt2-month':
                        $criteria .= '1-2 bulan tidak order';
                        break;

                    case 'gt2-lt3-month':
                        $criteria .= '2-3 bulan tidak order';
                        break;

                    case 'gt3-lt6-month':
                        $criteria .= '3-6 bulan tidak order';
                        break;

                    case 'gt6-month':
                        $criteria .= '> 6 bulan tidak order';
                        break;

                    default:
                        $criteria .= 'Super Admin & Owner';
                        break;
                }
                return $criteria . $store_list;
            })
            // ->setCustomTransformer("message_template", new MarkdownAttributeTransformer())
            ->setCustomTransformer("is_active", function ($value, $model) {
                return $value ? '🟢 ON' : '🔴 OFF';
            })
            ->setCustomTransformer("notificationlogs_count", function ($value, $model) {
                $notif_schedule = $model;
                $month01 = Carbon::now();
                $month02 = Carbon::now();
                $month03 = Carbon::now();
                $month06 = Carbon::now();
                $month01->subDays(30);
                $month02->subDays(60);
                $month03->subDays(90);
                $month06->subDays(180);

                $count_unsend = 0;
                $source = Customer::distinct();
                if (isset($notif_schedule->others['store_ids'])) {
                    $store_ids = collect($notif_schedule->others['store_ids'])->pluck('id')->toArray();
                    $source->whereHas('addresses', function ($query) use ($store_ids) {
                        $query->whereIn('store_id', $store_ids);
                    });
                }
                $data_unsend_notfound = (clone $source);
                $data_unsend_notfound
                    ->whereNull('deleted_at')
                    ->whereNull('socialchat_conversation_id')
                    ->where(function ($query) {
                        $query->where('options->ignore_notif_reminder', '!=', 1)
                            ->orWhereNull('options->ignore_notif_reminder')
                        ;
                    });
                $data_unsend = (clone $source);
                $data_unsend
                    ->whereNull('deleted_at')
                    ->whereNotNull('socialchat_conversation_id')
                    ->where(function ($query) {
                        $query->where('options->ignore_notif_reminder', '!=', 1)
                            ->orWhereNull('options->ignore_notif_reminder')
                        ;
                    });
                if ($model->criteria === 'test') {
                    $count_unsend = 3;
                } else if ($model->criteria === 'gt1-lt2-month') {
                    $count_unsend = $data_unsend
                        ->whereNotNull('options->last_order_at')
                        ->whereDate('options->last_order_at', '<=', $month01->toDateString())
                        ->whereDate('options->last_order_at', '>', $month02->toDateString())
                        ->where(function ($q) use ($month01, $month02) {
                            $q->whereNull('options->last_notified_at')
                                ->orWhere(function ($sq) use ($month01, $month02) {
                                    $sq->whereNotNull('options->last_notified_at')
                                        ->whereDate('options->last_notified_at', '<=', $month01->toDateString())
                                        ->whereDate('options->last_notified_at', '>', $month02->toDateString())
                                    ;
                                })
                            ;
                        })
                        ->count();
                    $count_unsend_notfound = $data_unsend_notfound
                        ->whereNotNull('options->last_order_at')
                        ->whereDate('options->last_order_at', '<=', $month01->toDateString())
                        ->whereDate('options->last_order_at', '>', $month02->toDateString())
                        ->where(function ($q) use ($month01, $month02) {
                            $q->whereNull('options->last_notified_at')
                                ->orWhere(function ($sq) use ($month01, $month02) {
                                    $sq->whereNotNull('options->last_notified_at')
                                        ->whereDate('options->last_notified_at', '<=', $month01->toDateString())
                                        ->whereDate('options->last_notified_at', '>', $month02->toDateString())
                                    ;
                                })
                            ;
                        })
                        ->count();
                } else if ($model->criteria === 'gt2-lt3-month') {
                    $count_unsend = $data_unsend
                        ->whereNotNull('options->last_order_at')
                        ->whereDate('options->last_order_at', '<=', $month02->toDateString())
                        ->whereDate('options->last_order_at', '>', $month03->toDateString())
                        ->where(function ($q) use ($month02, $month03) {
                            $q->whereNull('options->last_notified_at')
                                ->orWhere(function ($sq) use ($month02, $month03) {
                                    $sq->whereNotNull('options->last_notified_at')
                                        ->whereDate('options->last_notified_at', '<=', $month02->toDateString())
                                        ->whereDate('options->last_notified_at', '>', $month03->toDateString())
                                    ;
                                })
                            ;
                        })
                        ->count();
                    $count_unsend_notfound = $data_unsend_notfound
                        ->whereNotNull('options->last_order_at')
                        ->whereDate('options->last_order_at', '<=', $month02->toDateString())
                        ->whereDate('options->last_order_at', '>', $month03->toDateString())
                        ->where(function ($q) use ($month02, $month03) {
                            $q->whereNull('options->last_notified_at')
                                ->orWhere(function ($sq) use ($month02, $month03) {
                                    $sq->whereNotNull('options->last_notified_at')
                                        ->whereDate('options->last_notified_at', '<=', $month02->toDateString())
                                        ->whereDate('options->last_notified_at', '>', $month03->toDateString())
                                    ;
                                })
                            ;
                        })
                        ->count();
                } else if ($model->criteria === 'gt3-lt6-month') {
                    $count_unsend = $data_unsend
                        ->whereNotNull('options->last_order_at')
                        ->whereDate('options->last_order_at', '<=', $month03->toDateString())
                        ->whereDate('options->last_order_at', '>', $month06->toDateString())
                        ->where(function ($q) use ($month03, $month06) {
                            $q->whereNull('options->last_notified_at')
                                ->orWhere(function ($sq) use ($month03, $month06) {
                                    $sq->whereNotNull('options->last_notified_at')
                                        ->whereDate('options->last_notified_at', '<=', $month03->toDateString())
                                        ->whereDate('options->last_notified_at', '>', $month06->toDateString())
                                    ;
                                })
                            ;
                        })
                        ->count();
                    $count_unsend_notfound = $data_unsend_notfound
                        ->whereNotNull('options->last_order_at')
                        ->whereDate('options->last_order_at', '<=', $month03->toDateString())
                        ->whereDate('options->last_order_at', '>', $month06->toDateString())
                        ->where(function ($q) use ($month03, $month06) {
                            $q->whereNull('options->last_notified_at')
                                ->orWhere(function ($sq) use ($month03, $month06) {
                                    $sq->whereNotNull('options->last_notified_at')
                                        ->whereDate('options->last_notified_at', '<=', $month03->toDateString())
                                        ->whereDate('options->last_notified_at', '>', $month06->toDateString())
                                    ;
                                })
                            ;
                        })
                        ->count();
                } else if ($model->criteria === 'gt6-month') {
                    $count_unsend = $data_unsend
                        ->whereNotNull('options->last_order_at')
                        ->whereDate('options->last_order_at', '<=', $month06->toDateString())
                        ->where(function ($q) use ($month06) {
                            $q->whereNull('options->last_notified_at')
                                ->orWhere(function ($sq) use ($month06) {
                                    $sq->whereNotNull('options->last_notified_at')
                                        ->whereDate('options->last_notified_at', '<=', $month06->toDateString())
                                    ;
                                })
                            ;
                        })
                        ->count();
                    $count_unsend_notfound = $data_unsend_notfound
                        ->whereNotNull('options->last_order_at')
                        ->whereDate('options->last_order_at', '<=', $month06->toDateString())
                        ->where(function ($q) use ($month06) {
                            $q->whereNull('options->last_notified_at')
                                ->orWhere(function ($sq) use ($month06) {
                                    $sq->whereNotNull('options->last_notified_at')
                                        ->whereDate('options->last_notified_at', '<=', $month06->toDateString())
                                    ;
                                })
                            ;
                        })
                        ->count();
                }

                $count_ttl_res = $model->notificationlogs->whereNotNull('feedback_title')->count();
                $count_res = $model->notificationlogs->whereNotNull('feedback_title')->where('created_at', '>=', Carbon::today())->count();
                $count_sent = $model->notificationlogs->where('created_at', '>=', Carbon::today())->count();

                $all = '<div style="font-size: 10px !important; color: gray;">Keseluruhan</div>';
                $info = '<div style="font-size: 10px !important; color: gray;">Per hari ini</div>';
                $logs = '<div style="font-size: 10px !important;"><strong style="font-size: 10px !important;">📟 TOTAL TERKIRIM: </strong>' . $value . '</div>';
                $tts_res = '<div style="font-size: 10px !important;"><strong style="font-size: 10px !important;">💬 TOTAL RESPON: </strong>' . $count_ttl_res . '</div>';
                $unsend_notfound = '<div style="font-size: 10px !important;"><strong style="font-size: 10px !important;">❌ NO SCHAT: </strong>' . $count_unsend_notfound . '</div>';
                $unsend = '<div style="font-size: 10px !important;"><strong style="font-size: 10px !important;">⏳ BELUM TERKIRIM: </strong>' . $count_unsend . '</div>';
                $sent = '<div style="font-size: 10px !important;"><strong style="font-size: 10px !important;">✅ TERKIRIM: </strong>' . $count_sent . '</div>';
                $response = '<div style="font-size: 10px !important;"><strong style="font-size: 10px !important;">🗣 RESPON: </strong>' . $count_res . '</div>';
                return $all . $logs . $tts_res . $info . $unsend_notfound . $unsend . $sent . $response;
            })
            ->transform($model
                ->with([
                    "notifreminderimage",
                ])
                ->withCount(["notificationlogs"])
                ->paginate(30));
    }
}
