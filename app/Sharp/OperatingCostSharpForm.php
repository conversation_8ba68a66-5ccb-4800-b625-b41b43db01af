<?php

namespace App\Sharp;

use App\Models\Store;
use App\Models\Armada;
use App\Models\Employee;
use App\Models\CostCategory;
use App\Models\OperatingCost;
use Code16\Sharp\Form\SharpForm;
use Code16\Sharp\Http\WithSharpContext;
use Code16\Sharp\Form\Layout\FormLayoutColumn;
use Code16\Sharp\Form\Fields\SharpFormDateField;
use Code16\Sharp\Form\Fields\SharpFormHtmlField;
use Code16\Sharp\Form\Fields\SharpFormListField;
use Code16\Sharp\Form\Fields\SharpFormTextField;
use Code16\Sharp\Form\Fields\SharpFormSelectField;
use Code16\Sharp\Form\Fields\SharpFormUploadField;
use Code16\Sharp\Form\Fields\SharpFormTextareaField;
use App\Sharp\CustomFormFields\SharpFormTextCustomField;
use Code16\Sharp\Form\Fields\SharpFormAutocompleteField;
use Code16\Sharp\Form\Eloquent\WithSharpFormEloquentUpdater;
use Code16\Sharp\Form\Eloquent\Uploads\Transformers\SharpUploadModelFormAttributeTransformer;

class OperatingCostSharpForm extends SharpForm
{
    use WithSharpFormEloquentUpdater, WithSharpContext;

    /**
     * Retrieve a Model for the form and pack all its data as JSON.
     *
     * @param $id
     * @return array
     */
    public function find($id): array
    {
        return $this->setCustomTransformer("userstamp", function ($value, $model) {
            return [
                "created_by" => $model->creator ? $model->creator->name : 'System',
                "created_at" => $model->created_at ? $model->created_at->format('jMy(G:i)') : '-',
                "updated_by" => $model->editor ? $model->editor->name : 'System',
                "updated_at" => $model->updated_at ? $model->updated_at->format('jMy(G:i)') : '-',
            ];
        })
            // ->setCustomTransformer('user_id', function ($value, $model) {
            //     return $value ? $model->user : null;
            // })
            // ->setCustomTransformer('store_id', function ($value, $model) {
            //     return $value ? $model->store : null;
            // })
            // ->setCustomTransformer('armada_id', function ($value, $model) {
            //     return $value ? $model->armada : null;
            // })
            // ->setCustomTransformer('employee_id', function ($value, $model) {
            //     return $value ? $model->employee : null;
            // })
            // ->setCustomTransformer('cost_category_id', function ($value, $model) {
            //     return $value ? $model->costcategory : null;
            // })
            ->setCustomTransformer('items', function ($value, $model) {
                return array_map(function ($item) {
                    $item['parent_cost_category_id'] = OperatingCost::find($item['operating_cost_id'])->cost_category_id;
                    $item['child_cost_category_id'] = CostCategory::find($item['cost_category_id']);
                    return $item;
                }, $value);
            })
            ->setCustomTransformer(
                "operatingcostreceiptphotos",
                new SharpUploadModelFormAttributeTransformer()
            )
            ->transform(
                OperatingCost::with([
                    'operatingcostreceiptphotos',
                    'items',
                    'items.costcategory',
                    'user',
                    'store',
                    'costcategory',
                    'armada',
                    'employee',
                ])->findOrFail($id)
            );
    }

    // public function create(): array
    // {
    //     return $this->transform(new OperatingCost([
    //         // "product_list" => [1,2,3]
    //     ]));
    // }

    /**
     * @param $id
     * @param array $data
     * @return mixed the instance id
     */
    public function update($id, array $data)
    {
        $operatingcost = $id ? OperatingCost::findOrFail($id) : new OperatingCost;

        $data["items"] = array_map(function ($item) {
            unset($item['parent_cost_category_id']);
            $item['cost_category_id'] = $item['child_cost_category_id'];
            unset($item['child_cost_category_id']);
            return $item;
        }, $data["items"]);
        $this->ignore(['userstamp'])->save($operatingcost, $data);

        $this->notify('Data berhasil disimpan!')
            // ->setDetail($detail)
            // ->setDetail($data['banks'][0]['id'] . ' / ' . $data['banks'][0]['accurate_glaccount_id'])
            ->setLevelSuccess();
    }

    /**
     * @param $id
     */
    public function delete($id)
    {
        OperatingCost::findOrFail($id)->find($id)->delete();
    }

    /**
     * Build form fields using ->addField()
     *
     * @return void
     */
    public function buildFormFields()
    {
        $this->addField(
            SharpFormDateField::make("submit_at")
                ->setLabel("Tanggal & Waktu *")
                // ->setDisplayFormat("HH:mm:ss")
                // ->setStepTime(1)
                // ->setHasDate(false)
                ->setHasTime(true)
        )->addField(
            SharpFormSelectField::make(
                "store_id",
                Store::orderBy("name")->pluck("name", "id")->toArray()
            )
                ->setDisplayAsDropdown()
                ->setClearable()
                ->setReadOnly()
                ->setLabel("Toko *")
        )->addField(
            SharpFormSelectField::make(
                "cost_category_id",
                CostCategory::where('is_root', 1)->orderBy("title")->pluck("title", "id")->toArray()
            )
                ->setDisplayAsDropdown()
                ->setReadOnly()
                ->setClearable()
                ->setLabel("Tipe *")
        )->addField(
            SharpFormSelectField::make(
                "armada_id",
                Armada::orderBy("licence_number")->pluck("licence_number", "id")->toArray()
            )
                ->setDisplayAsDropdown()
                ->setClearable()
                ->setLabel("Armada *")
                ->addConditionalDisplay('cost_category_id', ['5'])
        )->addField(
            SharpFormSelectField::make(
                "employee_id",
                Employee::whereNull('deleted_at')->orderBy("full_name")->pluck("full_name", "id")->toArray()
            )
                ->setDisplayAsDropdown()
                ->setClearable()
                ->setLabel("Nama SDM *")
                ->addConditionalDisplay('cost_category_id', ['1'])
        )->addField(
            SharpFormListField::make("operatingcostreceiptphotos")
                ->setLabel("Nota")
                ->setAddText("Tambah Nota")
                ->setAddable()
                ->setRemovable()
                // ->setSortable(true)
                ->setItemIdAttribute("id")
                // ->setOrderAttribute("sort_order")
                ->addItemField(
                    SharpFormUploadField::make("file")
                        ->setFileFilterImages()
                        ->shouldOptimizeImage()
                        // ->setCompactThumbnail()
                        // ->setCropRatio("16:9")
                        ->setStorageDisk("public")
                        ->setStorageBasePath("img/operating-cost")
                )
        )->addField(
            SharpFormListField::make("items")
                ->setLabel("Item *")
                ->setAddText("Tambah Item")
                ->setAddable()
                ->setRemovable()
                ->setItemIdAttribute("id")
                ->addItemField(
                    SharpFormTextField::make("parent_cost_category_id")
                        ->setLabel("Tipe *")
                )
                ->addItemField(
                    SharpFormAutocompleteField::make('child_cost_category_id', 'remote')
                        ->setLabel('Nama Item *')
                        ->setListItemTemplatePath('sharp/templates/cost_category.vue')
                        ->setResultItemTemplatePath('sharp/templates/cost_category.vue')
                        // ->setDynamicRemoteEndpoint('/search/costcategory/{{parent_cost_category_id}}')
                        ->setRemoteEndpoint('/search/costcategory/0')
                        ->setSearchMinChars(1)
                        ->setPlaceholder('Cari "item" atau tekan "spasi" untuk melihat semua perumahan')
                )
                ->addItemField(
                    SharpFormTextCustomField::make("price")
                        ->setLabel("Biaya *")
                        // ->setPrepend("Rp")
                        ->setInputType('money')
                )
                ->addItemField(
                    SharpFormTextareaField::make('note')
                        ->setLabel('Catatan')
                )
        )->addField(
            SharpFormTextareaField::make('note')
                ->setLabel('Catatan')
        )->addField(
            SharpFormHtmlField::make('userstamp')
                ->setTemplatePath("/sharp/templates/userstamp.vue")
        );
    }

    /**
     * Build form layout using ->addTab() or ->addColumn()
     *
     * @return void
     */
    public function buildFormLayout()
    {
        $this->addColumn(6, function (FormLayoutColumn $column) {
            $column->withSingleField("submit_at")
                ->withSingleField("store_id")
                ->withSingleField("cost_category_id")
                ->withSingleField("armada_id")
                ->withSingleField("employee_id")
                ->withSingleField("operatingcostreceiptphotos", function (FormLayoutColumn $listItem) {
                    $listItem->withSingleField("file");
                })
                ->withSingleField("items", function (FormLayoutColumn $listItem) {
                    $listItem->withSingleField("child_cost_category_id")
                        // ->withSingleField("parent_cost_category_id")
                        ->withSingleField("price")
                        ->withSingleField("note");
                })
                ->withSingleField("note");
        })->addColumn(12, function (FormLayoutColumn $column) {
            $column->withSingleField('userstamp');
        });
    }
}
