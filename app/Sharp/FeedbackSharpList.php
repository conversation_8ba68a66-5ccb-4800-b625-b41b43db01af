<?php

namespace App\Sharp;

use App\Models\Feedback;
use Code16\Sharp\EntityList\Containers\EntityListDataContainer;
use Code16\Sharp\EntityList\EntityListQueryParams;
use Code16\Sharp\EntityList\SharpEntityList;
use App\Sharp\Filters\FeedbackServiceRatingFilter;
use App\Sharp\Filters\FeedbackDeliveryRatingFilter;
use App\Sharp\Filters\FeedbackDriverFilter;
use App\Sharp\Filters\FeedbackStoreFilter;
use Illuminate\Database\Eloquent\Builder;

class FeedbackSharpList extends SharpEntityList
{
    /**
     * Build list containers using ->addDataContainer()
     *
     * @return void
     */
    public function buildListDataContainers()
    {
        $this->addDataContainer(
            EntityListDataContainer::make('order')
                ->setLabel('Order')
            // ->setSortable()
            // ->setHtml()
        )->addDataContainer(
            EntityListDataContainer::make("service_rating")
                ->setSortable()
                ->setLabel("Servis")
        )->addDataContainer(
            EntityListDataContainer::make("delivery_rating")
                ->setSortable()
                ->setLabel("Delivery")
        )->addDataContainer(
            EntityListDataContainer::make("note")
                // ->setSortable()
                ->setLabel("Pesan")
        )->addDataContainer(
            EntityListDataContainer::make("created_at")
                ->setSortable()
                ->setLabel("Created")
        );
    }

    /**
     * Build list layout using ->addColumn()
     *
     * @return void
     */

    public function buildListLayout()
    {
        $this->addColumn("order", 3, 12)
            ->addColumn("service_rating", 2, 12)
            ->addColumn("delivery_rating", 2, 12)
            ->addColumn("note", 3, 12)
            ->addColumn("created_at", 2, 12);
    }

    /**
     * Build list config
     *
     * @return void
     */
    public function buildListConfig()
    {
        $this->setInstanceIdAttribute('id')
            ->setSearchable()
            ->setDefaultSort('created_at', 'desc')
            ->addFilter("service_rating", FeedbackServiceRatingFilter::class)
            ->addFilter("delivery_rating", FeedbackDeliveryRatingFilter::class)
            ->addFilter("drivers", FeedbackDriverFilter::class)
            ->addFilter("stores", FeedbackStoreFilter::class)
            ->setPaginated();
    }

    /**
     * Retrieve all rows data as array.
     *
     * @param EntityListQueryParams $params
     * @return array
     */
    public function getListData(EntityListQueryParams $params)
    {
        $feedbacks = Feedback::distinct();

        if ($params->sortedBy()) {
            $feedbacks->orderBy($params->sortedBy(), $params->sortedDir());
        }

        if ($params->hasSearch()) {
            foreach ($params->searchWords() as $word) {
                $feedbacks->where('note', 'like', $word);
            }
        }

        if ($stores = $params->filterFor("stores")) {
            $feedbacks->whereHas('order', function (Builder $query) use ($stores) {
                $query->whereIn('store_id', (array)$stores);
            });
        }

        if ($drivers = $params->filterFor("drivers")) {
            $feedbacks->whereHas('order', function (Builder $query) use ($drivers) {
                $query->whereIn('driver_id', (array)$drivers);
            });
        }

        if ($service_rating = $params->filterFor("service_rating")) {
            $feedbacks->where('service_rating', $service_rating);
        }

        if ($delivery_rating = $params->filterFor("delivery_rating")) {
            $feedbacks->where('delivery_rating', $delivery_rating);
        }

        return $this
            ->setCustomTransformer("order", function ($value, $feedback) {
                $store = '<div><i class="fas fa-store mr-1"></i>' . $feedback->order->store->name . '</div>';
                $driver = $feedback->order->driver ? '<div><i class="fas fa-motorcycle mr-1"></i>' . $feedback->order->driver->name . '</div>' : null;
                $customer = $feedback->order->customer ? '<div><i class="fas fa-user mr-1"></i>' . $feedback->order->customer->name . '</div>' : null;
                return $store . $driver . $customer;
            })
            ->setCustomTransformer("created_at", function ($value, $feedback) {
                return $feedback->created_at->format('jMy(G:i)');
            })
            ->transform($feedbacks->with(['order'])->paginate(30));
    }
}
