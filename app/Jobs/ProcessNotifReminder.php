<?php

namespace App\Jobs;

use Exception;
// use Illuminate\Contracts\Queue\ShouldBeUnique;
use Carbon\Carbon;
use App\Models\Order;
use App\Helper\Helper;
// use App\Models\Feedback;
use App\Models\Customer;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\DB;
use App\Models\NotificationSchedule;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
// use Illuminate\Queue\Middleware\WithoutOverlapping;

class ProcessNotifReminder implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $notification_schedule_id;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($notification_schedule_id)
    {
        $this->notification_schedule_id = $notification_schedule_id;
    }

    // /**
    //  * The number of seconds after which the job's unique lock will be released.
    //  *
    //  * @var int
    //  */
    // public $uniqueFor = 3600;

    // /**
    //  * The unique ID of the job.
    //  *
    //  * @return string
    //  */
    // public function uniqueId()
    // {
    //     $fake_id = '30c3195b-cc2c-4832-adb4-ee1cf37fd1e6';
    //     return $fake_id;
    // }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     *
     * @return array
     */
    // public function backoff()
    // {
    //     return [2, 5, 10];
    // }

    /**
     * Get the middleware the job should pass through.
     *
     * @return array
     */
    // public function middleware()
    // {
    //     $fake_id = '9b7d62f1-2ff5-420a-8beb-d68df0b173cf';
    //     return [new WithoutOverlapping($fake_id)];
    // }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $helper = new Helper;

        $notification_schedule = NotificationSchedule::find($this->notification_schedule_id);

        try {
            $this->processSendNotifReminder($notification_schedule);
        } catch (Exception $e) {
            // Handle the exception

            // Optionally, rethrow the exception to mark the job as failed
            $this->fail($e);
        }
    }

    protected function processSendNotifReminder($notification_schedule)
    {
        // throw new Exception($order->id . ' ~ Test fail');

        // ? Fail
        if (!$notification_schedule) {
            throw new Exception($notification_schedule->id . ' ~ Notification schedule not found');
            // $this->fail();
            // $this->fail(["message" => "Order not found!"]);
        }

        if ($notification_schedule) {
            $criteria = $notification_schedule->criteria;
            $customers = Customer::distinct();
            $month01 = Carbon::now();
            $month02 = Carbon::now();
            $month03 = Carbon::now();
            $month06 = Carbon::now();
            $month01->subDays(30);
            $month02->subDays(60);
            $month03->subDays(90);
            $month06->subDays(180);
            $latest_order = DB::table('orders')
                ->whereNull('deleted_at')
                ->where(function ($q) {
                    $q->where('status_id', 4)
                        ->orWhere('status_id', 6)
                        ->orWhere('status_id', 7);
                })
                ->select('customer_id', DB::raw('MAX(created_at) as last_order_created_at'))
                ->groupBy('customer_id');
            // $date_type = $params->filterFor("date_type");
            // if ($date_type == 'custom') {
            //     $customers->whereBetween("created_at", [
            //         $date_created['start'],
            //         $date_created['end'],
            //     ]);
            // }
            $customers->joinSub($latest_order, 'latest_order', function ($join) {
                $join->on('customers.id', '=', 'latest_order.customer_id');
            });
            if ($criteria === 'gt1-lt2-month') {
                $customers->whereDate('last_order_created_at', '<=', $month01->toDateString())
                    ->whereDate('last_order_created_at', '>', $month02->toDateString());
            } else if ($criteria === 'gt2-lt3-month') {
                $customers->whereDate('last_order_created_at', '<=', $month02->toDateString())
                    ->whereDate('last_order_created_at', '>', $month03->toDateString());
            } else if ($criteria === 'gt3-lt6-month') {
                $customers->whereDate('last_order_created_at', '<=', $month03->toDateString())
                    ->whereDate('last_order_created_at', '>', $month06->toDateString());
            } else if ($criteria === 'gt6-month') {
                $customers->whereDate('last_order_created_at', '<=', $month06->toDateString());
            }
            switch ($criteria) {
                case 'gt1-lt2-month':
                    # code...
                    break;

                default:
                    # code...
                    break;
            }
        }
    }
}
