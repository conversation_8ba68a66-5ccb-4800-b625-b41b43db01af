<?php

namespace App\Jobs;

use Exception;
// use Illuminate\Contracts\Queue\ShouldBeUnique;
use App\Models\Order;
use App\Helper\Helper;

// use App\Models\Feedback;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
// use Illuminate\Queue\Middleware\WithoutOverlapping;

class ProcessJobConfirm implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $dataconfirm;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($dataconfirm)
    {
        $this->dataconfirm = $dataconfirm;
    }

    // /**
    //  * The number of seconds after which the job's unique lock will be released.
    //  *
    //  * @var int
    //  */
    // public $uniqueFor = 3600;

    // /**
    //  * The unique ID of the job.
    //  *
    //  * @return string
    //  */
    // public function uniqueId()
    // {
    //     $fake_id = '30c3195b-cc2c-4832-adb4-ee1cf37fd1e6';
    //     return $fake_id;
    // }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     *
     * @return array
     */
    public function backoff()
    {
        return [2, 5, 10];
    }

    /**
     * Get the middleware the job should pass through.
     *
     * @return array
     */
    // public function middleware()
    // {
    //     $fake_id = '9b7d62f1-2ff5-420a-8beb-d68df0b173cf';
    //     return [new WithoutOverlapping($fake_id)];
    // }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $helper = new Helper;

        $data = $this->dataconfirm;
        $order = Order::find($data['order_id']);

        if ($order) {

            // ? Generate Deposit
            $helper->generateDepositHistory($order->customer_id, $order->created_at);
            // if (isset($res['status']) && $res['status'] === 'error') {
            //     $this->fail();
            // }
        }

        // ? Create Accurate.id Receipt
        try {
            $this->processCreateAccurateReceipt($order);
        } catch (Exception $e) {
            // Handle the exception

            // Optionally, rethrow the exception to mark the job as failed
            $this->fail($e);
        }
    }

    protected function processCreateAccurateReceipt($order)
    {
        // throw new Exception($order->id . ' ~ Test fail');

        // ? Fail
        if (!$order) {
            throw new Exception($order->id . ' ~ Order not found');
            // $this->fail();
            // $this->fail(["message" => "Order not found!"]);
        } else {
            if (!$order->accurateInvoice) {
                throw new Exception($order->id . ' ~ Accurate invoice is empty');
                // $this->fail();
                // $this->fail([
                //     'order_id' => $order->id,
                //     "message" => "Accurate invoice is empty!"
                // ]);
            } else if (!$order->store->accurate_cash_glaccount_id) {
                throw new Exception($order->id . ' ~ Accurate gl account is empty');
                // $this->fail();
                // $this->fail([
                //     'order_id' => $order->id,
                //     "message" => "Accurate gl account is empty!"
                // ]);
            } else if (!$order->address->accurate_customer_code) {
                throw new Exception($order->id . ' ~ Accurate customer code is empty');
                // $this->fail();
                // $this->fail(
                //     [
                //         'order_id' => $order->id,
                //         "message" => "Accurate customer code is empty!"
                //     ]
                // );
            } else if (!$order->store->accurate_branch_id) {
                throw new Exception($order->id . ' ~ Accurate branch id is empty');
                // $this->fail();
                // $this->fail([
                //     'order_id' => $order->id,
                //     "message" => "Accurate branch id is empty!"
                // ]);
            }
        }

        if ($order) {
            $helper = new Helper;

            // ? Success
            $invoice_number = $order->accurateInvoice->accurate_no;
            $params = [];
            $body = [
                'bankNo' => $order->store->accurate_cash_glaccount_id, // * Kas Toko Kabupaten
                'customerNo' => $order->address->accurate_customer_code, // * Aan (Toko Tas Ayu Collection jl Jambon)
                'chequeAmount' => $order->total,
                // 'detailInvoice[n].id' => 00000, // * Only to edit item (NOT REQUIRED)
                'detailInvoice[0].invoiceNo' => $invoice_number,
                'detailInvoice[0].paymentAmount' => $order->total,
                // 'detailInvoice[n]._status' => 'delete', // * Only to delete item (NOT REQUIRED)
                'transDate' => date('d/m/Y'),
                // 'chequeDate' => date('d/m/Y'), // * (NOT REQUIRED)
                'branchId' => $order->store->accurate_branch_id, // * Branch Gasplus Kabupaten
                // 'currencyCode' => 'IDR', // * (NOT REQUIRED)
                // 'description' => 'Test catatan 01', // * (NOT REQUIRED)
                'paymentMethod' => 'CASH_OTHER', // * (NOT REQUIRED)
            ];
            $result_create_receipt = $helper->fetchApiAccurate('/accurate/api/sales-receipt/save.do', 'POST', $params, $body);
            $dataAccurateReceipt = [];
            $dataAccurateReceipt['accuratable_key'] = 'sales-receipt';
            if (is_object($result_create_receipt) && $result_create_receipt->s) {
                $accurate_data = $result_create_receipt->r;
                $dataAccurateReceipt['error_message'] = null;
                $dataAccurateReceipt['synced_at'] = date('Y-m-d H:i:s');
                $dataAccurateReceipt['accurate_id'] = $accurate_data->id;
                $dataAccurateReceipt['accurate_no'] = $accurate_data->number;
            } else if (is_object($result_create_receipt) && !$result_create_receipt->s && isset($result_create_receipt->d)) {
                $dataAccurateReceipt['error_message'] = is_array($result_create_receipt->d) ? $result_create_receipt->d[0] : $result_create_receipt->d;
                throw new Exception($order->id . ' ~ ' . $dataAccurateReceipt['error_message']);
                // $this->fail();
                // $this->fail($dataAccurateReceipt);
            } else {
                $dataAccurateReceipt['error_message'] = 'Error fetchApiAccurate';
                throw new Exception($order->id . ' ~ ' . $dataAccurateReceipt['error_message']);
                // $this->fail();
                // $this->fail($dataAccurateReceipt);
            }
            if ($order->accurateReceipt) {
                $order->accurateReceipt()->update($dataAccurateReceipt);
            } else {
                $order->accurateReceipt()->create($dataAccurateReceipt);
            }
        }
    }
}
