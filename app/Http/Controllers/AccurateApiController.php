<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Helper\Helper;
use App\Models\AccurateTemporaryList;
use Illuminate\Http\Request;
use App\Models\Setting;
use App\Models\Address;
use App\Models\Customer;
use App\Models\Store;
use App\Models\Product;
use App\Models\ProductStore;
use App\Models\Order;
use PhpOffice\PhpSpreadsheet\IOFactory;
use DateTime;
// use Illuminate\Support\Facades\Storage;

class AccurateApiController extends Controller
{
    public function accurateAuthorize()
    {
        $accurate_scope = 'item_save item_view item_delete unit_view unit_save unit_delete warehouse_view warehouse_save warehouse_delete item_category_view item_category_save item_category_delete glaccount_view glaccount_save glaccount_delete sales_invoice_save sales_invoice_view sales_invoice_delete sales_receipt_save sales_receipt_view sales_receipt_delete customer_save customer_delete customer_view branch_view branch_save branch_delete stock_mutation_history_view purchase_invoice_view purchase_invoice_save purchase_invoice_delete vendor_view vendor_price_view vendor_category_view vendor_claim_view attachment_view attachment_save attachment_delete';
        $params = [
            'client_id' => env('ACCURATE_CLIENT_ID'),
            'response_type' => 'code',
            'redirect_uri' => env('APP_URL') . '/accurate-callback',
            'scope' => $accurate_scope,
        ];
        return redirect()->away('https://account.accurate.id/oauth/authorize?' . http_build_query($params));
    }

    public function accurateCallback(Request $request)
    {
        $helper = new Helper;
        // return $setting->value;
        $data = $request->all();
        if (isset($data['code']) && !empty($data['code'])) {

            // ? Save Accurate OAuth Code
            Setting::updateOrCreate(
                [
                    'name' => 'accurate_oauth',
                ],
                ['value' => ['code' => $data['code']],]
            );

            // ? Get Accurate OAuth Token
            $helper->accurateGetToken($data['code']);
        }

        $accurate_token = Setting::where('name', 'accurate_token')->first();
        if ($accurate_token) {

            // ? Get Accurate DB List
            $db_list = $helper->accurateGetDBList();
            if (!is_array($db_list) || empty($db_list)) {
                return 'FAILED: Get Accurate DB List';
            }

            // ? Get Accurate DB Session
            $company_index = array_search(env('ACCURATE_COMPANY_ALIAS'), array_column($db_list, 'alias'));
            if ($company_index === false) {
                // $company_index = 0;
                return 'FAILED: Find DB Name (Company Name)';
            }
            $company_id = $db_list[$company_index]->id;
            if (empty($company_id)) {
                return 'FAILED: Find DB ID (Company ID)';
            }
            $db_session = $helper->accurateGetDBSession($company_id);

            return [
                'result_db_list' => $db_list,
                'result_session' => $db_session,
            ];
        }
        return $data;
    }

    public function accurateSyncIndex(Request $request)
    {
        return redirect('accurate-sync-step-1');
    }

    public function accurateSyncStep1(Request $request)
    {
        $stores = Store::all();
        return view('manager.accurate-sync-step-1', compact('stores'));
    }

    public function accurateSyncProcess1(Request $request)
    {
        if ($request->hasFile('file')) {
            $helper = new Helper();

            $store_id = $request->input('store_id');
            $store_name = Store::find($store_id)->name;
            $is_exclude_synced = $request->input('is_exclude_synced');
            // $store = Store::find($request->input('store_id'));

            $file_type = 'Xlsx';
            $path = $request->file('file')->getRealPath();
            $reader = IOFactory::createReader($file_type);
            $spreadsheet = $reader->load($path);
            $worksheet = $spreadsheet->getActiveSheet();
            // $worksheet->removeRow(1, 1);
            // $worksheet->setCellValue([2, 1], 'Batch');
            // $worksheet->setCellValue([5, 1], 'PERSENTASE KECOCOKAN');
            // $worksheet->setCellValue([8, 1], 'NO WA DI GASPLUS');
            // $worksheet->setCellValue([12, 1], 'ALAMAT DI GASPLUS');
            $data_xlsx = $worksheet->toArray();
            $data_to_sync = [];

            foreach ($data_xlsx as $key => $row) {
                $item_to_sync = [];
                // $similarity = trim($row[5]);
                $accurate_customer_code = trim($row[2]);
                $name = trim($row[3]);
                $names = explode(" ", $name);
                $name01 = $names[0];
                $name02 = array_key_exists(1, $names) ? $names[1] : null;
                $store = trim($row[18]);
                $address01 = trim($row[17]);
                $address02 = trim($row[12]);
                $address = $address02 ? $address02 : $address01;
                $phone01 = $helper->convertPhone($row[7]);
                $phone02 = $helper->convertPhone($row[6]);
                if ($name === 'Nama' || empty($name)) continue;
                // if (!empty($similarity)) continue;
                $item_to_sync['accurate_customer_code'] = $accurate_customer_code;
                $item_to_sync['name'] = $name;
                $item_to_sync['store'] = $store;
                $item_to_sync['address'] = $address;
                $item_to_sync['phone01'] = $phone01;
                $item_to_sync['phone02'] = $phone02;
                $item_to_sync['address_id'] = null;
                $item_to_sync['similarity_percentage'] = null;
                $item_to_sync['customer_id'] = null;
                $item_to_sync['customer_name'] = null;
                $item_to_sync['customer_phone'] = null;
                $item_to_sync['customer_address'] = null;
                $item_to_sync['customer_store'] = null;
                $item_to_sync['accurate_customer_id'] = null;

                // return $item_to_sync;

                $customers = Customer::whereHas('addresses', function ($query) use ($store_id, $is_exclude_synced) {
                    $query->whereIn('store_id', [$store_id]);
                    if ($is_exclude_synced) {
                        $query->whereNull('accurate_customer_code');
                    }
                })
                    ->where('name', $name)
                    ->where(function ($query) use ($phone01, $phone02) {
                        $query->where('phone', $phone01)
                            ->orWhere('phone', $phone02);
                    })
                    ->with(['lastorder'])
                    ->get();
                if ($customers->count() === 0) {
                    $customers = Customer::whereHas('addresses', function ($query) use ($store_id, $is_exclude_synced) {
                        $query->whereIn('store_id', [$store_id]);
                        if ($is_exclude_synced) {
                            $query->whereNull('accurate_customer_code');
                        }
                    })
                        ->where('name', $name)
                        ->with(['lastorder'])
                        ->get();
                }
                if ($customers->count() === 0 && $name02) {
                    $customers = Customer::whereHas('addresses', function ($query) use ($store_id, $is_exclude_synced) {
                        $query->whereIn('store_id', [$store_id]);
                        if ($is_exclude_synced) {
                            $query->whereNull('accurate_customer_code');
                        }
                    })
                        ->where('name', 'like', '%' . $name02 . '%')
                        // ->where(function ($query) use ($name01, $name02) {
                        //     $query->where('name', 'like', '%' . $name01 . '%');
                        //     if ($name02) {
                        //         $query->orWhere('name', 'like', '%' . $name02 . '%');
                        //     }
                        // })
                        ->with(['lastorder'])
                        ->get();
                }
                if ($customers->count() === 0) {
                    if (count($names) > 1) {
                        $name02 = $names[1];
                    }
                    $customers = Customer::whereHas('addresses', function ($query) use ($store_id, $is_exclude_synced) {
                        $query->whereIn('store_id', [$store_id]);
                        if ($is_exclude_synced) {
                            $query->whereNull('accurate_customer_code');
                        }
                    })
                        ->where('name', 'like', '%' . $name01 . '%')
                        // ->where(function ($query) use ($name01, $name02) {
                        //     $query->where('name', 'like', '%' . $name01 . '%');
                        //     if ($name02) {
                        //         $query->orWhere('name', 'like', '%' . $name02 . '%');
                        //     }
                        // })
                        ->with(['lastorder'])
                        ->get();
                }
                if ($customers->count() === 0) {
                    $customers = Customer::whereHas('addresses', function ($query) use ($store_id, $is_exclude_synced) {
                        $query->whereNotIn('store_id', [$store_id]);
                        if ($is_exclude_synced) {
                            $query->whereNull('accurate_customer_code');
                        }
                    })
                        ->where('name', $name)
                        ->with(['lastorder'])
                        ->get();
                }
                if ($customers->count() === 0 && $name02) {
                    $customers = Customer::whereHas('addresses', function ($query) use ($store_id, $is_exclude_synced) {
                        $query->whereNotIn('store_id', [$store_id]);
                        if ($is_exclude_synced) {
                            $query->whereNull('accurate_customer_code');
                        }
                    })
                        ->where('name', 'like', '%' . $name02 . '%')
                        // ->where(function ($query) use ($name01, $name02) {
                        //     $query->where('name', 'like', '%' . $name01 . '%');
                        //     if ($name02) {
                        //         $query->orWhere('name', 'like', '%' . $name02 . '%');
                        //     }
                        // })
                        ->with(['lastorder'])
                        ->get();
                }
                if ($customers->count() === 0) {
                    $customers = Customer::whereHas('addresses', function ($query) use ($store_id, $is_exclude_synced) {
                        $query->whereNotIn('store_id', [$store_id]);
                        if ($is_exclude_synced) {
                            $query->whereNull('accurate_customer_code');
                        }
                    })
                        ->where('name', 'like', '%' . $name01 . '%')
                        // ->where(function ($query) use ($name01, $name02) {
                        //     $query->where('name', 'like', '%' . $name01 . '%');
                        //     if ($name02) {
                        //         $query->orWhere('name', 'like', '%' . $name02 . '%');
                        //     }
                        // })
                        ->with(['lastorder'])
                        ->get();
                }
                // return $customers;
                $customer_candidate = [];
                if ($customers->count() > 0 && !empty($accurate_customer_code)) {
                    foreach ($customers as $customer) {
                        // return $accurate_customer_code;
                        // return $addresses = $customer->addresses()
                        //     // ->whereNotIn('accurate_customer_code', [$accurate_customer_code])
                        //     ->get();
                        foreach ($customer->addresses as $customer_address) {
                            $address = $address02 ? $address02 : $address01;
                            if (!empty($address)) {

                                // ? Address percentage
                                $address_length = strlen($address);
                                $address_similarity = similar_text(strtolower($address), strtolower($customer_address->address), $address_percentage);
                                // $address_percentage = round($address_similarity / $address_length * 100, 0);
                                $address_percentage = round($address_percentage, 0);

                                // ? Name percentage
                                $name_length = strlen($name);
                                $name_similarity = similar_text(strtolower($name), strtolower($customer->name), $name_percentage);
                                // $name_percentage = round($name_similarity / $name_length * 100, 0);
                                $name_percentage = round($name_percentage, 0);

                                // ? Last Order pecentage
                                $firsOrderDate = new DateTime('2021-01-01');
                                $currentDate = new DateTime();
                                $lastOrderDate = new DateTime($customer_address->lastorder->created_at ?? null);
                                $intervalYear = $currentDate->diff($firsOrderDate);
                                $daysFirstOrderDifference = $intervalYear->y;
                                $interval = $currentDate->diff($lastOrderDate);
                                $daysDifference = $interval->days;
                                $totalDaysInYear = 365 * $daysFirstOrderDifference;
                                $last_order_percentage = ($daysDifference / $totalDaysInYear) * 100;
                                $last_order_percentage = 100 - $last_order_percentage;

                                // ? Total Percentage
                                $percentage = (($address_percentage * 3) + ($name_percentage * 3) + $last_order_percentage) / 7;
                                // $percentage = ($address_percentage + $name_percentage) / 2;

                                array_push($customer_candidate, [
                                    'address_id' => $customer_address->id,
                                    'address_percentage' => $address_percentage,
                                    'name_percentage' => $name_percentage,
                                    'last_order_percentage' => $last_order_percentage,
                                    'similarity_percentage' => $percentage,
                                    'customer_id' => $customer->id,
                                    'customer_name' => $customer->name,
                                    'customer_phone' => $customer->phone,
                                    'customer_address' => $customer_address->address,
                                    'customer_store' => $customer_address->store->name,
                                    'customer_last_order' => $customer_address->lastorder->created_at ?? '',
                                    'synced_at' => $customer_address->synced_at,
                                ]);
                            }
                        }
                    }
                }
                if (count($customer_candidate) > 0) {
                    usort($customer_candidate, function ($a, $b) {
                        return $b['similarity_percentage'] - $a['similarity_percentage'];
                    });

                    $customer_prediction = $customer_candidate[0];

                    // $address_accurate_found = Address::where('accurate_customer_code', $accurate_customer_code)->first();
                    // if (!$address_accurate_found) {
                    $item_to_sync['address_id'] = $customer_prediction['address_id'];
                    $item_to_sync['similarity_percentage'] = $customer_prediction['similarity_percentage'];
                    $item_to_sync['customer_id'] = $customer_prediction['customer_id'];
                    $item_to_sync['customer_name'] = $customer_prediction['customer_name'];
                    $item_to_sync['customer_phone'] = $customer_prediction['customer_phone'];
                    $item_to_sync['customer_address'] = $customer_prediction['customer_address'];
                    $item_to_sync['customer_store'] = $customer_prediction['customer_store'];
                    $item_to_sync['customer_last_order'] = $customer_prediction['customer_last_order'];
                    $item_to_sync['synced_at'] = $customer_prediction['synced_at'];

                    $item_to_sync['address_id_alt'] = null;
                    $item_to_sync['similarity_percentage_alt'] = null;
                    $item_to_sync['customer_id_alt'] = null;
                    $item_to_sync['customer_name_alt'] = null;
                    $item_to_sync['customer_phone_alt'] = null;
                    $item_to_sync['customer_address_alt'] = null;
                    $item_to_sync['customer_store_alt'] = null;
                    $item_to_sync['customer_last_order_alt'] = null;
                    $item_to_sync['synced_at_alt'] = null;
                    if (array_key_exists(1, $customer_candidate)) {
                        $item_to_sync['address_id_alt'] = $customer_candidate[1]['address_id'];
                        $item_to_sync['similarity_percentage_alt'] = $customer_candidate[1]['similarity_percentage'];
                        $item_to_sync['customer_id_alt'] = $customer_candidate[1]['customer_id'];
                        $item_to_sync['customer_name_alt'] = $customer_candidate[1]['customer_name'];
                        $item_to_sync['customer_phone_alt'] = $customer_candidate[1]['customer_phone'];
                        $item_to_sync['customer_address_alt'] = $customer_candidate[1]['customer_address'];
                        $item_to_sync['customer_store_alt'] = $customer_candidate[1]['customer_store'];
                        $item_to_sync['customer_last_order_alt'] = $customer_candidate[1]['customer_last_order'];
                        $item_to_sync['synced_at_alt'] = $customer_candidate[1]['synced_at'];
                    }

                    $item_to_sync['customer_candidate'] = $customer_candidate;
                    // $address = Address::where('id', $customer_prediction['address_id'])->first();
                    // $worksheet->setCellValue([2, $key + 1], '3');
                    // $worksheet->setCellValue([5, $key + 1], $customer_prediction['similarity_percentage']);
                    // $worksheet->setCellValue([8, $key + 1], $customer_prediction['customer_phone']);
                    // $worksheet->setCellValue([12, $key + 1], $customer_prediction['customer_address']);
                    // }
                }
                if (!empty($item_to_sync)) {
                    array_push($data_to_sync, $item_to_sync);
                }
            }
            // return $data_to_sync;

            // ? Fetch Accurate ID Customer
            if (!empty($data_to_sync)) {
                $fetch_batch = array_chunk($data_to_sync, 100);
                foreach ($fetch_batch as $batch) {
                    $params = [
                        'fields' => 'id,customerNo,name',
                        'sp.pageSize' => 1000,
                    ];
                    foreach ($batch as $key => $data) {
                        $params['filter.keywords.val[' . $key . ']'] = $data['accurate_customer_code'];
                    }
                    $result_ids = $helper->fetchApiAccurate('/accurate/api/customer/list.do', 'GET', $params, []);
                    if ($result_ids) {
                        $result_ids = $result_ids->d;
                    }
                    if (!empty($result_ids)) {
                        $data_to_sync = array_map(function ($data) use ($result_ids) {
                            $result_id_index = array_search($data['accurate_customer_code'], array_column($result_ids, 'customerNo'));
                            if ($result_id_index !== false) {
                                $data['accurate_customer_id'] = $result_ids[$result_id_index]->id;
                            }
                            return $data;
                        }, $data_to_sync);
                    }
                }
            }
        }

        usort($data_to_sync, function ($a, $b) {
            $x = (int)$a['similarity_percentage'];
            $y = (int)$b['similarity_percentage'];
            if ($x === $y) {
                return 0;
            }
            return $x < $y ? -1 : 1;
        });

        $data_to_sync = array_filter($data_to_sync, function ($data) {
            return !empty($data['accurate_customer_id']) && !empty($data['accurate_customer_code']) && empty($data['synced_at']);
        });

        // return $data_to_sync;

        return view('manager.accurate-sync-step-2', compact('data_to_sync', 'store_id', 'store_name'));
    }

    public function accurateSyncProcess2(Request $request)
    {
        $helper = new Helper;
        $data = $request->all();
        $store_id = $data['store_id'];
        $store_name = Store::find($store_id)->name;
        $address_ids = [];
        if (isset($data["is_sync_data"])) {
            foreach ($data["is_sync_data"] as $is_sync_data) {
                if ($is_sync_data) {
                    $array_sync_data = explode(';', $is_sync_data);
                    $accurate_customer_id = array_key_exists(0, $array_sync_data) ? $array_sync_data[0] : null;
                    $accurate_customer_code = array_key_exists(1, $array_sync_data) ? $array_sync_data[1] : null;
                    $address_id = array_key_exists(2, $array_sync_data) ? $array_sync_data[2] : null;
                    $customer_id = array_key_exists(3, $array_sync_data) ? $array_sync_data[3] : null;
                    // if (!empty($accurate_customer_id) && !empty($accurate_customer_code)) {
                    // $address_is_duplicated = Address::where('accurate_customer_id', $accurate_customer_id)
                    //     ->orWhere('accurate_customer_code', $accurate_customer_code)
                    //     // ->withTrashed()
                    //     ->first();
                    // if (!$address_is_duplicated) {
                    $address = Address::where('id', $address_id)
                        // ->withTrashed()
                        ->first();
                    if ($address) {
                        $address->accurate_customer_code = !empty($accurate_customer_code) ? $accurate_customer_code : null;
                        $address->accurate_customer_id = !empty($accurate_customer_id) ? $accurate_customer_id : null;
                        $address->saveQuietly();
                        if (!empty($accurate_customer_id)) {
                            array_push($address_ids, $address->id);
                        }
                    }
                    // }
                    // }
                }
            }
        }

        $addresses_updated = [];
        if (!empty($address_ids)) {
            $addresses_updated = $helper->accurateBulkUpsertCustomerAddress($address_ids);
        }
        return view('manager.accurate-sync-step-3', compact('addresses_updated', 'store_id', 'store_name'));
    }

    public function accurateSyncProcess3($store_id)
    {
        $helper = new Helper;
        $store_name = Store::find($store_id)->name;
        $unsync_customer_addresses = Address::where('store_id', $store_id)
            ->whereNull('accurate_customer_id')
            ->whereNull('synced_at')
            ->whereNull('deleted_at')
            ->whereHas('customer', function ($q) {
                $q->whereNull('deleted_at');
            })
            // ->whereNull('accurate_customer_code')
            ->get()
            ->sortBy(function ($address) {
                return $address->lastorder->created_at ?? null;
            })
            ->values()
            ->all();
        return view('manager.accurate-sync-step-4', compact('unsync_customer_addresses', 'store_id', 'store_name'));
    }

    public function accurateSyncProcess4(Request $request)
    {
        $helper = new Helper;
        $data = $request->all();
        $store_id = $data['store_id'];
        $store_name = Store::find($store_id)->name;
        $addresses = [];
        if (isset($data["address_ids"])) {
            foreach ($data["address_ids"] as $address_id) {
                if ($address_id) {
                    $address = Address::find($address_id);
                    $created = $helper->accurateUpsertCustomer($address, true);
                    if ($created) {
                        array_push($addresses, $address);
                    }
                }
            }
        }
        return view('manager.accurate-sync-step-5', compact('addresses', 'store_id', 'store_name'));
    }

    public function accurateSyncInitDb()
    {
        $helper = new Helper;

        // ? Sync ACC Branch to GP Store
        $params = [
            'sp.pageSize' => 1000,
        ];
        $body = [];
        $result_list_branch = $helper->fetchApiAccurate('/accurate/api/branch/list.do', 'GET', $params, $body);
        if (is_object($result_list_branch) && $result_list_branch->s) {
            foreach ($result_list_branch->d as $branch) {
                $store = null;
                if ($branch->name === 'Belanjakantor') {
                    $store = Store::where('name', 'belanjakantor.online')->first();
                } else {
                    $store = Store::where('name', $branch->name)->first();
                }
                if ($store) {
                    $store->accurate_branch_id = $branch->id;
                    $store->saveQuietly();
                }
            }
        }

        // ? Sync ACC Item to GP Product
        $page = 1;

        function fetchItems($page)
        {
            $helper = new Helper;
            $params = [
                'fields' => 'id,no,name',
                'sp.page' => $page,
                'sp.pageSize' => 1000,
            ];
            $body = [];
            $result_list_item = $helper->fetchApiAccurate('/accurate/api/item/list.do', 'GET', $params, $body);
            if (is_object($result_list_item) && $result_list_item->s) {
                foreach ($result_list_item->d as $item) {
                    $product = Product::where('code', $item->no)->first();
                    if ($product) {
                        $product->accurate_item_id = $item->id;
                        $product->accurate_item_no = $item->no;
                        $product->saveQuietly();
                    }
                }
                if ($result_list_item->sp->pageCount > $result_list_item->sp->page) {
                    $page++;
                    fetchItems($page);
                }
            }
            return true;
        }

        $is_items_synced = fetchItems($page);

        // ? Sync ACC Item stock
        // $stores = Store::all();
        // foreach ($stores as $store) {
        //     if (empty($store->accurate_warehouse_name)) continue;
        //     $params = [
        //         'sp.pageSize' => 1000,
        //         'warehouseName' => $store->accurate_warehouse_name,
        //     ];
        //     $body = [];
        //     $result = $helper->fetchApiAccurate('/accurate/api/item/list-stock.do', 'GET', $params, $body, true);
        //     if ($result) {
        //         foreach ($result->d as $stock) {
        //             ProductStore::where('store_id', $store->id)
        //                 ->whereHas('product', function ($q) use ($stock) {
        //                     $q->where('accurate_item_no', $stock->no);
        //                 })
        //                 ->update([
        //                     'stock' => $stock->quantity,
        //                 ]);
        //         }
        //     }
        // }


        return [
            'branch_synced' => $result_list_branch,
            'item_synced' => $is_items_synced,
        ];
    }

    public function accurateSyncStockDb()
    {
        $helper = new Helper;

        // ? Sync ACC Item stock
        $stores = Store::all();
        $product_count = 0;
        foreach ($stores as $store) {
            if (empty($store->accurate_warehouse_name)) continue;
            $params = [
                'sp.pageSize' => 1000,
                'warehouseName' => $store->accurate_warehouse_name,
            ];
            $body = [];
            $result = $helper->fetchApiAccurate('/accurate/api/item/list-stock.do', 'GET', $params, $body, true);
            if ($result) {
                foreach ($result->d as $stock) {
                    ProductStore::where('store_id', $store->id)
                        ->whereHas('product', function ($q) use ($stock) {
                            $q->where('accurate_item_no', $stock->no);
                        })
                        ->update([
                            'stock' => $stock->quantity,
                        ]);
                    $product_count++;
                }
            }
        }

        // ? Update PWA
        $helper->fetchBasic('https://api.vercel.com/v1/integrations/deploy/QmZHvnUDGTdNARB2KmAdbhxkosWsJubcdkWJJPbeCLwQ2Z/d3LKyLshWJ', 'GET');

        // ? Renew webhook
        // $helper->fetchApiAccurate('https://account.accurate.id/api/webhook-renew.do', 'GET', null, null, false, true);

        return $product_count . ' data stock synced from Accurate';
    }

    public function accurateTest(Request $request)
    {
        return 'TEST';
        $helper = new Helper;
        $params = [
            // 'id' => '242000'
            // 'number' => 'PI.2024.07.00266'
            // 'appKey' => 'b0f003bf-11bf-49ae-b58a-b2da98b9709c',
            'sp.pageSize' => 1000,
            // 'sp.page' => 2,
            // 'customerNo' => 'G10245',
            // 'no' => 'IA',
            'warehouseName' => 'Gudang Kabupaten',
        ];
        $body = [];
        return $result_list = $helper->fetchApiAccurate('/accurate/api/item/list-stock.do', 'GET', $params, $body);
        // return $result_list = $helper->fetchApiAccurate('/accurate/api/item/get-stock.do', 'GET', $params, $body);
        // return $result_list = $helper->fetchApiAccurate('/accurate/api/purchase-invoice/detail.do', 'GET', $params, $body);
        // return $result_list = $helper->fetchApiAccurate('https://account.accurate.id/api/webhook-renew.do', 'GET', null, null, false, true);
        // $params = [
        //     'fields' => 'totalAmount',
        //     'sp.pageSize' => 1000,
        //     // 'filter.dueDate.val[0]' => date('d/m/Y'),
        //     'filter.transDate.val[0]' => date('d/m/Y', strtotime('2024-07-05')),
        //     'filter.branchName' => 'Gasplus Palagan',
        // ];
        // $body = [];
        // return $result = $helper->fetchApiAccurate('/accurate/api/sales-invoice/list.do', 'GET', $params, $body);
        // // First order
        // $firsOrderDate = new DateTime('2021-01-01');

        // // Current date
        // $currentDate = new DateTime();

        // // Given date
        // $givenDate = new DateTime('2024-04-08T08:34:57.000000Z'); // Change this date to your desired date

        // // Calculate the difference in days
        // $interval = $currentDate->diff($firsOrderDate);
        // $daysFirstOrderDifference = $interval->y;

        // // Calculate the difference in days
        // $interval = $currentDate->diff($givenDate);
        // $daysDifference = $interval->days;

        // // Calculate the percentage
        // $totalDaysInYear = 365 * $daysFirstOrderDifference; // Assuming a non-leap year
        // $percentage = ($daysDifference / $totalDaysInYear) * 100;

        // $percentage = 100 - $percentage;

        // return "Percentage of days between the current date and the given date: " . round($percentage, 2) . "%";

        // $current_time = time();
        // $created_at = strtotime('-13 days', $current_time);
        // return [
        //     'current' => $current_time,
        //     'created_at' => $created_at,
        //     'diff' => $created_at - $current_time,
        // ];
        // $unsync_customer_addresses_concat = Address::where('store_id', 8)
        //     ->whereNull('accurate_customer_id')
        //     ->whereNull('synced_at')
        //     // ->whereNull('accurate_customer_code')
        //     ->get();
        // foreach ($unsync_customer_addresses_concat as $address) {
        //     $helper->accurateUpsertCustomer($address, true);
        // }
        // return $unsync_customer_addresses_concat->count();
        // // return 'yes' ?: 'aoijweiof';
        // // return date('Y-m-d H:i:s');
        // $order = Order::find(232172);
        // // return $helper->accurateUpsertInvoice($order, true);
        // // $address = Address::where('id', 14338)->withTrashed()->first();
        // // return $helper->accurateDeleteCustomer($address);
        // $params = [];
        $params = [
            'number' => 'SI.2024.05.04536',
        ];
        $body = [];
        // return $result_list = $helper->fetchApiAccurate('/accurate/api/sales-invoice/detail.do', 'GET', $params, $body);

        $params = [
            // 'fields' => 'id,no,name,customerNo,transDate,number,dueDate,shipDate,totalAmount',
            'fields' => 'totalAmount,shipDate,dueDate,transDate,number,approvalStatus',
            'sp.pageSize' => 1000,
            // 'filter.dueDate.val[0]' => '22/05/2024',
            // 'filter.shipDate.val[0]' => '22/05/2024',
            'filter.transDate.val[0]' => '22/05/2024',
            'filter.branchName' => 'Gasplus Palagan',
            // 'branchFilter' => [101900], // Palagan
            // 'id' => 494875,
            // 'id' => 230786,
            // 'filter.keywords.val[0]' => '1101034.2024.02.00104',
            // 'filter.keywords.val[0]' => 'SI.2024.02.01699',
            // 'filter.keywords.val[0]' => 'G12920',
            // 'filter.keywords.val[1]' => 'G12922',
            // 'filter.keywords.val[2]' => 'G12924',
            // 'fromDate' => '14/04/2024',
            // 'dueDateFilter' => '23/05/2024',
            // 'shipDateFilter' => ['22/05/2024'],
            // 'transDateFilter' => ['22/05/2024'],
            // 'toDate' => '14/04/2024',
            // 'itemNo' => 'IA',
            // 'warehouseName' => 'Gudang Palagan',
        ];
        // {
        //     "name": "Yamaha DDS jogja",
        //     "id": 230618,
        //     "customerNo": "G12920"
        //     },
        //     {
        //     "name": "Yamaha DDS jogja",
        //     "id": 230744,
        //     "customerNo": "G12922"
        //     },
        //     {
        //     "name": "Yamaha DDS jogja",
        //     "id": 230786,
        //     "customerNo": "G12924"
        //     }
        $body = [];
        return $result_list = $helper->fetchApiAccurate('/accurate/api/sales-invoice/list.do', 'GET', $params, $body);

        // ? Test Create Faktur
        $params = [];
        $body = [
            'customerNo' => 'G10056', // * Aan (Toko Tas Ayu Collection jl Jambon)
            // 'detailItem[0].id' => 00000, // * Only to edit item (NOT REQUIRED)
            'detailItem[0].itemNo' => 'IA',
            'detailItem[0].unitPrice' => 21000,
            // 'detailItem[0].itemUnitName' => 'Galon', // * (NOT REQUIRED)
            'detailItem[0].quantity' => 2, // * (NOT REQUIRED)
            'detailItem[0].warehouseName' => 'Gudang Kabupaten', // * (NOT REQUIRED)
            // 'detailItem[0]._status' => 'delete', // * Only to delete item (NOT REQUIRED)
            'transDate' => date('d/m/Y'),
            // 'shipDate' => date('d/m/Y'), // * (NOT REQUIRED)
            'branchId' => 102200, // * Branch Gasplus Kabupaten
            // 'currencyCode' => 'IDR', // * (NOT REQUIRED)
            // 'description' => 'Test catatan 01', // * (NOT REQUIRED)
            // 'toAddress' => 'Toko Tas Ayu Collection jl Jambon', // * (NOT REQUIRED)
            // 'documentCode' => 'DIGUNGGUNG', // * (NOT REQUIRED)
        ];
        $result_create_faktur = $helper->fetchApiAccurate('/accurate/api/sales-invoice/save.do', 'POST', $params, $body);
        // ==========

        if ($result_create_faktur) {
            $invoice_number = $result_create_faktur->r->number;
            // ? Test Create Receipt (Penerimaan Uang)
            $params = [];
            $body = [
                'bankNo' => 1101034, // * Kas Toko Kabupaten
                'customerNo' => 'G10056', // * Aan (Toko Tas Ayu Collection jl Jambon)
                'chequeAmount' => 42000,
                // 'detailInvoice[n].id' => 00000, // * Only to edit item (NOT REQUIRED)
                'detailInvoice[0].invoiceNo' => $invoice_number,
                'detailInvoice[0].paymentAmount' => 42000,
                // 'detailInvoice[n]._status' => 'delete', // * Only to delete item (NOT REQUIRED)
                'transDate' => date('d/m/Y'),
                // 'chequeDate' => date('d/m/Y'), // * (NOT REQUIRED)
                'branchId' => 102200, // * Branch Gasplus Kabupaten
                // 'currencyCode' => 'IDR', // * (NOT REQUIRED)
                // 'description' => 'Test catatan 01', // * (NOT REQUIRED)
                'paymentMethod' => 'CASH_OTHER', // * (NOT REQUIRED)
            ];
            return $result_create_receipt = $helper->fetchApiAccurate('/accurate/api/sales-receipt/save.do', 'POST', $params, $body);
            // ==========
        }

        // $customer_code = 'G08899';
        $params = [
            // 'fields' => 'id,no,customerNo,transDate,number',
            // 'fields' => 'id,no,name,accountType,parentNo,memo,currencyCode',
            // 'fields' => 'id,no,name,mobilePhone,billStreet,shipStreet,taxStreet,branchId,branchName,email',
            // 'sp.pageSize' => 1000,
            'id' => 474013,
            // 'id' => 469660,
            // 'filter.keywords.val[0]' => '1101034.2024.02.00104',
            // 'filter.keywords.val[0]' => 'SI.2024.02.01699',
        ];
        $body = [];
        return $result_list = $helper->fetchApiAccurate('/accurate/api/sales-receipt/detail.do', 'GET', $params, $body);
        // return $result_list = $helper->fetchApiAccurate('/accurate/api/sales-invoice/detail.do', 'GET', $params, $body);
        // return $result_list = $helper->fetchApiAccurate('/accurate/api/glaccount/list.do', 'GET', $params, $body);
        // return $result_list = $helper->fetchApiAccurate('/accurate/api/item/detail.do', 'GET', $params, $body);

        $params = [
            'fields' => 'id,customerNo,name,mobilePhone,billStreet,shipStreet,taxStreet,branchId,branchName,email',
            // 'filter.keywords.val[0]' => 'G08899',
            // 'filter.keywords.val[1]' => 'G06668',
            // 'filter.keywords.val[2]' => 'G04959',
            // 'filter.keywords.val[3]' => 'G08794',
            // 'filter.keywords.val[4]' => 'G11178',
            // 'filter.keywords.val[5]' => 'G07935',
        ];
        $body = [];
        return $result_list = $helper->fetchApiAccurate('/accurate/api/customer/list.do', 'GET', $params, $body);
        if (!empty($result_list->d)) {
            $result_list = $result_list->d;
        }

        $params = [];
        $body = [];
        foreach ($result_list as $key => $customer) {
            $body['data[' . $key . '].id'] = $customer->id;
            $body['data[' . $key . '].name'] = $customer->name . ' (Edited)';
        }
        return $helper->fetchApiAccurate('/accurate/api/customer/bulk-save.do', 'POST', $params, $body);

        $accurate_token = Setting::where('name', 'accurate_token')->first();
        $accurate_session = Setting::where('name', 'accurate_session')->first();
        // return $accurate_session->value['access_token'];
        if ($accurate_token && $accurate_session) {

            // ? Get List Item
            $params = [
                'fields' => 'id,name',
            ];
            $url = $accurate_session->value['host'] . '/accurate/api/item/list.do?' . http_build_query($params);
            $opts = array(
                'http' =>
                array(
                    'method'  => 'GET',
                    'header'  => [
                        'Content-Type: application/x-www-form-urlencoded',
                        'Authorization: Bearer ' . $accurate_token->value['access_token'],
                        'X-Session-ID: ' . $accurate_session->value['session'],
                    ],
                )
            );
            $context  = stream_context_create($opts);
            $result = file_get_contents($url, false, $context);
            $result = json_decode($result);
            if (!empty($result->error_message)) {
                return $result->error_message;
            }

            return $result;

            // ? Create Item
            // $url = $accurate_session->value['host'] . '/accurate/api/item/save.do';
            // $data = [
            //     'name' => 'Test Item 01',
            //     'itemType' => 'INVENTORY',
            // ];
            // $postdata = http_build_query($data);
            // $opts = array(
            //     'http' =>
            //     array(
            //         'method'  => 'POST',
            //         'header'  => [
            //             'Content-Type: application/x-www-form-urlencoded',
            //             'Authorization: Bearer ' . $accurate_token->value['access_token'],
            //             'X-Session-ID: ' . $accurate_session->value['session'],
            //         ],
            //         'content' => $postdata
            //     )
            // );
            // $context  = stream_context_create($opts);
            // $result = file_get_contents($url, false, $context);
            // $result = json_decode($result);
            // if (!empty($result->error_message)) {
            //     return $result->error_message;
            // }

            // return $result;
        }
        return [
            'accurate_token' => $accurate_token,
            'accurate_session' => $accurate_session,
        ];
    }

    public function getVendorListFromAccurate()
    {
        $path_alias = '/accurate/api/vendor/list.do';
        // $data = AccurateTemporaryList::where('alias', $path_alias)->get();
        // if ($data) {
        //     return response()->json(['status' => 'success', 'data' => $data->d]);
        // }
        $helper = new Helper;
        $params = [
            'fields' => 'id,name,vendorBranchName,suspended,vendorNo',
            'filter.suspended' => false,
            'sp.pageSize' => 1000,
            // 'id' => 235924,
        ];
        $body = [];
        $result = $helper->fetchApiAccurate($path_alias, 'GET', $params, $body, true);
        // $result = $helper->fetchApiAccurate($path_alias, 'GET', $params, $body);
        if (is_object($result) && $result->s) {
            AccurateTemporaryList::updateOrCreate(
                [
                    'alias' => $path_alias,
                ],
                [
                    'data' => $result->d,
                    'updated_at' => now()
                ]
            );
            return response()->json(['status' => 'success', 'data' => $result->d]);
        } else {
            return response()->json(['status' => 'fail', 'message' => 'Failed to fetch vendor list'], 400);
        }
    }
}
