<?php

namespace App\Http\Controllers;

use Imagick;
// use DateTime;
// use App\Models\Store;
use Carbon\Carbon;
use App\Models\Order;
// use App\Models\Order;
// use Illuminate\Database\Eloquent\Builder;
// use Illuminate\Http\Request;
use App\Models\Store;
use App\Helper\Helper;
use GuzzleHttp\Client;
use App\Models\Address;
use App\Models\Setting;
use App\Models\Customer;
use App\Models\Purchase;
// use Illuminate\Support\Facades\Crypt;
use App\Helper\HelperIak;
use App\Models\Socialchat;
use App\Helper\HelperSocialchat;
use App\Models\NotificationSchedule;
use Illuminate\Support\Facades\File;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use App\ScheduleObjects\SendNotifReminder;
use App\ScheduleObjects\RecordCustomerStat;

class TestController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    // public function __construct()
    // {
    //     $this->middleware('auth');
    // }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */

    public function testNotifReminder($template)
    {
        $setting = Setting::where('name', 'settings')->with([
            'notifreminderimage01',
            'notifreminderimage02',
            'notifreminderimage03',
        ])->first();
        $text = '';
        $media = [];
        switch ($template) {
            case 't3':
                $text = $setting->value['notifremindermessage03'];
                $media = $setting->notifreminderimage03 ? [
                    'type' => 'image',
                    'name' => 'Gasplus.' . explode('.', $setting->notifreminderimage03->file_name)[1],
                    // 'mimetype' => $setting->notifreminderimage01->mime_type,
                    'url' => explode('?', $setting->notifreminderimage03->thumbnail(500))[0] . '?ik=' . env('CLOUDFLARE_IGNORE_KEY'),
                ] : [];
                break;

            case 't2':
                $text = $setting->value['notifremindermessage02'];
                $media = $setting->notifreminderimage02 ? [
                    'type' => 'image',
                    'name' => 'Gasplus.' . explode('.', $setting->notifreminderimage02->file_name)[1],
                    // 'mimetype' => $setting->notifreminderimage01->mime_type,
                    'url' => explode('?', $setting->notifreminderimage02->thumbnail(500))[0] . '?ik=' . env('CLOUDFLARE_IGNORE_KEY'),
                ] : [];
                break;

            default:
                $text = $setting->value['notifremindermessage01'];
                $media = $setting->notifreminderimage01 ? [
                    'type' => 'image',
                    'name' => 'Gasplus.' . explode('.', $setting->notifreminderimage01->file_name)[1],
                    // 'mimetype' => $setting->notifreminderimage01->mime_type,
                    'url' => explode('?', $setting->notifreminderimage01->thumbnail(500))[0] . '?ik=' . env('CLOUDFLARE_IGNORE_KEY'),
                ] : [];
                break;
        }
        // $text = str_replace('* ', '- ', $text);
        // $text = preg_replace('/(?<!\*)\*(?!\*)/', '_', $text);
        // $text = str_replace('**', '*', $text);
        $helperSocialchat = new HelperSocialchat();
        $conv_id_niko = '66358a5aaf9ef08bd30a96fe';
        $conv_id_pak_yatno = '664b1f6f6a7efbc6f0d0930d';
        $niko = Socialchat::where('conversation_id', $conv_id_niko)->first()->customer;
        $text_niko = str_replace('{{nama_pelanggan}}', $niko->name, $text);
        $pak_yatno = Socialchat::where('conversation_id', $conv_id_pak_yatno)->first()->customer;
        $text_pak_yatno = str_replace('{{nama_pelanggan}}', $pak_yatno->name, $text);
        // if ($media) {
        //     $helperSocialchat->sendMessage($conv_id_niko, '', $media);
        // }
        $is_success_notif_niko = $helperSocialchat->sendMessage($conv_id_niko, $text_niko, $media);
        // if ($media) {
        //     $helperSocialchat->sendMessage($conv_id_pak_yatno, '', $media);
        // }
        return $is_success_notif_pak_yatno = $helperSocialchat->sendMessage($conv_id_pak_yatno, $text_pak_yatno, $media);
    }

    public function setLastOrderCustomers()
    {
        // Set last order datetime each Customer

        $customers = Customer::distinct()
            ->whereNull('options')
            ->orWhereNull('options->last_order_at')
            ->orWhereNull('options->last_order_or_notified_at')
            ->count();
        // ->get();
        // ->simplePaginate(1000);
        return $customers;
        foreach ($customers as $key => $customer) {
            // if (isset($customer->options['last_order_at'])) {
            //     continue;
            // }
            $last_order = $customer->lastorder;
            if ($last_order) {
                $customer->options = array_merge((array)$customer->options, [
                    'last_order_at' => $last_order->created_at->format('Y-m-d H:i:s'),
                    // 'last_order_or_notified_at' => $last_order->created_at->format('Y-m-d H:i:s'),
                ]);
                $customer->save();
            }
            // if (isset($customer->options['last_order_or_notified_at'])) {
            //     continue;
            // }
            // $last_order = $customer->lastorder;
            // if ($last_order) {
            //     $customer->options = array_merge((array)$customer->options, ['last_order_or_notified_at' => $last_order->created_at->format('Y-m-d H:i:s')]);
            //     $customer->save();
            // }
        }
        // $all = Customer::count();
        $count = Customer::whereNotNull('options->last_order_at')->count();
        return $count . ' DONE ';
    }
    public function index()
    {
        $helper = new Helper;
        // $order = Order::find(328509);
        // return $result = $helper->accurateUpsertInvoice($order);
        // $purchase = Purchase::where('id', 37)
        //     ->with([
        //         'photoreceipts'
        //     ])
        //     ->first();

        // foreach ($purchase->photoreceipts as $photo) {
        //     $params = [];
        //     $imageUrl = url('public/gasplus/' . $photo->file_name);
        //     return $imageUrl = public_path('public/gasplus/' . $photo->file_name);
        //     $imageData = file_get_contents($imageUrl);
        //     $base64Image = base64_encode($imageData);
        //     $body = [
        //         'description' => '',
        //         'contentBase64' => $base64Image,
        //         'name' => pathinfo($photo->file_name, PATHINFO_FILENAME) . '.' . pathinfo($photo->file_name, PATHINFO_EXTENSION),
        //         'transactionType' => 'PI',
        //         'transactionId' => '266259',
        //     ];
        //     return $result_upload_attachment = $helper->fetchApiAccurate('/accurate/api/attachment/attach.do', 'POST', $params, $body, false, false, true);
        // }
        // return json_decode('ioajowef');
        // return explode('ERROR: ', 'ERROdR: No Faktur "07234345" sudah ada untuk Faktur dari Pemasok PT. Trical Langgeng Jaya');
        $body = [];
        // return $result_list = $helper->fetchApiAccurate('/accurate/api/sales-invoice/detail.do', 'GET', $params, $body);

        $params = [
            'fields' => 'id,branch,customer,transDate,number,totalAmount,product',
            // 'fields' => 'totalAmount,shipDate,dueDate,transDate,number,approvalStatus',
            'sp.pageSize' => 1000,
            // 'filter.dueDate.val[0]' => '22/05/2024',
            // 'filter.shipDate.val[0]' => '22/05/2024',
            'filter.transDate.val[0]' => '22/05/2024',
            'filter.branchName' => 'Gasplus Kedungmundu',
            'filter.customerId.val[0]' => 239000,
            'filter.customerId.val[1]' => 235971,
            'filter.customerId.val[2]' => 209557,
            // 'branchFilter' => [101900], // Palagan
            // 'id' => 494875,
            // 'id' => 230786,
            // 'filter.keywords.val[0]' => '1101034.2024.02.00104',
            // 'filter.keywords.val[0]' => 'SI.2024.02.01699',
            // 'filter.keywords.val[0]' => 'G12920',
            // 'filter.keywords.val[1]' => 'G12922',
            // 'filter.keywords.val[2]' => 'G12924',
            // 'fromDate' => '14/04/2024',
            // 'dueDateFilter' => '23/05/2024',
            // 'shipDateFilter' => ['22/05/2024'],
            // 'transDateFilter' => ['22/05/2024'],
            // 'toDate' => '14/04/2024',
            // 'itemNo' => 'IA',
            // 'warehouseName' => 'Gudang Palagan',
        ];
        // {
        //     "name": "Yamaha DDS jogja",
        //     "id": 230618,
        //     "customerNo": "G12920"
        //     },
        //     {
        //     "name": "Yamaha DDS jogja",
        //     "id": 230744,
        //     "customerNo": "G12922"
        //     },
        //     {
        //     "name": "Yamaha DDS jogja",
        //     "id": 230786,
        //     "customerNo": "G12924"
        //     }
        $body = [];
        return $result_list = $helper->fetchApiAccurate('/accurate/api/sales-invoice/list.do', 'GET', $params, $body);
        return 'TEST';
        // return $orders = Order::distinct()
        //     ->whereDate('created_at', "2025-05-06")
        //     ->whereNull('deleted_at')
        //     ->where('store_id', 1)
        //     ->where('status_id', '!=', 5)
        //     ->where(function ($q) {
        //         $q
        //             ->whereHas('accurateInvoice', function ($sq) {
        //                 $sq->whereNull('accurate_id');
        //             })->OrWhereHas('accurateReceipt', function ($sq) {
        //                 $sq->whereNull('accurate_id');
        //             })
        //             ->orWhereDoesntHave('accurateInvoice')
        //             // ->orWhereDoesntHave('accurateReceipt')
        //         ;
        //     })
        //     ->with([
        //         'accurateInvoice',
        //         'accurateReceipt',
        //     ])
        //     ->get();
        $helper = new Helper;
        $params = [
            // 'number' => 'PI.2025.05.00104',
            // 'fields' => 'id,name,vendorBranchName',
            // 'id' => '264267',
            // // 'sp.page' => $page,
            // 'sp.pageSize' => 10,
            // // 'filter.keywords.val[0]' => 'Gasplus Palagan',
        ];
        $body = [
            'billNumber' => "07234345",
            'branchId' => "50",
            'description' => "test",
            'detailItem[0].itemNo' => "KAC220",
            'detailItem[0].quantity' => 13,
            'detailItem[0].unitPrice' => 24234,
            'detailItem[0].warehouseName' => "Gudang Kedungmundu",
            'transDate' => "12/05/2025",
            // 'vendorNo' => "52",

            'vendorNo' => 'PTTLJ',
            // 'billNumber' => '123456786',
            // 'branchId' => '50',
            // // 'paymentTermName' => 'Tunai',
            // // 'paymentTermName' => 'net 7',
            // 'paymentTermName' => 'net 300',
            // // 'currencyCode' => 'IDR',
            // 'description' => 'TESTING AJA',
            // 'transDate' => date('d/m/Y'),
            // 'detailItem[0].itemNo' => 'IBG5',
            // 'detailItem[0].unitPrice' => 87500,
            // 'detailItem[0].quantity' => 7,
            // // 'detailItem[0].itemUnitName' => 9,
            // 'detailItem[0].warehouseName' => 'Gudang Kedungmundu',
            // 'detailItem[1].itemNo' => 'IBG12',
            // 'detailItem[1].unitPrice' => 186000,
            // 'detailItem[1].quantity' => 12,
            // // 'detailItem[1].itemUnitName' => 9,
            // 'detailItem[1].warehouseName' => 'Gudang Kedungmundu',
        ];
        return $result_item = $helper->fetchApiAccurate('/accurate/api/purchase-invoice/save.do', 'POST', $params, $body);
        // try {
        //     $imagick = new \Imagick();
        //     $imagick->newImage(100, 100, new \ImagickPixel('red'));
        //     $imagick->setImageFormat('png');
        //     header("Content-Type: image/png");
        //     echo $imagick;
        // } catch (\ImagickException $e) {
        //     return 'Imagick is not installed or not working: ' . $e->getMessage();
        // }
        // return 'TEST';
        // $helperSocialchat = new HelperSocialchat();
        // $customer = Customer::where('name', 'NOT LIKE', '%ASDT%')
        //     ->withCount(['orders' => function ($query) {
        //         $query->whereIn('store_id', [1, 8])
        //             ->whereYear('created_at', 2024);
        //     }])
        //     ->orderBy('orders_count', 'desc')
        //     ->first();
        // $last_socialchat = Socialchat::where('phone', $customer->phone)->orderBy('id', 'desc')->first();
        // if ($last_socialchat) {
        //     $customer->socialchat_conversation_id = $last_socialchat->conversation_id;
        //     $customer->save();
        // } else {
        //     if ($customer->lastorder) {
        //         $conversation_id = $helperSocialchat->getConversationId($customer->lastorder->store, $customer->phone);
        //         if ($conversation_id) {
        //             Socialchat::create([
        //                 'store_id' => $customer->lastorder->store_id,
        //                 'customer_id' => $customer->id,
        //                 'phone' => $customer->phone,
        //                 'conversation_id' => $conversation_id,
        //             ]);
        //             $customer->socialchat_conversation_id = $conversation_id;
        //             $customer->save();
        //         }
        //     }
        // }
        // return $customer;
        // return 'TEST';
        // $notif_schedule = NotificationSchedule::first();
        // $store_ids = collect($notif_schedule->others['store_ids'])->pluck('id')->toArray();
        // return $store_ids;
        // return $notif_schedule->others['store_ids'];
        // $store_ids = collect(json_decode('{"store_ids":[{"id":1},{"id":8},{"id":2}]}')->store_ids)->pluck('id')->toArray();
        // return $store_ids;
        // return 'TEST';
        // $recordcustomerstat = new RecordCustomerStat();
        // return $recordcustomerstat();
        // $text = '{link_feedback:pindah_rumah134}';
        // return $text = preg_replace_callback('/\{link_feedback:([a-z_0-9]+)\}/', function ($matches) {
        //     return '/test/' . $matches[1];
        // }, $text);

        // $text = '{link_feedback(pindah_rumah_13)}';
        // return $text = preg_replace_callback('/\{link_feedback\(([a-z_]+)\)\}/', function ($matches) {
        //     return '/test/' . $matches[1];
        // }, $text);


        // return 'TEST';
        // $key = 'your-secret-key';
        // $string = '1234567890';
        // $cipher = 'AES-128-ECB'; // Short encryption method
        // $encrypted = openssl_encrypt($string, $cipher, $key, OPENSSL_RAW_DATA);
        // $encryptedString = rtrim(strtr(base64_encode($encrypted), '+/', '-_'), '=');

        // $cipher = 'AES-128-ECB';
        // $decoded = base64_decode(strtr($encryptedString, '-_', '+/')); // Reverse transformation
        // return openssl_decrypt($decoded, $cipher, $key, OPENSSL_RAW_DATA);

        // return 'TEST';
        // $helperSocialchat = new HelperSocialchat;
        // $store = Store::find(8); // Concat;
        // return $helperSocialchat->getChannelId($store);
        // $conversationId = '66358a5aaf9ef08bd30a96fe';
        // $appId = env('SOCIALCHAT_APP_ID');
        // $secretKey = env('SOCIALCHAT_SECRET_KEY');
        // $bearer = base64_encode($appId . '_' . $secretKey);

        // $url = 'https://api.socialchat.id/partner/message/66358936af9ef08bd309f2fd';
        // $data = [
        //     'webhookUrl' => 'https://test.ordergasplus.online/api/test-webhook-socialchat?app_key=b0f003bf-11bf-49ae-b58a-b2da98b9709c',
        //     // 'webhookUrl' => 'https://test.ordergasplus.online/api',
        // ];

        //         $client = new Client();
        // $headers = [
        //   'Authorization' => 'JWT',
        // ];
        // $body = '{
        //   "webhookUrl": "https://api.webpage.com/execute",
        //   "callbackMessengerUrl": "https://webpage.com/callback/messenger",
        //   "callbackInstagramUrl": "https://webpage.com/callback/instagram"
        // }';
        // $request = new Request('PUT', 'http://localhost:5002/partner/', $headers, $body);
        // $res = $client->sendAsync($request)->wait();
        // echo $res->getBody();
        // $data['text'] = $msg;
        // if (!empty($media)) {
        //     $data['media'] = $media;
        // }
        // $postdata = http_build_query($data);
        // $opts = array(
        //     'http' =>
        //     array(
        //         'method'  => 'GET',
        //         'header'  => [
        //             // 'Content-Type: application/x-www-form-urlencoded',
        //             'Content-Type: application/json',
        //             'Authorization: Bearer ' . $bearer,
        //         ],
        //         // 'content' => $postdata
        //     )
        // );
        // $context  = stream_context_create($opts);
        // $result = @file_get_contents($url, false, $context);
        // return $result = json_decode($result);
        // return 'TEST';
        // $month01 = Carbon::now();
        // $month02 = Carbon::now();
        // $month03 = Carbon::now();
        // $month06 = Carbon::now();
        // $month01->subDays(30);
        // $month02->subDays(60);
        // $month03->subDays(90);
        // $month06->subDays(180);
        // $customers = Customer::distinct();
        // $customers->where(function ($query) {
        //     $query->where('options->ignore_notif_reminder', '!=', true)
        //         ->orWhereNull('options->ignore_notif_reminder')
        //     ;
        // });
        // $customers->whereDate("options->last_order_or_notified_at", '<=', $month01->toDateString());
        // return $customers->paginate(5);
        // // call and run invoice method new SendNotifReminder
        // $sendNotifReminder = new SendNotifReminder();
        // return $sendNotifReminder();
        // // return $sendNotifReminder;
        // return 'TEST';
        // // $setting = Setting::where('name', 'settings')->with([
        // //     'notifreminderimage01',
        // //     'notifreminderimage02',
        // //     'notifreminderimage03',
        // // ])->first();
        // // return explode('?', $setting->notifreminderimage01->thumbnail(500))[0] . '?app_key=b0f003bf-11bf-49ae-b58a-b2da98b9709c';
        // // return $setting->notifreminderimage01->thumbnail(500) . '?app_key=b0f003bf-11bf-49ae-b58a-b2da98b9709c';
        // return 'TEST';
        // $helperSocialchat = new HelperSocialchat();
        // return $isSuccesSendNotifViaSocialchat = $helperSocialchat->sendMessage('66358a5aaf9ef08bd30a96fe', 'test kirim gambar', [
        //     'type' => 'image',
        //     // 'type' => 'document',
        //     // 'name' => "w202.jpg",
        //     // 'mimetype' => 'image/jpg',
        //     // 'mimetype' => 'application/pdf',
        //     // 'url' => 'https://eforms.com/download/2019/01/Car-Vehicle-Receipt-Template.pdf',
        //     // 'url' => 'https://test.ordergasplus.online/public/gasplus/img/notif/LOGOCAKAR.jpg?app_key=b0f003bf-11bf-49ae-b58a-b2da98b9709c',
        //     // 'url' => 'https://farm1.staticflickr.com/722/31964170702_31070fed60_o.jpg',
        //     // 'url' => "https://test.ordergasplus.online/public/gasplus/iak/receipt/20250213/C250213-1M-352877810.pdf?ik=" . env('CLOUDFLARE_IGNORE_KEY'),
        //     'url' => "https://test.ordergasplus.online/public/gasplus/thumbnails/img/order-received/1000-1000/order-received-273593-1741051458.webp?ik=" . env('CLOUDFLARE_IGNORE_KEY'),
        //     // 'thumbnail' => $image_data,
        // ]);
        // return 'TEST';
        // // $date = $order->created_at->format('Ymd');
        // // $file_name = 'GP210111-0856' . '-' . '234345345345';
        // // $directoryPath = public_path('gasplus/iak/receipt/' . '20210111');
        // // $fullUrl = url('public/gasplus/iak/receipt/' . '20210111');

        // // return $fullUrl;
        $url = env('IAK_BASE_URL_POSTPAID') . '/api/v1/download/352874977';
        // // $url = env('IAK_BASE_URL_POSTPAID') . '/api/v1/download/352875557';
        try {
            $client = new Client();
            $response = $client->request('GET', $url);
            $result_pdf = $response->getBody();

            // Check if the result is a PDF
            if (strpos($response->getHeaderLine('Content-Type'), 'application/pdf') === false) {

                $xml = simplexml_load_string($result_pdf);
                if ($xml && isset($xml->response_code) && (string)$xml->response_code !== '00') {
                    throw new \Exception('Error: ' . (string)$xml->message);
                }
                throw new \Exception('Error: The response is not a PDF.');
            }

            $directoryPath = public_path('gasplus/iak/pdf');
            if (!file_exists($directoryPath)) {
                mkdir($directoryPath, 0777, true);
            }
            $pdf_path = $directoryPath . '/352874977.pdf';
            file_put_contents($pdf_path, $result_pdf);

            // return url('/public/gasplus/iak/pdf/352874977.pdf');

            // $imagick = new \Imagick();
            // $imagick->readImage($pdf_path);
            // $imagick->setImageBackgroundColor('white');
            // $imagick = $imagick->flattenImages();
            // $imagick->setImageFormat('jpeg');
            // $image_path = $directoryPath . '/352874977.jpeg';
            // $imagick->writeImage($image_path);

            return url('/public/gasplus/iak/pdf/352874977.jpeg');

            // $image_data = base64_encode(file_get_contents($image_path));

            // $helperSocialchat = new HelperSocialchat();
            // return $isSuccesSendNotifViaSocialchat = $helperSocialchat->sendMessage('66358a5aaf9ef08bd30a96fe', 'Test satu dua tiga', [
            //     'type' => 'image',
            //     'name' => 'receipt.jpeg',
            //     'mimetype' => 'image/jpeg',
            //     // 'url' => 'https://eforms.com/download/2019/01/Car-Vehicle-Receipt-Template.pdf',
            //     'url' => 'https://mm.uma.ac.id/wp-content/uploads/2023/01/google-doc.jpeg',
            //     // 'thumbnail' => $image_data,
            // ]);
        } catch (\Throwable $th) {
            return $th;
        }

        return "TEST";
        // $customers = Customer::where('name', 'NOT LIKE', '%ASDT%')
        //     ->withCount(['orders' => function ($query) {
        //         $query->where('status_id', 6)
        //             ->whereNotNull('created_by');
        //     }])
        //     ->orderBy('orders_count', 'desc')
        //     // ->with([
        //     //     'addresses',
        //     //     'addresses.store',
        //     // ])
        //     ->take(10)
        //     ->get();

        // $helperSocialchat = new HelperSocialchat();
        $store_slug = 'palagan-06';
        $socialchats = Socialchat::where('store_id', 1)
            ->with([
                'store',
                'customer',
            ])
            ->skip(1250)
            ->take(250)
            ->get();

        // return $socialchats;

        $messages = [];
        foreach ($socialchats as $socialchat) {
            $nextToken = null;
            $appId = env('SOCIALCHAT_APP_ID');
            $secretKey = env('SOCIALCHAT_SECRET_KEY');
            $bearer = base64_encode($appId . '_' . $secretKey);
            // $channelId = '663b182baf20123f5da8fd74';
            $params = [];
            $opts = array(
                'http' =>
                array(
                    'method'  => 'GET',
                    'header'  => [
                        'Content-Type: application/x-www-form-urlencoded',
                        'Authorization: Bearer ' . $bearer,
                    ],
                )
            );
            do {
                if ($nextToken) {
                    $params = [
                        'nextToken' => $nextToken,
                    ];
                }
                $url = 'https://api.socialchat.id/partner/message/' . $socialchat->conversation_id . '?' . http_build_query($params);
                $context  = stream_context_create($opts);
                $result = @file_get_contents($url, false, $context);
                $result = json_decode($result);
                if (isset($result->messages)) {
                    $nextToken = $result->nextToken ?? null;
                    $messages = array_merge($messages, $result->messages);
                }
            } while ($nextToken);

            // return $messages;
        }

        // return $messages;
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set the headers
        $sheet->setCellValue('A1', 'sendAt');
        $sheet->setCellValue('B1', 'senderId');
        $sheet->setCellValue('C1', 'senderName');
        $sheet->setCellValue('D1', 'text');
        $sheet->setCellValue('E1', 'media');

        // Set the data
        $row = 2;
        foreach ($messages as $data) {
            $sheet->setCellValue('A' . $row, $data->sendAt);
            $sheet->setCellValue('B' . $row, $data->senderId);
            $sheet->setCellValue('C' . $row, isset($data->senderName) ? $data->senderName : $data->senderId);
            $sheet->setCellValue('D' . $row, $data->text);
            $sheet->setCellValue('E' . $row, isset($data->media) ? $data->media->url : '');
            $row++;
        }

        // Ensure the directory exists
        $directoryPath = public_path('chat');
        file_exists($directoryPath);
        if (!file_exists($directoryPath)) {
            mkdir($directoryPath, 0777, true);
        }
        $filePath = $directoryPath . '/gasplus-chat-' . $store_slug . '.csv';
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Csv($spreadsheet);
        $writer->save($filePath);

        return 'OK';

        // return response()->json($customers);

        // return response()->download($filePath);

        // $helper_iak = new HelperIak;
        // $pl = $helper_iak->fetchApiIakPrepaid('/api/pricelist/pln', 'POST', [], [
        //     'status' => 'active',
        // ], 'pl');
        // return $pl;
        // // $params = [
        // //     'fields' => 'id,no,name',
        // //     'sp.page' => 1,
        // //     'sp.pageSize' => 1000,

        // // ];
        // // $body = [];
        // // return $result_list_item = $helper->fetchApiAccurate('/accurate/api/item/list.do', 'GET', $params, $body);
        // $params = [
        //     'no' => 'KP220',
        //     // 'sp.page' => $page,
        //     // 'sp.pageSize' => 1000,
        // ];
        // $body = [];
        // return $result_list_item = $helper->fetchApiAccurate('/accurate/api/item/detail.do', 'GET', $params, $body);
        return "TEST";


        // ? Cancel Update Accurate ID on Gasplus DB
        $helper = new Helper();
        $inputFileType = 'Xlsx';
        $inputFileName = __DIR__ . '/concat_02_sync.xlsx';

        $reader = IOFactory::createReader($inputFileType);
        $spreadsheet = $reader->load($inputFileName);
        $worksheet = $spreadsheet->getActiveSheet();
        $dataArray = $worksheet->toArray();

        foreach ($dataArray as $key => $row) {
            $accurate_id = trim($row[2]);
            $is_cancel = trim($row[11]);
            if ($is_cancel == 'CANCEL' && !empty($accurate_id)) {
                Address::where('accurate_id', $accurate_id)->update([
                    'accurate_id' => NULL,
                ]);
            }
        }

        return 'CANCEL DONE';


        // ? Update Accurate ID to Gasplus DB
        $helper = new Helper();
        $inputFileType = 'Xlsx';
        $inputFileName = __DIR__ . '/concat_02.xlsx';

        $reader = IOFactory::createReader($inputFileType);
        $spreadsheet = $reader->load($inputFileName);
        $worksheet = $spreadsheet->getActiveSheet();
        // $worksheet->removeRow(1, 1);
        // $worksheet->setCellValue([2, 1], 'Batch');
        $worksheet->setCellValue([5, 1], 'Similarity');
        // $worksheet->setCellValue([8, 1], 'Gasplus Phone');
        $worksheet->setCellValue([12, 1], 'Gasplus Address');
        $dataArray = $worksheet->toArray();

        $updated_count = 0;
        $row_count = 0;

        foreach ($dataArray as $key => $row) {
            // $similarity = trim($row[5]);
            // if ($row_count == 1000) break;
            // if (!empty($similarity)) continue;
            $accurate_id = trim($row[2]);
            $name = trim($row[3]);
            $address01 = trim($row[17]);
            $address02 = trim($row[12]);
            $phone01 = $helper->convertPhone($row[7]);
            $phone02 = $helper->convertPhone($row[6]);
            if ($name === 'Nama') continue;
            $row_count++;



            // $addresses = Address::whereIn('store_id', [8])
            //     // ->where('accurate_id', '!=', $accurate_id)
            //     ->whereNull('accurate_id')
            //     ->whereHas('customer', function ($query) use ($name) {
            //         $query->where('name', $name);
            //     })
            //     ->get();

            // if ($addresses->count() > 0 && !empty($accurate_id)) {
            //     foreach ($addresses as $customer_address) {
            //         $data_address = $address02 ? $address02 : $address01;
            //         if (!empty($data_address)) {
            //             $address_length = strlen($data_address);
            //             $address_similarity = similar_text($data_address, $customer_address->address);
            //             $address_percentage = $address_similarity / $address_length * 100;
            //             $address_accurate_found = Address::where('accurate_id', $accurate_id)->first();
            //             if (!$address_accurate_found && $address_percentage >= 25) {
            //                 $customer_address->update([
            //                     'accurate_id' => $accurate_id,
            //                 ]);
            //                 $updated_count += 1;
            //                 $worksheet->setCellValue([2, $key + 1], '5');
            //                 $worksheet->setCellValue([5, $key + 1], $address_percentage);
            //                 // $worksheet->setCellValue([8, $key + 1], $customer->phone);
            //                 $worksheet->setCellValue([12, $key + 1], $customer_address->address);
            //                 break;
            //             }
            //         }
            //     }
            // }


            $customers = Customer::whereHas('addresses', function ($query) use ($accurate_id) {
                $query->whereIn('store_id', [8])
                    // ->where('accurate_id', '!=', $accurate_id)
                    ->whereNull('accurate_id');
            })
                ->where('name', $name)
                ->where(function ($query) use ($phone01, $phone02) {
                    $query->where('phone', $phone01)
                        ->orWhere('phone', $phone02);
                })
                ->get();
            if ($customers->count() == 0) {
                $customers = Customer::whereHas('addresses', function ($query) use ($accurate_id) {
                    $query->whereIn('store_id', [8])
                        // ->where('accurate_id', '!=', $accurate_id)
                        ->whereNull('accurate_id');
                })
                    ->where('name', $name)
                    ->get();
            }
            $addresses_candidate = [];
            if ($customers->count() > 0 && !empty($accurate_id)) {
                foreach ($customers as $customer) {
                    // return $accurate_id;
                    // return $addresses = $customer->addresses()
                    //     // ->whereNotIn('accurate_id', [$accurate_id])
                    //     ->get();
                    foreach ($customer->addresses as $customer_address) {
                        $data_address = $address02 ? $address02 : $address01;
                        if (!empty($data_address)) {
                            $address_length = strlen($data_address);
                            $address_similarity = similar_text($data_address, $customer_address->address);
                            $address_percentage = $address_similarity / $address_length * 100;
                            array_push($addresses_candidate, [
                                'address_id' => $customer_address->id,
                                'similarity_percentage' => $address_percentage,
                                'customer_phone' => $customer->phone,
                                'customer_address' => $customer_address->address,
                            ]);
                        }
                    }
                }
            }
            if (count($addresses_candidate) > 0) {
                usort($addresses_candidate, function ($a, $b) {
                    return $b['similarity_percentage'] - $a['similarity_percentage'];
                });

                $address_prediction = $addresses_candidate[0];

                $address_accurate_found = Address::where('accurate_id', $accurate_id)->first();
                if (!$address_accurate_found) {
                    Address::where('id', $address_prediction['address_id'])->update([
                        'accurate_id' => $accurate_id,
                    ]);
                    $updated_count++;
                    // $worksheet->setCellValue([2, $key + 1], '3');
                    $worksheet->setCellValue([5, $key + 1], $address_prediction['similarity_percentage']);
                    $worksheet->setCellValue([8, $key + 1], $address_prediction['customer_phone']);
                    $worksheet->setCellValue([12, $key + 1], $address_prediction['customer_address']);
                }
            }
        }

        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        // $date = date_create();
        // $writer->save(__DIR__ . "/concat_new_" . date_timestamp_get($date) . ".xlsx");
        $writer->save(__DIR__ . "/concat_02_sync.xlsx");
        // dd($dataArray);
        // $helper = new Helper();
        // $order = Order::find(232159);
        // return $helper->createOrderMessage($order, true);
        // return 'test';
        // // $helper = new Helper;
        // // return $helper->getDirectionMapbox('110.366939,-7.783167', '110.418851,-7.748538');
        // $helper = new Helper;
        // $store_id = 9;
        // // $addresses = Address::where('store_id', $store_id)->get();
        // $addresses = Address::distinct()
        //     // ->where('id', 12109)
        //     ->where('store_id', $store_id)
        //     ->where(function (Builder $subquery) {
        //         $subquery
        //             ->doesntHave('storedistances')
        //             ->orWhereHas('storedistances', function (Builder $query) {
        //                 $query
        //                     ->where('distance_meter', '<=', 7000)
        //                     ->whereNull('distance_meter_by_route')
        //                     //
        //                 ;
        //             })
        //             //
        //         ;
        //     })
        //     ->with(['storedistances', 'store'])
        //     ->limit(5)
        //     ->get();
        // // $addresses = Address::all();
        // // return $addresses;
        // // return $addresses->count();
        // $stores = Store::all();

        // // $address = Address::find(5137);
        // // $address = Address::find(14330);

        // // $address->storedistances()->where('store_id', 1)->first();

        // $helper->countDistances($stores, $addresses);

        // foreach ($addresses as $address) {
        //     // return $address->label;
        //     if (!$address->latlng) continue;
        //     return $helper->countDistances($stores, $address);
        // }
        return $row_count . '/' . $updated_count;
    }
}
