<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class ReportController extends Controller
{
    /**
     * Display the report page with dummy data.
     */
    public function index(Request $request): Response
    {
        // Sample stores data
        $stores = [
            ['id' => 1, 'slug' => 'store-1', 'name' => 'Gasplus Jakarta Pusat'],
            ['id' => 2, 'slug' => 'store-2', 'name' => 'Gasplus Jakarta Selatan'],
            ['id' => 3, 'slug' => 'store-3', 'name' => 'Gasplus Jakarta Utara'],
            ['id' => 4, 'slug' => 'store-4', 'name' => 'Gasplus Bandung'],
            ['id' => 5, 'slug' => 'store-5', 'name' => 'Gasplus Surabaya'],
        ];

        // Sample user data
        $user = [
            'id' => 1,
            'role_id' => 1, // Admin role
            'email' => '<EMAIL>',
            'name' => 'Admin User',
            'options' => [
                'is_show_ltt' => true, // Show LTT (Lap. Tutup Toko) reports
            ]
        ];

        return Inertia::render('Report', [
            'store_slug' => $request->query('store', 'store-1'),
            'stores' => $stores,
            'user' => $user,
        ]);
    }
}
