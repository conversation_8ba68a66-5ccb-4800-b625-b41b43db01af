<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class JobsController extends Controller
{
    /**
     * Display the jobs page with dummy data.
     */
    public function index(Request $request): Response
    {
        // Sample stores data
        $stores = [
            ['id' => 1, 'slug' => 'store-1', 'name' => 'Gasplus Jakarta Pusat'],
            ['id' => 2, 'slug' => 'store-2', 'name' => 'Gasplus Jakarta Selatan'],
            ['id' => 3, 'slug' => 'store-3', 'name' => 'Gasplus Jakarta Utara'],
            ['id' => 4, 'slug' => 'store-4', 'name' => 'Gasplus Bandung'],
            ['id' => 5, 'slug' => 'store-5', 'name' => 'Gasplus Surabaya'],
        ];

        // Sample drivers data
        $drivers = [
            [
                'id' => 1,
                'name' => '<PERSON> Rizki',
                'orders_diambil' => rand(3, 8),
                'orders_terkirim' => rand(5, 12),
                'orders_selesai' => rand(8, 15),
                'orders_batal' => rand(0, 2),
                'marketings_diambil' => rand(2, 6),
                'marketings_selesai' => rand(8, 12),
                'marketings_batal' => rand(0, 1),
            ],
            [
                'id' => 2,
                'name' => 'Budi Santoso',
                'orders_diambil' => rand(2, 7),
                'orders_terkirim' => rand(4, 10),
                'orders_selesai' => rand(6, 13),
                'orders_batal' => rand(0, 3),
                'marketings_diambil' => rand(3, 7),
                'marketings_selesai' => rand(7, 11),
                'marketings_batal' => rand(0, 2),
            ],
            [
                'id' => 3,
                'name' => 'Candra Wijaya',
                'orders_diambil' => rand(4, 9),
                'orders_terkirim' => rand(6, 14),
                'orders_selesai' => rand(9, 16),
                'orders_batal' => rand(0, 1),
                'marketings_diambil' => rand(1, 5),
                'marketings_selesai' => rand(9, 13),
                'marketings_batal' => rand(0, 1),
            ],
        ];

        // Sample banks data for payment methods
        $banks = [
            ['bank_name' => 'BCA', 'orders_count' => rand(5, 15), 'order_total' => rand(500000, 2000000)],
            ['bank_name' => 'Mandiri', 'orders_count' => rand(3, 10), 'order_total' => rand(300000, 1500000)],
            ['bank_name' => 'BNI', 'orders_count' => rand(2, 8), 'order_total' => rand(200000, 1200000)],
            ['bank_name' => 'QRIS', 'orders_count' => rand(8, 20), 'order_total' => rand(800000, 3000000)],
            ['bank_name' => 'GoPay', 'orders_count' => rand(4, 12), 'order_total' => rand(400000, 1800000)],
        ];

        // Generate realistic job counts
        $orderTotalCount = rand(25, 50);
        $orderBebasCount = rand(3, 8);
        $orderDiambilCount = array_sum(array_column($drivers, 'orders_diambil'));
        $orderTerkirimCount = array_sum(array_column($drivers, 'orders_terkirim'));
        $orderSelesaiCount = array_sum(array_column($drivers, 'orders_selesai'));
        $orderBatalCount = array_sum(array_column($drivers, 'orders_batal'));

        // Marketing (B-Bro) counts
        $marketingDiambilCount = array_sum(array_column($drivers, 'marketings_diambil'));
        $marketingSelesaiCount = array_sum(array_column($drivers, 'marketings_selesai'));
        $marketingBatalCount = array_sum(array_column($drivers, 'marketings_batal'));
        $marketingPlusSelesaiCount = rand(0, 3);

        // Financial data
        $orderTerkirimCashCount = rand(8, 15);
        $orderTerkirimCashTotal = rand(800000, 2500000);
        $orderTerkirimNonCashCount = rand(10, 20);
        $orderTerkirimNonCashTotal = rand(1200000, 3500000);

        $orderSelesaiCashCount = rand(12, 25);
        $orderSelesaiCashTotal = rand(1500000, 4000000);
        $orderSelesaiSplitCashTotal = rand(200000, 800000);
        $orderSelesaiNonCashCount = rand(15, 30);
        $orderSelesaiNonCashTotal = rand(2000000, 5000000);
        $orderSelesaiInvoiceCount = rand(3, 8);
        $orderSelesaiInvoiceTotal = rand(500000, 1500000);

        $invoiceConfirmedNonCashCount = rand(5, 12);
        $invoiceConfirmedNonCashTotal = rand(800000, 2200000);
        $invoiceTotalCount = rand(8, 18);

        $orderTotal = $orderSelesaiCashTotal + $orderSelesaiSplitCashTotal + 
                     $orderSelesaiNonCashTotal + $orderSelesaiInvoiceTotal;

        // Operating costs
        $totalOperatingcostToday = rand(200000, 800000);

        // Monthly overtime statistics
        $totalJobThisMonth = rand(800, 1200);
        $totalJobThisMonthOvertime = rand(50, 120);
        $totalJobToday = rand(25, 50);
        $totalJobTodayOvertime = rand(2, 8);

        // Date navigation
        $currentDate = now();
        $dateNow = $currentDate->format('Y-m-d');
        $datePrev = $currentDate->copy()->subDay()->format('Y-m-d');
        $dateNext = $currentDate->copy()->addDay()->format('Y-m-d');
        $day = $currentDate->format('l, d F Y');

        // Store data
        $store = [
            'id' => 1,
            'marketing_each_day' => 15,
            'hide_freejob_fordelman' => false,
        ];

        // Current marketing for B-Bro modal
        $currentMarketing = [
            'id' => 1,
            'photos' => [], // Empty for demo
        ];

        // Unfinished orders from previous days
        $ordersUnfinish = [
            ['date' => $currentDate->copy()->subDays(2)->format('Y-m-d'), 'count' => rand(1, 3)],
            ['date' => $currentDate->copy()->subDays(1)->format('Y-m-d'), 'count' => rand(0, 2)],
        ];

        // Sample user data
        $user = [
            'id' => 1,
            'role_id' => 1, // Admin role
            'email' => '<EMAIL>',
            'name' => 'Admin User'
        ];

        return Inertia::render('Jobs', [
            'store_slug' => $request->query('store', 'store-1'),
            'stores' => $stores,
            'drivers' => $drivers,
            'banks' => $banks,
            'date_now' => $dateNow,
            'date_prev' => $datePrev,
            'date_next' => $dateNext,
            'day' => $day,
            'store' => $store,
            'current_marketing' => $currentMarketing,
            'order_total_count' => $orderTotalCount,
            'order_bebas_count' => $orderBebasCount,
            'order_diambil_count' => $orderDiambilCount,
            'order_terkirim_count' => $orderTerkirimCount,
            'order_selesai_count' => $orderSelesaiCount,
            'order_batal_count' => $orderBatalCount,
            'marketing_diambil_count' => $marketingDiambilCount,
            'marketing_selesai_count' => $marketingSelesaiCount,
            'marketing_batal_count' => $marketingBatalCount,
            'marketing_plus_selesai_count' => $marketingPlusSelesaiCount,
            'order_terkirim_cash_count' => $orderTerkirimCashCount,
            'order_terkirim_cash_total' => $orderTerkirimCashTotal,
            'order_terkirim_non_cash_count' => $orderTerkirimNonCashCount,
            'order_terkirim_non_cash_total' => $orderTerkirimNonCashTotal,
            'order_selesai_cash_count' => $orderSelesaiCashCount,
            'order_selesai_cash_total' => $orderSelesaiCashTotal,
            'order_selesai_split_cash_total' => $orderSelesaiSplitCashTotal,
            'order_selesai_non_cash_count' => $orderSelesaiNonCashCount,
            'order_selesai_non_cash_total' => $orderSelesaiNonCashTotal,
            'order_selesai_invoice_count' => $orderSelesaiInvoiceCount,
            'order_selesai_invoice_total' => $orderSelesaiInvoiceTotal,
            'invoice_confirmed_non_cash_count' => $invoiceConfirmedNonCashCount,
            'invoice_confirmed_non_cash_total' => $invoiceConfirmedNonCashTotal,
            'invoice_total_count' => $invoiceTotalCount,
            'order_total' => $orderTotal,
            'total_operatingcost_today' => $totalOperatingcostToday,
            'total_job_this_month' => $totalJobThisMonth,
            'total_job_this_month_overtime' => $totalJobThisMonthOvertime,
            'total_job_today' => $totalJobToday,
            'total_job_today_overtime' => $totalJobTodayOvertime,
            'orders_unfinish' => $ordersUnfinish,
            'user' => $user,
        ]);
    }
}
