<?php

namespace App\Http\Controllers;

use DateTime;
use App\Models\Area;
use App\Models\Bank;
use App\Models\User;
use App\Models\Media;
use App\Models\Order;
// use Illuminate\Filesystem\Filesystem;
// use App\Models\User;
// use App\Models\Store;
use App\Models\Store;
use App\Helper\Helper;
use App\Models\Merger;
use App\Models\Address;
use App\Models\Deposit;
use App\Models\Invoice;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Employee;
use App\Models\Marketing;
use App\Models\Socialchat;
use App\Models\ProductStore;
use Illuminate\Http\Request;
use App\Jobs\ProcessJobConfirm;
use App\Helper\HelperSocialchat;
use Illuminate\Support\Facades\DB;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Cache;
use Illuminate\Database\Eloquent\Builder;
// use PhpParser\Node\Stmt\Foreach_;

class AjaxController extends Controller
{
    public function changestore(Request $request)
    {
        // if (!$request->ajax()) {
        //     return false;
        // }
        $data = $request->all();
        $is_success = Address::whereIn('id', (array)$data['address_ids'])->update([
            'store_id' => $data['destination_store_id'],
        ]);
        return $is_success;
    }

    public function ordersetdriver(Request $request)
    {
        if (!$request->ajax()) {
            return false;
        }
        $data = $request->all();
        $order = Order::find($data['order_id']);
        $inputs = [
            'status_id' => $data['driver_id'] ? 3 : 1,
            'driver_id' => $data['driver_id'] ? $data['driver_id'] : null
        ];
        if ($data['amount_return'] && intval($data['amount_return']) > -1) {
            $inputs['amount_return'] = intval($data['amount_return']);
            // $inputs['amount_will_pay'] = $order->total + intval($data['amount_return']);
            $inputs['total_deposit'] = $order->total_after_deposit + intval($data['amount_return']);
        }
        if ($data['driver_id']) {
            $employee = Employee::where('user_id', $data['driver_id'])->first();
            if ($employee) {
                $inputs['armada_id'] = $employee->armada ? $employee->armada->id : null;
            }
        }
        $is_success = $order->update($inputs);
        return $is_success;
    }

    public function marketingsetdriver(Request $request)
    {
        // if (!$request->ajax()) {
        //     return false;
        // }
        $data = $request->all();
        $marketing = Marketing::find($data['marketing_id']);
        $inputs = [
            // 'status_id' => $data['driver_id'] ? 3 : 1,
            'driver_id' => $data['driver_id'] ? $data['driver_id'] : null
        ];
        $is_success = $marketing->update($inputs);
        return $is_success;
    }

    public function ordersetstatus(Request $request)
    {
        if (!$request->ajax()) {
            return false;
        }
        $data = $request->all();
        $status_ids = [
            'selesai' => 4,
            'batal' => 5,
        ];
        $order = Order::find($data['order_id']);
        $address = Address::find($order->address_id);
        if (empty($address->latlng) && !empty($data['latlng'])) {
            Address::where('id', $order->address_id)->update([
                'latlng' => $data['latlng'],
            ]);
        }
        $is_success = Order::where('id', $data['order_id'])->update([
            'status_id' => $status_ids[$data['status']],
            'received_at' => date('Y-m-d H:i:s'),
            'received_latlng' => $data['latlng'] ? $data['latlng'] : null,
            'received_latlng_accuracy' => $data['latlng_accuracy'] ? $data['latlng_accuracy'] : null,
        ]);
        return $is_success;
    }

    public function searchcustomer(Request $request)
    {
        // if (!$request->ajax()) {
        //     return false;
        // }
        $data = $request->all();

        $store_id = $data['store_id'];
        $store = Store::find($store_id);
        $q = $data['query'];
        $addresses_inside_store = Address::with([
            'customer' => function ($q) {
                $q->withCount('addresses');
            },
            'store',
            // 'customer.merger',
            // 'customer.merger.maincustomer',
            // 'customer.merger.maincustomer.addresses',
        ])
            ->where("store_id", $store_id)
            ->where(function ($query) use ($q) {
                // $query->where('address', "like", "%" . $q . "%");
                $query->whereHas('customer', function (Builder $subquery) use ($q) {
                    $subquery->where('name', "like", "%" . $q . "%")
                        ->orWhere('phone', "like", "%" . $q . "%");
                })
                    // ->orWhere('address', "like", "%" . $q . "%")
                ;

                $words = explode(' ', $q);
                foreach ($words as $word) {
                    $query->orWhereHas('customer', function (Builder $subquery) use ($word) {
                        $subquery->where('name', "like", "%" . $word . "%")
                            ->orWhere('phone', "like", "%" . $word . "%");
                    })
                        // ->orWhere('address', "like", "%" . $word . "%")
                    ;
                }
            })
            ->distinct()
            ->limit(50)
            ->get();

        $addresses_outside_store = Address::with([
            'customer' => function ($q) {
                $q->withCount('addresses');
            },
            'store',
            // 'customer.merger',
            // 'customer.merger.maincustomer',
            // 'customer.merger.maincustomer.addresses',
        ])
            ->where("store_id", '!=', $store_id)
            ->where(function ($query) use ($q) {
                // $query->where('address', "like", "%" . $q . "%");
                $query->whereHas('customer', function (Builder $subquery) use ($q) {
                    $subquery->where('name', "like", "%" . $q . "%")
                        ->orWhere('phone', "like", "%" . $q . "%");
                })
                    // ->orWhere('address', "like", "%" . $q . "%")
                ;

                $words = explode(' ', $q);
                foreach ($words as $word) {
                    $query->orWhereHas('customer', function (Builder $subquery) use ($word) {
                        $subquery->where('name', "like", "%" . $word . "%")
                            ->orWhere('phone', "like", "%" . $word . "%");
                    })
                        // ->orWhere('address', "like", "%" . $word . "%")
                    ;
                }
            });

        if ($store->storesearchexcludes) {
            $addresses_outside_store->whereNotIn('store_id', $store->storesearchexcludes->pluck('id'));
        }

        $addresses_outside_store = $addresses_outside_store->distinct()
            ->limit(50)
            ->get();

        // $addresses = Customer::with(['addresses'])
        //     ->whereHas('addresses', function (Builder $query) use ($store_id, $q) {
        //         $query->where('store_id', $store_id);
        //     })

        //     ->orWhereHas('addresses', function (Builder $query) use ($store_id, $q) {
        //         $query->where('address', "like", "%" . $q . "%");
        //     })
        //     ->orWhere('name', "like", "%" . $q . "%")
        //     ->orWhere('phone', "like", "%" . $q . "%");


        // return $addresses->orderBy('name')
        $addresses = $addresses_inside_store
            ->concat($addresses_outside_store)
            // ->unique('id')
        ;

        $result = [];
        foreach ($addresses as $address) {
            if ($address->customer) {
                if ($address->customer && (int)$address->customer->is_main === 0 && (int)$address->customer->merger_id > -1) {
                    $merger = Merger::where('id', $address->customer->merger_id)
                        ->with([
                            'maincustomer',
                            'maincustomer.addresses',
                            'maincustomer.addresses.customer' => function ($q) {
                                $q->withCount('addresses');
                            },
                            'maincustomer.addresses.store',
                        ])
                        ->first();
                    foreach ($merger->maincustomer->addresses as $adrs) {
                        array_push($result, $adrs);
                    }
                } else {
                    array_push($result, $address);
                }
            }
        }

        usort($result, function ($a, $b) {
            return $b['customer_id'] - $a['customer_id'];
        });
        return $result;
    }

    public function searcharea(Request $request)
    {
        $data = $request->all();

        $store_id = $data['store_id'];
        $q = $data['query'];
        $result = Area::where(function ($query) use ($store_id) {
            $query->where('store_id', $store_id)
                ->orWhereNull('store_id');
        })
            ->where(function ($query) use ($q) {
                $query->where('name', "like", "%" . $q . "%")
                    ->orWhere('latlng', "like", "%" . $q . "%");
            });

        return $result->distinct()
            ->limit(25)
            ->get();
    }

    public function getdrivers(Request $request)
    {
        $data = $request->all();

        $store_id = $data['store_id'];
        $result = User::where("store_id", $store_id)
            ->where("role_id", 5);
        // ->where(function($query) use ($q) {
        //     $query->where('name', "like", "%" . $q . "%");
        //         // ->orWhere('email', "like", "%" . $q . "%");
        // });

        return $result->distinct()
            // ->limit(25)
            ->get();
    }

    public function getcoordinategmap(Request $request)
    {
        $data = $request->all();
        $url = $data['url'];

        // URL HAVE COORDINATE
        if (strpos($url, '/@')) {
            $arr = explode('/@', $url);
            if ($arr[1]) {
                $zero = explode(',', $arr[1]);
                if ($zero[0] && $zero[1]) {
                    return $zero[0] . ',' . $zero[1];
                }
                return $url;
            }
            return $url;

            // URL DONT HAVE COORDINATE
        } else {
            if (strpos($url, 'goo.gl')) {
                $headers = get_headers($url, 1);
                $target = $headers['Location'];
                // if (strpos($url, '/@') !== false) {
                $url = $target;
            }
            $ch = curl_init();
            // $url = env('API_URL').'/api/revalidate?secret='.env('API_TOKEN');
            // $url = 'https://www.google.com/maps/place/Jambon Asri Residen, Jatimulyo Indah No.5, Kricak, Kec. Tegalrejo, Kota Yogyakarta, Daerah Istimewa Yogyakarta 55242/data=!4m2!3m1!1s0x2e7a5869f93512cd:0x40e9036c91cb1556?utm_source=mstt_1&entry=gps&g_ep=CAESBzEwLjgyLjEYACD___________8BKgA=';
            // $url = 'https://maps.app.goo.gl/tvKcrpRMEbjT2VbBA';
            // $url = 'https://www.google.com/maps/place/Jambon+Asri+Residen,+Jatimulyo+Indah+No.5,+Kricak,+Kec.+Tegalrejo,+Kota+Yogyakarta,+Daerah+Istimewa+Yogyakarta+55242/data=!4m2!3m1!1s0x2e7a5869f93512cd:0x40e9036c91cb1556?utm_source=mstt_1&entry=gps&g_ep=CAESBzEwLjgyLjEYACD___________8BKgA%3D';
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_BINARYTRANSFER, true);
            // curl_setopt($ch,CURLOPT_HEADER, false); 
            curl_setopt($ch, CURLOPT_TIMEOUT, 4);
            // curl_setopt($ch,CURLOPT_FRESH_CONNECT, true); 
            // curl_setopt($ch,CURLOPT_FOLLOWLOCATION, false); 
            // curl_setopt($ch,CURLOPT_FAILONERROR, true); 

            $output = curl_exec($ch);

            curl_close($ch);

            // dd($output);

            if ($output == false) {
                return $url;
            }

            // return htmlentities($output);

            $arr = explode('/@', $output);
            if ($arr[1]) {
                $zero = explode(',', $arr[1]);
                if ($zero[0] && $zero[1]) {
                    return $zero[0] . ',' . $zero[1];
                } else {
                    return $url;
                }
            }
            return $url;
        }

        // return response()->json(
        // 	[
        // 		'success' => true,
        // 		// 'contacts' => 'test',
        // 		'url' => $url,
        // 	],
        // 	200
        // );


    }

    public function searchproduct(Request $request)
    {
        // if (!$request->ajax()) {
        //     return false;
        // }
        $data = $request->all();

        $store_id = $data['store_id'];
        $q = isset($data['query']) && !empty($data['query']) ? $data['query'] : false;
        $address = Address::find($data['address_id']);
        $customer_id = $address && $address->customer && $address->customer->products->count() > 0 ? $address->customer_id : false;
        $products = Product::distinct()
            ->with([
                'featurephoto',
                'ppobpricelist',
            ])
            ->leftJoin('product_store', 'product_store.product_id', '=', 'products.id')
            ->leftJoin('customer_product', 'customer_product.product_id', '=', 'products.id')
            ->where('product_store.store_id', $store_id)
            // ->where('product_store.is_available', 1)
            // ->where('product_store.stock', '>', 0)
        ;

        if ($q) {
            $products->where(function ($query) use ($q) {
                $query->where('products.name', "like", "%" . $q . "%")
                    ->orWhere('products.code', "like", "%" . $q . "%");
            });
        }

        if ($customer_id) {
            $products->where('customer_product.customer_id', $customer_id)
                // ->where('customer_product.is_available', 1)
                // ->where('customer_product.stock', '>', 0)
                ->select(
                    'products.*',
                    'product_store.local_price',
                    'product_store.stock',
                    'product_store.is_available',
                    'customer_product.local_price as special_price',
                    'customer_product.stock as special_stock',
                    'customer_product.is_available as special_is_available',
                );
        } else {
            $products->select(
                'products.*',
                'product_store.local_price',
                'product_store.stock',
                'product_store.is_available',
            );
        }

        $products = $products->orderBy('products.sort_order')
            ->get();

        foreach ($products as &$product) {
            $product->featurephoto_url = $product->featurephoto ? $product->featurephoto->thumbnail($width = 354, $height = 354, $filters = []) : null;
            $product->is_ppob = $product->is_ppob;
        }
        return $products;
    }

    public function validatecustomer(Request $request)
    {
        if (!$request->ajax()) {
            return false;
        }
        $data = $request->all();
        $helper = new Helper();
        $phone = $helper->convertPhone($data['phone']);

        return $customer = Customer::where('phone', "like", "%" . $phone . "%")->count();
    }

    public function orderfinish(Request $request)
    {
        if (!$request->ajax()) {
            return false;
        }
        $helper = new Helper();

        $image_path = 'public/gasplus/img' . DIRECTORY_SEPARATOR . 'order-received';
        if (!File::isDirectory($image_path)) {
            File::makeDirectory($image_path, 0777, true, true);
        }
        $image_path = 'public/gasplus/img' . DIRECTORY_SEPARATOR . 'payment-proof';
        if (!File::isDirectory($image_path)) {
            File::makeDirectory($image_path, 0777, true, true);
        }
        $data = $request->all();
        $photo = $request->file('photo');
        $photo_paymentproof = $request->file('photo_paymentproof');
        $order = Order::find($data['order_id']);

        $is_success = $order->update([
            'status_id' => 4, // completed
            'received_by' => $data['received_by'], // completed
            'received_at' => date('Y-m-d H:i:s'),
            'payment_method_ask' => isset($data['payment_method_ask']) && !empty($data['payment_method_ask']) ? $data['payment_method_ask'] : null,
            'payment_note' => isset($data['payment_note']) && !empty($data['payment_note']) ? $data['payment_note'] : null,
            'driver_note' => isset($data['driver_note']) && !empty($data['driver_note']) ? $data['driver_note'] : null,
            'received_latlng' => isset($data['received_latlng']) && !empty($data['received_latlng']) ? $data['received_latlng'] : null,
            'received_latlng_accuracy' => isset($data['received_latlng_accuracy']) && !empty($data['received_latlng_accuracy']) ? $data['received_latlng_accuracy'] : null,
            'amount_split_to_cash' => isset($data['amount_split_to_cash']) && !empty($data['amount_split_to_cash']) ? $data['amount_split_to_cash'] : null,
            'deposit_balance_after' => $order->deposit_balance_before != 0 && isset($data['amount_split_to_cash']) && !empty($data['amount_split_to_cash']) ? $order->deposit_balance_after + $data['amount_split_to_cash'] : $order->deposit_balance_after,
            // 'amount_pay' => $order->total_after_deposit > 0 && $data['payment_method_ask'] == 'non-cash' && isset($data['amount_split_to_cash']) && !empty($data['amount_split_to_cash']) ? $order->total_after_deposit - $data['amount_split_to_cash'] : null,
        ]);

        $order = Order::find($data['order_id']);
        // if ($order->deposit_balance_before != 0 && isset($data['amount_split_to_cash']) && !empty($data['amount_split_to_cash'])) {
        //     $deposit = Deposit::where('order_id', $order->id)->first();
        //     $new_amount = $deposit->amount + $data['amount_split_to_cash'];
        //     $new_balance = $deposit->balance + $data['amount_split_to_cash'];
        //     $deposit->update([
        //         'amount' => $new_amount,
        //         'balance' => $new_balance,
        //         'note' => 'JOB TERKIRIM',
        //     ]);
        //     $order->customer->addDeposit($data['amount_split_to_cash']);
        // }
        if ($photo) {
            $helper = new Helper();
            $data_photo = $helper->saveImage(
                'order-received',
                $order,
                $photo,
                1000,
                'receivephoto',
                'App\Models\Order',
            );
            $order->receivephoto()->create($data_photo);
        }
        if ($photo_paymentproof) {
            $helper = new Helper();
            $data_photo_paymentproof = $helper->saveImage(
                'payment-proof',
                $order,
                $photo_paymentproof,
                1000,
                'paymentproof',
                'App\Models\Order',
            );
            $order->paymentproof()->create($data_photo_paymentproof);
        }
        $order->countDistance(true);
        $order->countDuration(true);

        $helper->generateDepositHistory($order->customer_id, $order->created_at);
        $helper->accurateUpsertInvoice($order);

        // Send Notification by WhatsApp
        // -------------------------------------
        if ($order->store->is_notif_wa && $order->customer->notif_status === 'on') {
            // $messages = [];
            $msg_order_delivered = $order->store->msg_order_delivered ? $order->store->msg_order_delivered : 'Yeay pesanan sudah sampai. Terima kasih sudah berbelanja di Gasplus.';
            $msg_order_delivered .= $order->driver ? '<br><br>_Dikirim oleh: ' . $order->driver->name . '_' : '';
            $url_gmap = $order->store->meta['url_gmap'] ?? '';
            $is_ignore_review_invitation = $order->customer->options['ignore_review_invitation'] ?? 0;
            if ($url_gmap && !$is_ignore_review_invitation) {
                $msg_review_invitation = $order->store->meta['msg_review_invitation'] ?? 'Kalau kamu puas, yuk bantu kami berkembang dengan kasih review-nya 🙏 di link berikut: ';
                $msg_review_invitation .= ' ' . url('/rv/' . $helper->miniEncrypt($order->customer_id . '-' . $order->store_id));
                $msg_order_delivered .= '<br><br>' . $msg_review_invitation;
            }
            // Customer
            // array_push($messages, [
            //     'order_id'      => $order->id,
            //     'to'      => $helper->convertPhone($order->receiver_phone ? $order->receiver_phone : $order->customer->phone),
            //     'message'    => $msg_order_delivered,
            // ]);
            // $helper->processSendNotifToWablas($messages);

            // ? Get conversation chat
            $helperSocialchat = new HelperSocialchat();
            $conversationId = null;
            $phone = $order->receiver_phone ?? $order->customer->phone;
            $socialchat = Socialchat::where("store_id", $order->store_id)
                ->where('customer_id', $order->customer_id)
                ->where('phone', $phone)
                ->orderBy('id', 'desc')
                ->first();
            if ($socialchat) {
                $conversationId = $socialchat->conversation_id;
            } else {
                $conversationId = $helperSocialchat->getConversationId($order->store, $phone);
                if ($conversationId) {
                    Socialchat::create([
                        'store_id' => $order->store_id,
                        'customer_id' => $order->customer_id,
                        'phone' => $phone,
                        'conversation_id' => $conversationId,
                    ]);
                }
            }

            // ? Try send notif via socialchat
            // $isSuccesSendNotifViaSocialchat = false;
            if ($conversationId) {
                $order->customer->socialchat_conversation_id = $conversationId;
                $order->customer->save();
                $msg_order_delivered = str_replace(PHP_EOL, '', $msg_order_delivered);
                $msg_order_delivered = str_replace('<br>', PHP_EOL, $msg_order_delivered);

                // $media = $order->receivephoto ? [
                //     'type' => 'image',
                //     // 'name' => 'Gasplus.' . explode('.', $order->receivephoto->file_name)[1],
                //     // 'mimetype' => $setting->notifreminderimage01->mime_type,
                //     'url' => explode('?', $order->receivephoto->thumbnail(500))[0] . '?ik=' . env('CLOUDFLARE_IGNORE_KEY'),
                // ] : [];
                if ($order->receivephoto) {
                    $media =  [
                        'type' => 'image',
                        // 'name' => 'Gasplus.' . explode('.', $order->receivephoto->file_name)[1],
                        // 'mimetype' => $setting->notifreminderimage01->mime_type,
                        'url' => explode('?', $order->receivephoto->thumbnail(500))[0] . '?ik=' . env('CLOUDFLARE_IGNORE_KEY'),
                    ];
                    $helperSocialchat->sendMessage($conversationId, '', $media);
                    sleep(0.25);
                }
                $isSuccesSendNotifViaSocialchat = $helperSocialchat->sendMessage($conversationId, $msg_order_delivered);
                // $query_url['alert'] = 'notif-sent';
                // $query_url['alertmsg'] = 'Job ' . $order->code . ' berhasil terkirim.';
            }
        }

        return $is_success;
    }

    public function uploadpaymentproof(Request $request)
    {
        // if (!$request->ajax()) {
        //     return false;
        // }
        $data = $request->all();
        $is_success = true;

        if ($data['order_type'] == 'invoice') {
            $image_path = 'public/gasplus/img' . DIRECTORY_SEPARATOR . 'invoice-payment-proof';
            if (!File::isDirectory($image_path)) {
                File::makeDirectory($image_path, 0777, true, true);
            }
            $photo_paymentproof = $request->file('photo_paymentproof');
            $invoice = Invoice::find($data['order_id']);
            if ($invoice->paymentproof) {
                $file_path = public_path('gasplus' . DIRECTORY_SEPARATOR . $invoice->paymentproof->file_name);
                if (File::exists($file_path)) {
                    File::delete($file_path);
                }
                $invoice->paymentproof()->delete();
            }
            if ($photo_paymentproof) {
                $helper = new Helper();
                $data_photo_paymentproof = $helper->saveImage(
                    'invoice-payment-proof',
                    $invoice,
                    $photo_paymentproof,
                    1000,
                    'paymentproof',
                    'App\Models\Invoice',
                );
                $is_success = $invoice->paymentproof()->create($data_photo_paymentproof);
            }
        } else {
            $image_path = 'public/gasplus/img' . DIRECTORY_SEPARATOR . 'payment-proof';
            if (!File::isDirectory($image_path)) {
                File::makeDirectory($image_path, 0777, true, true);
            }
            $photo_paymentproof = $request->file('photo_paymentproof');
            $order = Order::find($data['order_id']);
            if ($order->paymentproof) {
                $file_path = public_path('gasplus' . DIRECTORY_SEPARATOR . $order->paymentproof->file_name);
                if (File::exists($file_path)) {
                    File::delete($file_path);
                }
                $order->paymentproof()->delete();
            }
            if ($photo_paymentproof) {
                $helper = new Helper();
                $data_photo_paymentproof = $helper->saveImage(
                    'payment-proof',
                    $order,
                    $photo_paymentproof,
                    1000,
                    'paymentproof',
                    'App\Models\Order',
                );
                $is_success = $order->paymentproof()->create($data_photo_paymentproof);
            }
        }
        return $is_success;
    }

    public function uploadaddress(Request $request)
    {
        // if (!$request->ajax()) {
        //     return false;
        // }
        $data = $request->all();
        $is_success = false;

        $image_path = 'public/gasplus/img' . DIRECTORY_SEPARATOR . 'address';
        if (!File::isDirectory($image_path)) {
            File::makeDirectory($image_path, 0777, true, true);
        }
        $photo_address = $request->file('photo_address');
        $order = Order::where('id', $data['order_id'])
            ->with(['address'])
            ->first();
        if ($order && $order->address && $order->address->photo) {
            $file_path = public_path('gasplus' . DIRECTORY_SEPARATOR . $order->address->photo->file_name);
            if (File::exists($file_path)) {
                File::delete($file_path);
            }
            $order->address->photo()->delete();
        }
        if ($order && $photo_address) {
            $helper = new Helper();
            $data_photo_address = $helper->saveImage(
                'address',
                $order->address,
                $photo_address,
                1000,
                'photo',
                'App\Models\Address',
            );
            $is_success = $order->address->photo()->create($data_photo_address);
        }
        return $is_success;
    }

    public function uploadphotomarketing(Request $request)
    {
        // if (!$request->ajax()) {
        //     return false;
        // }
        $image_path = 'public/gasplus/img' . DIRECTORY_SEPARATOR . 'photo-marketing';
        if (!File::isDirectory($image_path)) {
            File::makeDirectory($image_path, 0777, true, true);
        }
        $data = $request->all();
        $photo_marketing = $request->file('photo_marketing');

        $is_success = true;
        $marketing = Marketing::find($data['marketing_id']);
        // if ($marketing->paymentproof) {
        //     $file_path = public_path('gasplus'.DIRECTORY_SEPARATOR.$marketing->paymentproof->file_name);
        //     if (File::exists($file_path)) {
        //         File::delete($file_path);
        //     }
        //     $marketing->paymentproof()->delete();
        // }
        if ($photo_marketing) {
            $helper = new Helper();
            $data_photo_marketing = $helper->saveImage(
                'photo-marketing',
                $marketing,
                $photo_marketing,
                1000,
                'photos',
                'App\Models\Marketing',
            );
            $is_success = $marketing->photos()->create($data_photo_marketing);
        }
        return $is_success;
    }

    public function deletephotomarketing(Request $request)
    {
        $data = $request->all();
        $media = Media::findOrFail($data['id'])->find($data['id']);
        // $file_path = public_path(
        //     'medias' . DIRECTORY_SEPARATOR . $media->file_name
        // );
        // if (File::exists($file_path)) {
        //     File::delete($file_path);
        // }
        $is_success = $media->delete();
        return $is_success;
    }

    public function ordercancel(Request $request)
    {
        if (!$request->ajax()) {
            return false;
        }
        $helper = new Helper();
        $data = $request->all();
        $order = Order::find($data['order_id']);
        $amount = ($order->amount_deposit_used != 0 && $order->amount_deposit_used != null) ? ($order->customer->deposit_amount + $order->amount_deposit_used) : 0;
        // Deposit::where('order_id', $order->id)->update([
        //     'amount' => 0,
        //     'balance' => $amount,
        //     'note' => 'CANCEL JOB'. (isset($data['driver_note']) && !empty($data['driver_note']) ? ': '.$data['driver_note'] : ''),
        // ]);
        // if ($order->amount_deposit_used != 0 && $order->amount_deposit_used != null) {
        //     $order->customer->setDeposit($amount);
        // }
        $is_success = $order->update([
            'status_id' => 5, // canceled
            'received_at' => date('Y-m-d H:i:s'),
            'driver_note' => isset($data['driver_note']) && !empty($data['driver_note']) ? $data['driver_note'] : null,
            'cancel_by' => auth()->user()->id,
            'received_latlng' => isset($data['received_latlng']) && !empty($data['received_latlng']) ? $data['received_latlng'] : null,
            'received_latlng_accuracy' => isset($data['received_latlng_accuracy']) && !empty($data['received_latlng_accuracy']) ? $data['received_latlng_accuracy'] : null,
            'amount_deposit_used' => 0,
            'total_after_deposit' => $order->total,
            'deposit_balance_after' => $order->deposit_balance_before,
        ]);
        foreach ($order->orderproducts as $orderproducts) {
            $product_store = ProductStore::where('store_id', $order->store_id)
                ->where('product_id', $orderproducts->product_id)
                ->where('stock', '!=', 999)
                ->first();
            if ($product_store) {
                $product_store->stock = $product_store->stock + $orderproducts->qty;
                $product_store->save();
            }
        }
        $order->countDistance(true);
        $order->countDuration(true);
        $helper->generateDepositHistory($order->customer_id, $order->created_at);
        $helper->accurateDeleteInvoice($order);

        // Send Notification by WhatsApp
        // -------------------------------------
        if ($order->store->is_notif_wa && $order->customer->notif_status === 'on') {
            // $messages = [];
            // $data = [
            //     '{code_job}' => $order->code,
            //     '{driver_note}' => $order->driver_note,
            // ];
            // $msg_order_canceled = $order->store->msg_order_canceled ? $order->store->msg_order_canceled : 'Order *{code_job}* dibatalkan 🙏. Terima kasih sudah berbelanja di Gasplus. _Catatan: {driver_note}_';
            // $msg_order_canceled = strtr($msg_order_canceled, $data);
            // // Customer
            // array_push($messages, [
            //     'order_id'      => $order->id,
            //     'to'      => $helper->convertPhone($order->receiver_phone ? $order->receiver_phone : $order->customer->phone),
            //     'message'    => $msg_order_canceled,
            // ]);
            // $helper->processSendNotifToWablas($messages);
        }

        return $is_success;
    }

    public function marketingcancel(Request $request)
    {
        // if (!$request->ajax()) {
        //     return false;
        // }
        $helper = new Helper();
        $data = $request->all();
        $marketing = Marketing::find($data['marketing_id']);

        $is_success = $marketing->update([
            'status_id' => 5, // canceled
            'received_at' => date('Y-m-d H:i:s'),
            'driver_note' => isset($data['driver_note']) && !empty($data['driver_note']) ? $data['driver_note'] : null,
            'cancel_by' => auth()->user()->id,
            'received_latlng' => isset($data['received_latlng']) && !empty($data['received_latlng']) ? $data['received_latlng'] : null,
            'received_latlng_accuracy' => isset($data['received_latlng_accuracy']) && !empty($data['received_latlng_accuracy']) ? $data['received_latlng_accuracy'] : null,
        ]);
        $marketing->countDistance(true);
        $marketing->countDuration(true);
        return $is_success;
    }

    public function orderchangedate(Request $request)
    {
        if (!$request->ajax()) {
            return false;
        }
        $helper = new Helper();
        $data = $request->all();
        unset($data['_token']);
        $order = Order::find($data['order_id']);

        $old_created_at = new DateTime($order->created_at);
        $new_created_at = new DateTime($data['created_at']);

        if ($old_created_at != $new_created_at) {
            $data['sequence_per_day'] = Order::where('store_id', $order->store_id)->whereDate('created_at', $new_created_at->format('Y-m-d'))->max('sequence_per_day') + 1;
            $data['code'] = $order->getCode($order->store_id, $data['created_at'], $data['sequence_per_day']);
        }

        // $data['created_at'] = new DateTime($data['created_at'].' '.$old_created_at->format('H:i:s'));
        $data['created_at'] = new DateTime($data['created_at']);

        $is_success = $order->update($data);
        if ($is_success) {
            $helper->accurateUpsertInvoice($order);
        }
        // $order->countDistance(true);
        // $order->countDuration(true);
        // $helper->generateDepositHistory($order->customer_id, $order->created_at);
        return $is_success;
    }

    public function orderconfirm(Request $request)
    {
        if (!$request->ajax()) {
            return false;
        }
        $helper = new Helper();
        $data = $request->all();

        $inputs = [
            'status_id' => 6,
            'confirmed_by' => auth()->user()->id,
            'amount_pay' => isset($data['amount_pay']) && !empty($data['amount_pay']) ? $data['amount_pay'] : null,
            'amount_split_to_cash' => isset($data['amount_split_to_cash']) && !empty($data['amount_split_to_cash']) ? $data['amount_split_to_cash'] : null,
            'confirm_note' => isset($data['confirm_note']) && !empty($data['confirm_note']) ? $data['confirm_note'] : null,
        ];
        if (isset($data['bank_id']) && !empty($data['bank_id']) && $data['bank_id'] == 'invoice') {
            $inputs['payment_method_confirmed'] = 'invoice';
            // $inputs['amount_pay'] = null;
            // $inputs['amount_split_to_cash'] = null;
        } else if (isset($data['bank_id']) && !empty($data['bank_id'])) {
            $bank = Bank::find($data['bank_id']);
            $inputs['bank_id'] = intval($data['bank_id']);
            if (str_contains(strtolower($bank->bank_name), 'qris')) {
                $inputs['payment_method_confirmed'] = 'qris';
            } else {
                $inputs['payment_method_confirmed'] = 'transfer';
            }
        } else {
            $inputs['payment_method_confirmed'] = 'cash';
        }

        $order = Order::find($data['order_id']);
        // $total_amount_pay = $inputs['amount_pay'] + $inputs['amount_split_to_cash'];
        // if ($order->payment_method_ask != 'cash') {
        //     $new_deposit = $total_amount_pay - $order->total_after_deposit;
        //     $inputs['deposit_balance_after'] = $order->deposit_balance_after + $new_deposit;
        //     Deposit::where('order_id', $order->id)->update([
        //         'amount' => $inputs['deposit_balance_after'] - $order->deposit_balance_before,
        //         'balance' => $inputs['deposit_balance_after'],
        //         'note' => 'CONFIRM JOB'. (isset($data['confirm_note']) && !empty($data['confirm_note']) ? ': '.$data['confirm_note'] : null),
        //     ]);
        //     $order->customer->setDeposit($inputs['deposit_balance_after']);
        // }
        if ($order->deposit_job) {
            Deposit::create([
                'job_order_id' => $order->id,
                'customer_id' => $order->customer_id,
                'amount' => $order->deposit_job,
                'balance' => $order->customer->deposit_amount + $order->deposit_job,
                'note' => 'JOB DEPOSIT: ' . $data['confirm_note'],
            ]);
        }

        $is_success = $order->update($inputs);
        ProcessJobConfirm::dispatch([
            'order_id' => $order->id,
        ]);
        // $helper->generateDepositHistory($order->customer_id, $order->created_at);
        return $is_success;
    }

    public function ordercheckinmarketing(Request $request)
    {
        // if (!$request->ajax()) {
        //     return false;
        // }
        $helper = new Helper();
        $data = $request->all();

        $inputs = [
            'status_id' => 6,
            'confirmed_by' => auth()->user()->id,
            'received_at' => date('Y-m-d H:i:s'),
            'driver_note' => isset($data['driver_note']) && !empty($data['driver_note']) ? $data['driver_note'] : null,
            'received_latlng' => isset($data['received_latlng']) && !empty($data['received_latlng']) ? $data['received_latlng'] : null,
            'received_latlng_accuracy' => isset($data['received_latlng_accuracy']) && !empty($data['received_latlng_accuracy']) ? $data['received_latlng_accuracy'] : null,
        ];

        $marketing = Marketing::find($data['marketing_id']);
        $is_success = $marketing->update($inputs);
        $count = DB::table('marketings')
            ->where('status_id', 6)
            ->where('area_id', $marketing->area_id)
            ->selectRaw('count(*) as checkin')
            ->first();
        Area::where('id', $marketing->area_id)->update([
            'count' => $count->checkin,
        ]);
        return $is_success;
    }

    public function submitbbro(Request $request)
    {
        $helper = new Helper();
        $data = $request->all();
        $created_at = new DateTime();
        $marketing = Marketing::find($data['marketing_id']);
        $sequence_per_day = Marketing::where('store_id', $marketing->store_id)->whereDate('created_at', $created_at->format('Y-m-d'))->max('sequence_per_day') + 1;
        $store = Store::find($marketing->store_id);

        $inputs = [
            'code' => $marketing->getCode($marketing->store_id, null, $sequence_per_day),
            'sequence_per_day' => $sequence_per_day,
            'status_id' => 6,
            'confirmed_by' => auth()->user()->id,
            'created_at' => $created_at,
            'received_at' => date('Y-m-d H:i:s'),
            'driver_note' => isset($data['driver_note']) && !empty($data['driver_note']) ? $data['driver_note'] : null,
            'received_latlng' => isset($data['received_latlng']) && !empty($data['received_latlng']) ? $data['received_latlng'] : null,
            'received_latlng_accuracy' => isset($data['received_latlng_accuracy']) && !empty($data['received_latlng_accuracy']) ? $data['received_latlng_accuracy'] : null,
        ];

        $is_success = $marketing->update($inputs);

        Cache::forget('calendar_marketings_' . $store->slug . '_' . $created_at->format('Y-m-d') . '_' . auth()->user()->id);
        Cache::forget('jobs_marketing_' . $store->slug . '_' . $created_at->format('Y-m-d') . '_' . auth()->user()->id);
        Cache::forget('jobs_marketing_plus_' . $store->slug . '_' . $created_at->format('Y-m-d') . '_' . auth()->user()->id);

        return $is_success;
    }

    public function blashissend(Request $request)
    {
        $data = $request->all();
        $customer = Customer::find($data['customer_id']);
        $is_success = $customer->update([
            'blash_updated_at' => date('Y-m-d H:i:s'),
        ]);
        return [
            'status' => $is_success,
            'data' => $data,
        ];
    }

    public function offlineordersubmit(Request $request)
    {
        if (!$request->ajax()) {
            return false;
        }
        $helper = new Helper();
        $data = $request->all();
        $is_success = 'empty';

        // return [
        //     'status' => false,
        //     'data' => $data,
        // ];

        if (isset($data['gp_orders_finished']) && !empty($data['gp_orders_finished'])) {
            foreach ($data['gp_orders_finished'] as $key => $job) {
                $the_order = Order::find($job['order_id']);
                $is_success = $the_order->update([
                    'status_id' => 4, // completed
                    'received_by' => $job['received_by'], // completed
                    'received_at' => $job['received_at'],
                    'payment_method_ask' => isset($job['payment_method_ask']) && !empty($job['payment_method_ask']) ? $job['payment_method_ask'] : null,
                    'payment_note' => isset($job['payment_note']) && !empty($job['payment_note']) ? $job['payment_note'] : null,
                    'driver_note' => isset($job['driver_note']) && !empty($job['driver_note']) ? $job['driver_note'] : null,
                    'received_latlng' => isset($job['received_latlng']) && !empty($job['received_latlng']) ? $job['received_latlng'] : null,
                    'received_latlng_accuracy' => isset($job['received_latlng_accuracy']) && !empty($job['received_latlng_accuracy']) ? $job['received_latlng_accuracy'] : null,
                    'amount_split_to_cash' => isset($job['amount_split_to_cash']) && !empty($job['amount_split_to_cash']) ? $job['amount_split_to_cash'] : null,
                    'deposit_balance_after' => isset($job['amount_split_to_cash']) && !empty($job['amount_split_to_cash']) ? $the_order->deposit_balance_after + $job['amount_split_to_cash'] : $the_order->deposit_balance_after,
                    // 'amount_pay' => $order->total_after_deposit > 0 && $data['payment_method_ask'] == 'non-cash' && isset($data['amount_split_to_cash']) && !empty($data['amount_split_to_cash']) ? $order->total_after_deposit - $data['amount_split_to_cash'] : null,
                ]);
                $the_order->countDistance(true);
                $the_order->countDuration(true);

                // $the_order = Order::find($job['order_id']);
                // if ($the_order->customer->deposit_amount != 0 && isset($job['amount_split_to_cash']) && !empty($job['amount_split_to_cash'])) {
                //     $deposit = Deposit::where('order_id', $the_order->id)->first();
                //     $new_amount = $deposit->amount + $job['amount_split_to_cash'];
                //     $new_balance = $deposit->balance + $job['amount_split_to_cash'];
                //     $deposit->update([
                //         'amount' => $new_amount,
                //         'balance' => $new_balance,
                //         'note' => 'JOB TERKIRIM',
                //     ]);
                //     $the_order->customer->setDeposit($the_order->customer->deposit_amount + $job['amount_split_to_cash']);
                // }
                $helper->generateDepositHistory($the_order->customer_id, $the_order->created_at);
            }
        }
        if (isset($data['gp_orders_canceled']) && !empty($data['gp_orders_canceled'])) {
            foreach ($data['gp_orders_canceled'] as $key => $job) {
                $the_order = Order::find($job['order_id']);
                if ($the_order->status_id != 5) {
                    // $the_order = Order::find($job['order_id']);
                    // $amount = ($the_order->amount_deposit_used != 0 && $the_order->amount_deposit_used != null) ? ($the_order->customer->deposit_amount + $the_order->amount_deposit_used) : 0;
                    // Deposit::where('order_id', $the_order->id)->update([
                    //     'amount' => 0,
                    //     'balance' => $amount,
                    //     'note' => 'CANCEL JOB'. (isset($job['driver_note']) && !empty($job['driver_note']) ? ': '.$job['driver_note'] : ''),
                    // ]);
                    // if ($the_order->amount_deposit_used != 0 && $the_order->amount_deposit_used != null) {
                    //     $the_order->customer->setDeposit($amount);
                    // }
                    $is_success = $the_order->update([
                        'status_id' => 5, // canceled
                        'received_at' => $job['received_at'],
                        'driver_note' => isset($job['driver_note']) && !empty($job['driver_note']) ? $job['driver_note'] : null,
                        'cancel_by' => auth()->user()->id,
                        'received_latlng' => isset($job['received_latlng']) && !empty($job['received_latlng']) ? $job['received_latlng'] : null,
                        'received_latlng_accuracy' => isset($job['received_latlng_accuracy']) && !empty($job['received_latlng_accuracy']) ? $job['received_latlng_accuracy'] : null,
                        'amount_deposit_used' => 0,
                        'total_after_deposit' => $the_order->total,
                        'deposit_balance_after' => $the_order->deposit_balance_before,
                    ]);
                    $the_order->countDistance(true);
                    $the_order->countDuration(true);
                    $helper->generateDepositHistory($the_order->customer_id, $the_order->created_at);
                }
            }
        }

        return [
            'status' => $is_success,
            'data' => $data,
        ];
    }

    public function generatereportdeposit(Request $request, $store_id, $date)
    {
        if (!$request->ajax()) {
            return false;
        }
        $helper = new Helper();
        $customers = Customer::distinct()
            ->whereHas('orders', function (Builder $query) use ($store_id, $date) {
                $query->where('store_id', $store_id)
                    ->whereDate('created_at', $date);
            })
            // ->with(['orders'])
            ->get();

        foreach ($customers as $customer) {
            if ($customer) {
                $helper->generateDepositHistory($customer->id, $date);
            }
        }

        return true;
    }
}
