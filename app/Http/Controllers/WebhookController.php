<?php

namespace App\Http\Controllers;

// use Illuminate\Support\Facades\File;
// use Illuminate\Filesystem\Filesystem;
// use App\Models\User;
// use App\Models\Store;
use App\Models\Store;
use App\Helper\Helper;
use App\Models\Setting;
use App\Models\ProductStore;
use Illuminate\Http\Request;
use App\Jobs\ProcessUpdateStock;
use App\Http\Controllers\Controller;

class WebhookController extends Controller
{
    public function webhookAcc(Request $request)
    {
        $data = $request->all();
        if (!isset($data['key']) || $data['key'] !== env('WEBHOOK_CLIENT_SECRET')) {
            return response()->json(
                [
                    'status' => "error",
                    'error' => 'Key invalid!',
                ],
                500
            );
        }
        if (!isset($data['0']['type'])) {
            return response()->json([
                'status' => "success",
                'message' => 'Unknown type!',
            ], 500);
        }
        $type = $data['0']['type'];
        switch ($type) {
            case 'PURCHASE_INVOICE':
                // ProcessUpdateStock::dispatch($data[0]['data'][0]);
                $helper = new Helper;
                $data = $data[0]['data'][0];
                $stores = null;
                $product_count = 0;
                if (isset($data['purchaseInvoiceId']) && !empty($data['purchaseInvoiceId'])) {
                    $params = [
                        'id' => $data['purchaseInvoiceId']
                    ];
                    $body = [];
                    $result_invoice_detail = $helper->fetchApiAccurate('/accurate/api/purchase-invoice/detail.do', 'GET', $params, $body);
                    Setting::updateOrCreate(
                        [
                            'name' => 'test-result-invoice-detail',
                        ],
                        ['value' => $result_invoice_detail]
                    );
                    if (is_object($result_invoice_detail) && $result_invoice_detail->s) {
                        $branchId = $result_invoice_detail->d->branchId;
                        if ($branchId) {
                            $stores = Store::where('accurate_branch_id', $branchId)->get();
                        }
                    }
                }
                if (!$stores) {
                    // $stores = Store::all();
                    return response()->json([
                        'status' => "success",
                        'message' => 'Unknown store!',
                    ], 500);
                }
                foreach ($stores as $store) {
                    if (empty($store->accurate_warehouse_name)) continue;
                    $params = [
                        'sp.pageSize' => 1000,
                        'warehouseName' => $store->accurate_warehouse_name,
                    ];
                    $body = [];
                    $result = $helper->fetchApiAccurate('/accurate/api/item/list-stock.do', 'GET', $params, $body, true);
                    if ($result) {
                        foreach ($result->d as $stock) {
                            ProductStore::where('store_id', $store->id)
                                ->whereHas('product', function ($q) use ($stock) {
                                    $q->where('accurate_item_no', $stock->no);
                                })
                                ->update([
                                    'stock' => $stock->quantity,
                                ]);
                            $product_count++;
                        }
                    }
                }
                // Setting::updateOrCreate(
                //     [
                //         'name' => 'test-stock-updated',
                //     ],
                //     ['value' => [
                //         'product_count' => $product_count,
                //     ]]
                // );

                // $helper->fetchApiAccurate('https://account.accurate.id/api/webhook-renew.do', 'GET', null, null, false, true);
                return response()->json([
                    'status' => "success",
                    'message' => 'Success update ' . $product_count . ' products',
                ]);
                break;

            default:
                return response()->json([
                    'status' => "success",
                    'message' => 'Unknown type!',
                ], 500);
                break;
        }
    }
}
