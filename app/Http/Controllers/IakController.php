<?php

namespace App\Http\Controllers;

// use Illuminate\Http\Request;
use App\Helper\Helper;
use App\Models\Product;
use App\Models\Setting;
use App\Helper\HelperIak;
use App\Models\Socialchat;
use App\Models\OrderProduct;
use Illuminate\Http\Request;
use App\Models\PpobPricelist;
use App\Helper\HelperSocialchat;
use App\Models\PpobResponseData;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Cache;
// use App\Models\PpobCustomerData;

class IakController extends Controller
{
    public function callback(Request $request)
    {
        if (empty($request->query('app_key')) || $request->query('app_key') != env('WEBHOOK_CLIENT_SECRET')) {
            return response()->json(['error' => 'Key invalid!'], 500);
        }

        $data = $request->all();
        $data = (array)$data['data'] ?? null;

        if (!isset($data['ref_id'])) {
            return response()->json([
                'status' => 'error',
                'message' => 'Data empty!'
            ], 500);
        }

        // Setting::updateOrCreate(
        //     [
        //         'name' => 'iak-' . $data['ref_id'],
        //     ],
        //     ['value' => $data]
        // );

        $order_product = OrderProduct::where('ppob_ref_id', $data['ref_id'])->with([
            'order',
            'product',
            'ppobresponsedata',
        ])->first();

        if (!$order_product) {
            return response()->json([
                'status' => 'error',
                'message' => 'Order product not found',
            ], 400);
        }

        switch (intval($data['status'])) {
            case 0:
                $order_product->ppob_status = 'PROCESS';
                break;
            case 1:
                $order_product->ppob_status = 'SUCCESS';
                break;
            // case 2:
            //     $order_product->ppob_status = 'FAILED';
            //     break;
            default:
                $order_product->ppob_status = 'FAILED';
                break;
        }

        $ppob_response_data = PpobResponseData::updateOrCreate(
            [
                'ppob_ref_id' => $data['ref_id'],
                // 'order_product_id' => $order_product->id,
                'product_id' => $order_product->product_id,
                'customer_id' => $order_product->order->customer_id,
            ],
            [
                'response_data' => $data,
                'error_message' => intval($data['status']) >= 2 ? $data['message'] : null,
                'updated_at' => now()
            ]
        );

        $order_product->ppob_response_data_id = $ppob_response_data->id;
        $order_product->updated_at = now();
        $order_product->save();

        // ? Send notif to customer
        if ($data['message'] === 'SUCCESS') {
            // ? Update balance
            $helper_iak = new HelperIak();
            $path = '/api/check-balance';
            $additional_sign = 'bl';
            $method = 'POST';
            $params = [];
            $body = [];
            $result = $helper_iak->fetchApiIakPrepaid($path, $method, $params, $body, $additional_sign);
            if ($result && $result->rc === '00') {
                Setting::updateOrCreate(
                    [
                        'name' => 'iak_balance',
                    ],
                    [
                        'value' => $result,
                        'updated_at' => now()
                    ]
                );
                Cache::forget('iak_balance');
            }

            if (!$order_product->notif_sent_at) {
                // ? Get conversation chat
                $helperSocialchat = new HelperSocialchat();
                $conversationId = null;
                $order = $order_product->order;
                $phone = $order->receiver_phone ?? $order->customer->phone;
                $socialchat = Socialchat::where("store_id", $order->store_id)
                    ->where('customer_id', $order->customer_id)
                    ->where('phone', $phone)
                    ->orderBy('id', 'desc')
                    ->first();
                if ($socialchat) {
                    $conversationId = $socialchat->conversation_id;
                } else {
                    $conversationId = $helperSocialchat->getConversationId($order->store, $phone);
                    if ($conversationId) {
                        Socialchat::create([
                            'store_id' => $order->store_id,
                            'customer_id' => $order->customer_id,
                            'phone' => $phone,
                            'conversation_id' => $conversationId,
                        ]);
                    }
                }

                // ? Try send notif via socialchat
                if ($conversationId) {
                    $helper = new Helper();
                    $msg_ppob = $helper->createPpobMessage($order_product);
                    if ($msg_ppob) {
                        $msg_ppob = str_replace(PHP_EOL, '', $msg_ppob);
                        $msg_ppob = str_replace('<br>', PHP_EOL, $msg_ppob);
                        $isSuccesSendNotif = $helperSocialchat->sendMessage($conversationId, $msg_ppob);
                        if ($isSuccesSendNotif) {
                            $order_product->notif_sent_at = now();
                            $order_product->updated_at = now();
                            $order_product->save();
                        }
                    }
                }
            }
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Success to update order product',
            'data' => $data,
        ]);
    }

    public function checkBalance()
    {
        $helper_iak = new HelperIak();
        $path = '/api/check-balance';
        $additional_sign = 'bl';
        $method = 'POST';
        $params = [];
        $body = [];
        $result = $helper_iak->fetchApiIakPrepaid($path, $method, $params, $body, $additional_sign);
        if ($result && $result->rc === '00') {
            Setting::updateOrCreate(
                [
                    'name' => 'iak_balance',
                ],
                [
                    'value' => $result,
                    'updated_at' => now()
                ]
            );
            Cache::forget('iak_balance');
        }
        return response()->json($result);
    }
    public function getPrepaidInquiryPln($ppob_key, $customer_id = null, $product_id = null)
    {
        $helper_iak = new HelperIak();
        $path = '/api/inquiry-pln';
        $additional_sign = $ppob_key;
        $method = 'POST';
        $params = [];
        $body = [
            'customer_id' => $ppob_key,
        ];
        $result = $helper_iak->fetchApiIakPrepaid($path, $method, $params, $body, $additional_sign);
        return response()->json($result);
    }
    public function getPostpaidInquiryPdam($ppob_key, $product_code, $ref_id, $customer_id = null, $product_id = null)
    {
        $helper_iak = new HelperIak();
        $path = '/api/v1/bill/check';
        $custom_ref_id = $ppob_key . '-' . $ref_id;
        $additional_sign = $custom_ref_id;
        $method = 'POST';
        $params = [];
        $body = [
            'commands' => 'inq-pasca',
            'hp' => $ppob_key,
            'code' => $product_code,
            'ref_id' => $custom_ref_id,
        ];
        $result = $helper_iak->fetchApiIakPostpaid($path, $method, $params, $body, $additional_sign);
        if ($result && isset($result->response_code) && $result->response_code === '11') {
            $path = '/api/v1/bill/check';
            $additional_sign = 'cs';
            $method = 'POST';
            $params = [];
            $body = [
                'commands' => 'checkstatus',
                'ref_id' => $custom_ref_id,
            ];
            $result = $helper_iak->fetchApiIakPostpaid($path, $method, $params, $body, $additional_sign);
        }
        return response()->json($result);
    }

    public function getPostpaidPricelist(Request $request, $type = "", $operator = "")
    {
        $data = $request->all();
        $helper_iak = new HelperIak();
        $additional_sign = '';
        $path = '/api/v1/bill/check';
        $method = 'POST';
        $params = [];
        $body = [
            'status' => 'active'
        ];
        switch ($type) {
            case 'pdam':
                $path .= '/pdam';
                $additional_sign = 'pl';
                $body['commands'] = 'pricelist-pasca';
                break;
            // Add other cases as needed
            default:
                break;
        }
        $result = $helper_iak->fetchApiIakPostpaid($path, $method, $params, $body, $additional_sign);
        if ($result && isset($result->pasca) && !empty($result->pasca)) {
            PpobPricelist::updateOrCreate(
                [
                    'type' => $type,
                    'operator' => $operator,
                    'product_id' => $data['product_id'] ?? null,
                ],
                [
                    'data' => $result->pasca,
                    'updated_at' => now()
                ]
            );
        }
        return response()->json($result->pasca);
    }

    public function getPrepaidPricelist(Request $request, $type = "", $operator = "")
    {
        $data = $request->all();
        $helper_iak = new HelperIak();
        $additional_sign = '';
        $path = '/api/pricelist';
        switch ($type) {
            case 'etoll':
                $path .= '/etoll';
                $additional_sign = 'pl';
                break;
            case 'pln':
                $path .= '/pln';
                $additional_sign = 'pl';
                break;
            // Add other cases as needed
            default:
                break;
        }
        $method = 'POST';
        $params = [];
        $body = [
            'status' => 'active'
        ];
        $result = $helper_iak->fetchApiIakPrepaid($path, $method, $params, $body, $additional_sign);
        if ($result && isset($result->rc) && $result->rc === '00') {
            PpobPricelist::updateOrCreate(
                [
                    'type' => $type === 'etoll' ? 'emoney' : $type,
                    'operator' => $operator,
                    'product_id' => $data['product_id'] ?? null,
                ],
                [
                    'data' => $result->pricelist,
                    'updated_at' => now()
                ]
            );
        }
        return response()->json($result);
    }

    public function prepaidCheckStatus($ref_id)
    {
        $order_product = OrderProduct::where('ppob_ref_id', $ref_id)->with([
            'order',
            'product',
            'ppobresponsedata',
        ])->first();
        if (!$order_product) {
            return response()->json([
                'status' => 'error',
                'message' => 'Order product not found',
            ], 400);
        }
        $helper_iak = new HelperIak();

        // ? PLN Token & eMoney
        if (in_array($order_product->product->code, ['PLN_TKN', 'EMONEY'])) {
            // $additional_sign = '';
            $additional_sign = $ref_id;
            $path = '/api/check-status';
            $method = 'POST';
            $params = [];
            $body = [
                'ref_id' => $ref_id
            ];
            $result = $helper_iak->fetchApiIakPrepaid($path, $method, $params, $body, $additional_sign);
            // return response()->json($result);
            if ($result && isset($result->status)) {

                // ? If transaction not found, try to top-up
                if ($result->status === 2 && isset($result->rc) && $result->rc === '06') {
                    $result = $helper_iak->fetchApiIakPrepaid('/api/top-up', 'POST', [], [
                        'customer_id' => $order_product->ppob_key,
                        'product_code' => $order_product->ppob_product_code,
                        'ref_id' => $order_product->ppob_ref_id,
                    ], $order_product->ppob_ref_id);
                }
                switch ($result->status) {
                    case 0:
                        $order_product->ppob_status = 'PROCESS';
                        break;
                    case 1:
                        $order_product->ppob_status = 'SUCCESS';
                        break;
                    // case 2:
                    //     $order_product->ppob_status = 'FAILED';
                    //     break;
                    default:
                        $order_product->ppob_status = 'FAILED';
                        break;
                }
                $ppob_response_data = PpobResponseData::updateOrCreate(
                    [
                        'ppob_ref_id' => $ref_id,
                        // 'order_product_id' => $order_product->id,
                        'product_id' => $order_product->product_id,
                        'customer_id' => $order_product->order->customer_id,
                    ],
                    [
                        'response_data' => $result,
                        'error_message' => $result->status >= 2 ? $result->message : null,
                        'updated_at' => now()
                    ]
                );

                $order_product->ppob_response_data_id = $ppob_response_data->id;
                $order_product->updated_at = now();
                $order_product->save();

                // ? Send notif to customer
                // if (true) {
                if ($result->status === 1 && !$order_product->notif_sent_at) {
                    $helperSocialchat = new HelperSocialchat();

                    // ? Get conversation chat
                    $conversationId = $this->getConversationChat($order_product);

                    // ? Try send notif via socialchat
                    if ($conversationId) {
                        $helper = new Helper();
                        $msg_ppob = $helper->createPpobMessage($order_product);
                        if ($msg_ppob) {
                            $msg_ppob = str_replace(PHP_EOL, '', $msg_ppob);
                            $msg_ppob = str_replace('<br>', PHP_EOL, $msg_ppob);
                            $isSuccesSendNotif = $helperSocialchat->sendMessage($conversationId, $msg_ppob);
                            if ($isSuccesSendNotif) {
                                $order_product->notif_sent_at = now();
                                $order_product->updated_at = now();
                                $order_product->save();
                            }
                        }
                    }
                }

                $pivot = $order_product->withoutRelations();
                $ppobresponsedata = $ppob_response_data;
                return response()->json([
                    'status' => 'success',
                    'message' => 'Success to check status',
                    'response' => $result,
                    'pivot' => $pivot,
                    'ppobresponsedata' => $ppobresponsedata,
                ]);
            } else {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Failed to check status',
                    // 'data' => $result,
                ], 400);
            }

            // ? Air PDAM
        } else if ($order_product->product->code === 'AIR_PDAM') {
            $additional_sign = 'cs';
            $path = '/api/v1/bill/check';
            $method = 'POST';
            $params = [];
            $body = [
                'commands' => 'checkstatus',
                'ref_id' => $ref_id
            ];
            $result = $helper_iak->fetchApiIakPostpaid($path, $method, $params, $body, $additional_sign);
            if ($result && isset($result->status)) {
                switch ($result->status) {
                    case 3:
                        $order_product->ppob_status = 'PROCESS';
                        break;
                    case 1:
                        $order_product->ppob_status = 'SUCCESS';
                        break;
                    // case 2:
                    //     $order_product->ppob_status = 'FAILED';
                    //     break;
                    default:
                        $order_product->ppob_status = 'FAILED';
                        break;
                }
                $ppob_response_data = PpobResponseData::updateOrCreate(
                    [
                        'ppob_ref_id' => $ref_id,
                        // 'order_product_id' => $order_product->id,
                        'product_id' => $order_product->product_id,
                        'customer_id' => $order_product->order->customer_id,
                    ],
                    [
                        'response_data' => $result,
                        'error_message' => $result->status >= 2 ? $result->message : null,
                        'updated_at' => now()
                    ]
                );

                $order_product->ppob_response_data_id = $ppob_response_data->id;
                $order_product->updated_at = now();
                $order_product->save();

                // ? Send notif to customer
                // if (true) {
                if ($result->status === 1 && !$order_product->notif_sent_at) {
                    $helperSocialchat = new HelperSocialchat();

                    // ? Get conversation chat
                    $conversationId = $this->getConversationChat($order_product);

                    // ? Try send notif via socialchat
                    if ($conversationId) {
                        $helper = new Helper();
                        $msg_ppob = $helper->createPpobMessage($order_product);
                        if ($msg_ppob) {
                            $msg_ppob = str_replace(PHP_EOL, '', $msg_ppob);
                            $msg_ppob = str_replace('<br>', PHP_EOL, $msg_ppob);
                            $isSuccesSendNotif = $helperSocialchat->sendMessage($conversationId, $msg_ppob);
                            if ($isSuccesSendNotif) {
                                $order_product->notif_sent_at = now();
                                $order_product->updated_at = now();
                                $order_product->save();
                            }
                        }
                    }
                }

                $pivot = $order_product->withoutRelations();
                $ppobresponsedata = $ppob_response_data;
                return response()->json([
                    'status' => 'success',
                    'message' => 'Success to check status',
                    'response' => $result,
                    'pivot' => $pivot,
                    'ppobresponsedata' => $ppobresponsedata,
                ]);
            } else {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Failed to check status',
                    // 'data' => $result,
                ], 400);
            }
        }
    }

    private function getConversationChat($order_product)
    {
        $helperSocialchat = new HelperSocialchat();
        $conversationId = null;
        $order = $order_product->order;
        $phone = $order->receiver_phone ?? $order->customer->phone;
        $socialchat = Socialchat::where("store_id", $order->store_id)
            ->where('customer_id', $order->customer_id)
            ->where('phone', $phone)
            ->orderBy('id', 'desc')
            ->first();
        if ($socialchat) {
            $conversationId = $socialchat->conversation_id;
        } else {
            $conversationId = $helperSocialchat->getConversationId($order->store, $phone);
            if ($conversationId) {
                Socialchat::create([
                    'store_id' => $order->store_id,
                    'customer_id' => $order->customer_id,
                    'phone' => $phone,
                    'conversation_id' => $conversationId,
                ]);
            }
        }
        return $conversationId;
    }

    public function pricelistOfProduct($product_id)
    {
        $ppob_pricelist = PpobPricelist::where('product_id', $product_id)->first();
        $product = Product::find($product_id);

        if (!$product) {
            return '☕️ Pricelist not found!';
        }
        $helper_iak = new HelperIak();
        if (in_array($product->slug, ['emoney', 'pln-token-listrik'])) {
            $additional_sign = '';
            $operator = '';
            $type = [
                'emoney' => 'emoney',
                'pln-token-listrik' => 'pln',
            ];
            $path = '/api/pricelist';
            switch ($product->slug) {
                case 'emoney':
                    $path .= '/etoll';
                    $additional_sign = 'pl';
                    break;
                case 'pln-token-listrik':
                    $path .= '/pln';
                    $additional_sign = 'pl';
                    break;
                // Add other cases as needed
                default:
                    return '☕️ Pricelist not found!';
                    break;
            }
            $method = 'POST';
            $params = [];
            $body = [
                'status' => 'active'
            ];
            $result = $helper_iak->fetchApiIakPrepaid($path, $method, $params, $body, $additional_sign);
            if ($result && isset($result->rc) && $result->rc === '00') {
                $ppob_pricelist = PpobPricelist::updateOrCreate(
                    [
                        'type' => $type[$product->slug],
                        'operator' => $operator,
                        'product_id' => $product_id,
                    ],
                    [
                        'data' => $result->pricelist,
                        'updated_at' => now()
                    ]
                );
            }
        } else if (in_array($product->slug, ['air-pdam'])) {
            $additional_sign = '';
            $operator = '';
            $type = [
                'air-pdam' => 'pdam',
            ];
            $path = '/api/v1/bill/check';
            $method = 'POST';
            $params = [];
            $body = [
                'status' => 'active'
            ];
            switch ($product->slug) {
                case 'air-pdam':
                    $path .= '/pdam';
                    $additional_sign = 'pl';
                    $body['commands'] = 'pricelist-pasca';
                    break;
                // Add other cases as needed
                default:
                    return '☕️ Pricelist not found!';
                    break;
            }
            $result = $helper_iak->fetchApiIakPostpaid($path, $method, $params, $body, $additional_sign);
            if ($result && isset($result->pasca) && !empty($result->pasca)) {
                $ppob_pricelist = PpobPricelist::updateOrCreate(
                    [
                        'type' => $type[$product->slug],
                        'operator' => $operator,
                        'product_id' => $product_id,
                    ],
                    [
                        'data' => $result->pasca,
                        'updated_at' => now()
                    ]
                );
            }
        } else {
            return '☕️ Pricelist not found!';
        }

        $pricelist = $ppob_pricelist->data;
        foreach ($pricelist as &$item) {
            foreach ($item as $key => $value) {
                if (is_numeric($value)) {
                    $item[$key] = number_format((int) $value, 0, '', '.');
                }
            }
        }
        return view("sharp.product-ppob-iak-pricelist", compact(
            "product",
            "pricelist",
            "ppob_pricelist",
        ));
    }
}
