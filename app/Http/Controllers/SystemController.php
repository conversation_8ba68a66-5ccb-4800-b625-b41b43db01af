<?php

namespace App\Http\Controllers;

use App\Models\Setting;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class SystemController extends Controller
{
    public function updatestoreproduct()
    {
        return 'System Updated!';
    }

    public function webhook(Request $request)
    {
        $data = $request->all();
        $method = $request->method();
        $setting = Setting::updateOrCreate(
            [
                'name' => 'test-webhook',
            ],
            ['value' => [
                'test' => $data[0]['uuid'],
                'webhookdata' => $data,
                'method' => $method,
            ]]
        );
        return response()->json($setting);
    }

    public function testCallback(Request $request, $name)
    {
        if (empty($request->query('app_key')) || $request->query('app_key') != env('WEBHOOK_CLIENT_SECRET')) {
            return response()->json(['error' => 'Key invalid!'], 500);
        }
        $data = $request->all();
        $method = $request->method();
        $setting = Setting::updateOrCreate(
            [
                'name' => $name,
            ],
            ['value' => [
                'method' => $method,
                'response' => $data['data']['ref_id'],
            ]]
        );
        return response()->json($setting);
    }

    public function testWebhookSocialchat(Request $request)
    {
        return 'PAUSED';
        if (empty($request->query('app_key')) || $request->query('app_key') != env('WEBHOOK_CLIENT_SECRET')) {
            return response()->json(['error' => 'Key invalid!'], 500);
        }
        $data = $request->all();
        $method = $request->method();
        $setting = Setting::updateOrCreate(
            [
                'name' => 'socialchat-' . Str::random(10),
            ],
            ['value' => [
                'method' => $method,
                'data' => $data,
            ]]
        );
        return response()->json($setting);
    }
}
