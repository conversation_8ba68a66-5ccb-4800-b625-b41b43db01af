<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class AccountController extends Controller
{
    /**
     * Display the account page with dummy data.
     */
    public function index(Request $request): Response
    {
        // Sample stores data
        $stores = [
            ['id' => 1, 'slug' => 'store-1', 'name' => 'Gasplus Jakarta Pusat'],
            ['id' => 2, 'slug' => 'store-2', 'name' => 'Gasplus Jakarta Selatan'],
            ['id' => 3, 'slug' => 'store-3', 'name' => 'Gasplus Jakarta Utara'],
            ['id' => 4, 'slug' => 'store-4', 'name' => 'Gasplus Bandung'],
            ['id' => 5, 'slug' => 'store-5', 'name' => 'Gasplus Surabaya'],
        ];

        // Sample SDM (Human Resources) data
        $sdms = [
            [
                'id' => 1,
                'name' => '<PERSON>',
                'store_id' => 1,
                'role' => ['title' => 'Driver'],
                'employee' => [
                    [
                        'id' => 1,
                        'full_name' => '<PERSON>',
                    ]
                ]
            ],
            [
                'id' => 2,
                'name' => '<PERSON>i <PERSON>o',
                'store_id' => 2,
                'role' => ['title' => 'Driver'],
                'employee' => [
                    [
                        'id' => 2,
                        'full_name' => 'Budi Santoso Wijaya',
                    ]
                ]
            ],
            [
                'id' => 3,
                'name' => 'Candra Wijaya',
                'store_id' => 1,
                'role' => ['title' => 'Manager'],
                'employee' => []
            ],
            [
                'id' => 4,
                'name' => 'Dedi Kurniawan',
                'store_id' => 3,
                'role' => ['title' => 'Driver'],
                'employee' => [
                    [
                        'id' => 4,
                        'full_name' => 'Dedi Kurniawan Saputra',
                    ]
                ]
            ],
            [
                'id' => 5,
                'name' => 'Eko Prasetyo',
                'store_id' => 0, // Unassigned
                'role' => ['title' => 'Driver'],
                'employee' => [
                    [
                        'id' => 5,
                        'full_name' => 'Eko Prasetyo Nugroho',
                    ]
                ]
            ],
        ];

        // Sample Armada (Fleet) data
        $armadas = [
            [
                'id' => 1,
                'licence_number' => 'B 1234 ABC',
                'store_id' => 1,
                'employee_id' => 1,
            ],
            [
                'id' => 2,
                'licence_number' => 'B 5678 DEF',
                'store_id' => 2,
                'employee_id' => 2,
            ],
            [
                'id' => 3,
                'licence_number' => 'B 9012 GHI',
                'store_id' => 1,
                'employee_id' => 0, // Cadangan (Reserve)
            ],
            [
                'id' => 4,
                'licence_number' => 'B 3456 JKL',
                'store_id' => 3,
                'employee_id' => 4,
            ],
            [
                'id' => 5,
                'licence_number' => 'B 7890 MNO',
                'store_id' => 1,
                'employee_id' => 0, // Cadangan (Reserve)
            ],
        ];

        // Sample user data
        $user = [
            'id' => 1,
            'role_id' => 1, // Admin role
            'email' => '<EMAIL>',
            'name' => 'Admin User'
        ];

        // Get current store
        $storeSlug = $request->query('store', 'store-1');
        $currentStore = collect($stores)->firstWhere('slug', $storeSlug);
        $storeId = $currentStore ? $currentStore['id'] : 1;

        return Inertia::render('Account', [
            'store_slug' => $storeSlug,
            'store_id' => $storeId,
            'stores' => $stores,
            'sdms' => $sdms,
            'armadas' => $armadas,
            'user' => $user,
        ]);
    }
}
