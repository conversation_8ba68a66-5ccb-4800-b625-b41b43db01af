<?php

namespace App\Helper;

use Imagick;
// use App\Models\Store;
// use Illuminate\Support\Facades\File;
// use Illuminate\Database\Eloquent\Builder;
// use Illuminate\Filesystem\Filesystem;
// use Illuminate\Support\Facades\DB;

// use App\Jobs\ProcessNotif;
use GuzzleHttp\Client;

class HelperIak
{
    public function fetchApiIakPostpaid($path, $method, $params, $body, $additional_sign = "", $content_type = 'application/json', $is_all_page = false, $is_exact_host = false)
    {

        $client = new Client();

        $url = $is_exact_host ? $path : env('IAK_BASE_URL_POSTPAID') . $path;
        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }
        // if (!isset($body['status'])) {
        //     $body['status'] = 'all';
        // }
        $body['username'] = env('IAK_USERNAME');
        $body['sign'] = md5(env('IAK_USERNAME') . env('IAK_API_KEY') . $additional_sign);

        try {
            $response = $client->request($method, $url, [
                'json' => $body,
                'headers' => [
                    'Content-Type' => $content_type,
                ],
            ]);

            // ? Content Type JSON
            if ($content_type == 'application/json') {
                $json = $response->getBody();
                $result = json_decode($json);
                if ($result && isset($result->data)) {
                    return $result->data;
                }
                return $result;
            }
            // ? Content Type Image
            // if ($content_type == 'image/jpeg') {
            //     $imageContents = $response->getBody()->getContents();
            //     $fileName = 'downloaded_image.jpg';
            //     file_put_contents($fileName, $imageContents);
            //     return $fileName;
            // }
            $result = $response->getBody();

            if (!$result) {
                return null;
            }
            if ($result && isset($result->data)) {
                return $result->data;
            }
            return $result;
        } catch (\Exception $e) {
            return [
                'status' => 2,
                'message' => $e->getMessage(),
            ];
        }
    }

    public function fetchApiIakPrepaid($path, $method, $params, $body, $additional_sign = "", $content_type = 'application/json', $is_all_page = false, $is_exact_host = false)
    {

        $client = new Client();

        $url = $is_exact_host ? $path : env('IAK_BASE_URL_PREPAID') . $path;
        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }
        // if (!isset($body['status'])) {
        //     $body['status'] = 'all';
        // }
        $body['username'] = env('IAK_USERNAME');
        $body['sign'] = md5(env('IAK_USERNAME') . env('IAK_API_KEY') . $additional_sign);

        try {
            $response = $client->request($method, $url, [
                'json' => $body,
                'headers' => [
                    'Content-Type' => $content_type,
                ],
            ]);

            // ? Content Type JSON
            if ($content_type == 'application/json') {
                $json = $response->getBody();
                $result = json_decode($json);
                if ($result && isset($result->data)) {
                    return $result->data;
                }
                return $result;
            }
            // ? Content Type Image
            // if ($content_type == 'image/jpeg') {
            //     $imageContents = $response->getBody()->getContents();
            //     $fileName = 'downloaded_image.jpg';
            //     file_put_contents($fileName, $imageContents);
            //     return $fileName;
            // }
            $result = $response->getBody();

            if (!$result) {
                return null;
            }
            if ($result && isset($result->data)) {
                return $result->data;
            }
            return $result;
        } catch (\Exception $e) {
            return [
                'status' => 2,
                'message' => $e->getMessage(),
            ];
        }
    }

    public function getReceiptPostpaid($order, $tr_id)
    {
        $base_path = 'gasplus/iak/receipt/';
        $date = $order->created_at->format('Ymd');
        $file_name = $order->code . '-' . $tr_id;
        $directoryPath = public_path($base_path . $date);
        $pdf_path = $directoryPath . '/' . $file_name . '.pdf';
        if (file_exists($pdf_path)) {
            return [
                // 'path' => url('/public/' . $base_path . $date . '/' . $file_name . '.jpeg'),
                'path' => url('/public/' . $base_path . $date . '/' . $file_name . '.pdf'),
                'file_name' => $file_name,
            ];
        }
        $url = env('IAK_BASE_URL_POSTPAID') . '/api/v1/download/' . $tr_id;
        try {
            $client = new Client();
            $response = $client->request('GET', $url);
            $result_pdf = $response->getBody();

            // Check if the result is a PDF
            if (strpos($response->getHeaderLine('Content-Type'), 'application/pdf') === false) {

                $xml = simplexml_load_string($result_pdf);
                if ($xml && isset($xml->response_code) && (string)$xml->response_code !== '00') {
                    // throw new \Exception('Error: ' . (string)$xml->message);
                    return null;
                }
                // throw new \Exception('Error: The response is not a PDF.');
                return null;
            }
            if (!file_exists($directoryPath)) {
                mkdir($directoryPath, 0777, true);
            }
            file_put_contents($pdf_path, $result_pdf);

            // $imagick = new Imagick();
            // $imagick->readImageBlob($result_pdf);
            // $imagick->setImageBackgroundColor('white');
            // $imagick = $imagick->flattenImages();
            // $imagick->setImageFormat('jpeg');
            // $image_path = $directoryPath . '/' . $file_name . '.jpeg';
            // $imagick->writeImage($image_path);

            return [
                // 'path' => url('/public/' . $base_path . $date . '/' . $file_name . '.jpeg'),
                'path' => url('/public/' . $base_path . $date . '/' . $file_name . '.pdf'),
                'file_name' => $file_name,
            ];

            // $image_data = base64_encode(file_get_contents($image_path));

            // $helperSocialchat = new HelperSocialchat();
            // return $isSuccesSendNotifViaSocialchat = $helperSocialchat->sendMessage('66358a5aaf9ef08bd30a96fe', 'Test satu dua tiga', [
            //     'type' => 'image',
            //     'name' => 'receipt.jpeg',
            //     'mimetype' => 'image/jpeg',
            //     // 'url' => 'https://eforms.com/download/2019/01/Car-Vehicle-Receipt-Template.pdf',
            //     'url' => 'https://mm.uma.ac.id/wp-content/uploads/2023/01/google-doc.jpeg',
            //     // 'thumbnail' => $image_data,
            // ]);
        } catch (\Throwable $th) {
            // return $th;
            return null;
        }
    }
}