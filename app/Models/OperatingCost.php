<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
// use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Sqits\UserStamps\Concerns\HasUserStamps;

class OperatingCost extends Model
{
    use HasFactory, SoftDeletes, HasUserStamps;

    // protected $table = 'test_products';
    // public $timestamps = false;

    // protected $dates = [
    //     'created_at',
    //     'updated_at',
    //     'deleted_at',
    // ];

    protected $fillable = [
        'submit_at',
        'user_id',
        'store_id',
        'cost_category_id',
        'armada_id',
        'employee_id',
        'note',
    ];

    // public $rules = [
    //     'code' => 'required|string',
    // ];

    // Convert Input
    // protected $casts = [
    //     'is_admin' => 'boolean',
    // ];

    // Default Value
    // protected $attributes = [
    //     'sort_order' => 0,
    // ];

    public function operatingcostreceiptphotos()
    {
        return $this->morphMany(Media::class, "model")
            ->where("model_key", "operatingcostreceiptphotos");
    }

    public function items()
    {
        return $this->hasMany(OperatingCostItem::class, "operating_cost_id", "id");
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    public function costcategory()
    {
        return $this->belongsTo(CostCategory::class, 'cost_category_id', 'id');
    }

    public function armada()
    {
        return $this->belongsTo(Armada::class);
    }

    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }

    public function getDefaultAttributesFor($attribute)
    {
        return in_array($attribute, [
            "operatingcostreceiptphotos",
            ])
            ? ["model_key" => $attribute]
            : [];
    }
}