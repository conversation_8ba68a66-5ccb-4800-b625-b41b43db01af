<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Sqits\UserStamps\Concerns\HasUserStamps;

class Notification extends Model
{
    use HasFactory, HasUserStamps;

    protected $fillable = [
        'notifable_type',
        'notifable_id',
        'queue',
        'from',
        'to',
        'message',
        'footer',
        'forward_message',
        'status',
    ];

    protected $casts = [
        'forward_message' => 'array',
    ];


    public function notifable()
    {
        return $this->morphTo();
    }
}