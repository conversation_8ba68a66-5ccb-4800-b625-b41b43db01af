<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Sqits\UserStamps\Concerns\HasUserStamps;
use App\Models\Store;
use App\Helper\Helper;

class Address extends Model
{
    use HasFactory, SoftDeletes, HasUserStamps;

    // protected $table = 'test_products';
    // public $timestamps = false;

    // protected $dates = [
    //     'created_at',
    //     'updated_at',
    //     'deleted_at',
    // ];

    protected $fillable = [
        'store_id',
        'customer_id',
        'accurate_customer_id',
        'accurate_customer_code',
        'label',
        'address',
        'latlng',
        'area_id',
        'is_need_marketing',
        'is_secondfloor',
        'additionalcost_secondfloor',
        'socialchat_conversation_id',
        // 'created_at',
        // 'updated_at',
    ];

    public $rules = [
        'store_id' => 'required|integer',
        'area_id' => 'integer',
        'customer_id' => 'required|integer',
        'label' => 'required|string',
        'address' => 'required|string',
        'latlng' => 'string',
        // 'is_need_marketing' => 'string',
    ];

    // Convert Input
    // protected $casts = [
    //     'is_admin' => 'boolean',
    // ];

    // Default Value
    // protected $attributes = [
    //     'sort_order' => 0,
    // ];

    protected static function boot()
    {
        parent::boot();

        static::saved(function ($address) {
            $helper = new Helper;
            $stores = Store::where('city_id', $address->store->city_id)->get();
            $new_distance_sync = [];
            foreach ($stores as $store) {
                if (!$store->latlng) continue;
                if ($address->store->city_id !== $store->city_id) continue;
                $storedistance = $address->storedistances->where('pivot.store_id', $store->id)->first();
                $pivot_data = [
                    'distance_meter' => null,
                    'distance_meter_by_route' => null,
                ];
                if ($storedistance) {
                    $pivot_data = [
                        'distance_meter' => $storedistance->pivot->distance_meter,
                        'distance_meter_by_route' => $storedistance->pivot->distance_meter_by_route,
                    ];
                }
                if (!$pivot_data['distance_meter']) {
                    $distance = $helper->getDistance($store->latlng, $address->latlng);
                    $pivot_data['distance_meter'] = round($distance);
                }
                if (!$pivot_data['distance_meter_by_route'] && $pivot_data['distance_meter'] <= 7000) {
                    $distance_by_route = $helper->getDirectionMapbox($store->latlng, $address->latlng);
                    $pivot_data['distance_meter_by_route'] = $distance_by_route;
                }
                if ($storedistance) {
                    if ($storedistance->pivot->distance_meter !== $pivot_data['distance_meter'] || $storedistance->pivot->distance_meter_by_route !== $pivot_data['distance_meter_by_route']) {
                        $address->storedistances()->updateExistingPivot($store->id, $pivot_data);
                    }
                } else {
                    $new_distance_sync[$store->id] = $pivot_data;
                }
            }
            // return $distance_sync;
            // $address->storedistances()->updateExistingPivot($store->id, $pivot_data);
            if (count($new_distance_sync) > 0) {
                $address->storedistances()->syncWithoutDetaching($new_distance_sync);
            }
        });

        static::created(function ($model) {
            $helper = new Helper;
            $helper->accurateUpsertCustomer($model, true);
        });

        static::updated(function ($model) {
            $helper = new Helper;
            $helper->accurateUpsertCustomer($model);
        });

        static::deleted(function ($model) {
            $helper = new Helper;
            $helper->accurateDeleteCustomer($model);
        });
    }

    public function lastorder()
    {
        return $this->hasOne(Order::class)->latestOfMany();
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class)
            ->withTrashed();
    }

    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    public function storedistances()
    {
        // return $this->hasMany(StoreDistance::class);
        return $this->belongsToMany(Store::class, 'address_store_distance', 'address_id', 'store_id')
            ->withPivot('distance_meter', 'distance_meter_by_route')
            ->withTimestamps()
            ->orderBy('distance_meter')
            // ->select(
            //     'address_store_distance.id',
            //     'address_store_distance.store_id',
            //     'stores.name',
            // )
            //
        ;
    }

    public function area()
    {
        return $this->belongsTo(Area::class);
    }

    public function orders()
    {
        return $this->hasMany(Store::class);
    }

    public function photo()
    {
        return $this->morphOne(Media::class, "model")
            ->where("model_key", "photo");
    }

    public function getDefaultAttributesFor($attribute)
    {
        return in_array($attribute, ["photo"])
            ? ["model_key" => $attribute]
            : [];
    }
}