<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Statistic extends Model
{
    use HasFactory;

    // protected $table = 'test_products';
    // public $timestamps = false;

    // protected $dates = [
    //     'created_at',
    //     'updated_at',
    //     // 'deleted_at',
    // ];

    protected $fillable = [
        'title',
        'value',
        'updated_at'
    ];

    public $rules = [
        'title' => 'required',
    ];

    // Convert Input
    protected $casts = [
        'value' => 'array',
    ];

    // public function notifreminderimage01()
    // {
    //     return $this->morphOne(Media::class, "model")
    //         ->where("model_key", "notifreminderimage01");
    // }

    // public function notifreminderimage02()
    // {
    //     return $this->morphOne(Media::class, "model")
    //         ->where("model_key", "notifreminderimage02");
    // }

    // public function notifreminderimage03()
    // {
    //     return $this->morphOne(Media::class, "model")
    //         ->where("model_key", "notifreminderimage03");
    // }

    // public function getDefaultAttributesFor($attribute)
    // {
    //     return in_array($attribute, [
    //         "notifreminderimage01",
    //         "notifreminderimage02",
    //         "notifreminderimage03",
    //     ])
    //         ? ["model_key" => $attribute]
    //         : [];
    // }

    // Default Value
    // protected $attributes = [
    //     'sort_order' => 0,
    // ];
}
