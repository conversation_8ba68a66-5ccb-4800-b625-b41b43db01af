<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
// use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Sqits\UserStamps\Concerns\HasUserStamps;
use App\Models\Store;
// use App\Models\Customer;
use Carbon\Carbon;
use App\Helper\Helper;
use DateTime;

class Marketing extends Model
{
    use HasFactory, SoftDeletes, HasUserStamps;

    // protected $table = 'test_products';
    // public $timestamps = false;

    // protected $dates = [
    //     'created_at',
    //     'updated_at',
    //     'deleted_at',
    // ];

    protected $fillable = [
        'code',
        'store_id',
        // 'customer_id',
        'area_id',
        'driver_id',
        // 'bank_id',
        'cancel_by',
        // 'confirmed_by',
        // 'total',
        // 'amount_deposit_used',
        // 'deposit_balance_before',
        // 'deposit_balance_after',
        // 'total_after_deposit',
        'status_id',
        'duration',
        'distance_store_area',
        'distance_area_received',
        // 'payment',
        // 'payment_method_ask',
        // 'payment_method_confirmed',
        // 'amount_will_pay',
        // 'amount_return',
        // 'total_deposit',
        // 'amount_pay',
        // 'amount_split_to_cash',
        // 'note',
        'note_for_driver',
        // 'created_by',
        // 'receiver_phone',
        'received_at',
        // 'received_by',
        'received_latlng',
        'received_latlng_accuracy',
        // 'payment_note',
        'driver_note',
        // 'confirm_note',
        'is_offline',
        'created_at',
        'sequence_per_day',
        // 'updated_at',
    ];

    // public $rules = [
    //     'code' => 'required|string',
    //     'store_id' => 'required|integer',
    //     'customer_id' => 'required|integer',
    //     'address_id' => 'required|integer',
    //     'user_id' => 'required|integer',
    //     'total' => 'required|integer',
    //     'status_id' => 'required|integer',
    //     'distance' => 'integer',
    //     'payment' => 'string',
    //     'note' => 'string',
    //     'created_by' => 'required|integer',
    //     'received_by' => 'string',
    //     'driver_note' => 'string',
    // ];

    // Convert Input
    // protected $casts = [
    //     'is_admin' => 'boolean',
    // ];

    // Default Value
    // protected $attributes = [
    //     'sort_order' => 0,
    // ];

    public function getCode($store_id, $date = null, $sequence_per_day = null)
    {
        $order = new Marketing();
        $store = Store::find($store_id);
        $area = $store->area;
        $store_code = strtoupper($area[0]);
        $date = $date ? new DateTime($date) : new DateTime(Carbon::now());
        $current_date = $date->format('ymd');
        if (!$sequence_per_day) {
            $sequence_per_day = Marketing::where('store_id', $store_id)->whereDate('created_at', $date->format('Y-m-d'))->max('sequence_per_day') + 1;
        }
        // $order_count = $order->withTrashed()->where('store_id', $store->id)->whereDate('created_at', $date)->count();
        // $random_code = substr(md5(uniqid(mt_rand(), true)), 0, $length);
        $random_char = strtoupper(chr(rand(97, 122)));

        return $store_code . $current_date . '-B' . $sequence_per_day . $random_char;
    }

    protected static function booted()
    {
        $order = new Marketing();
        static::creating(function ($model) use ($order) {
            if ($model->area_id) {
                $date = new DateTime($model->created_at);
                $sequence_per_day = Marketing::where('store_id', $model->store_id)->whereDate('created_at', $date->format('Y-m-d'))->max('sequence_per_day') + 1;
                $model->sequence_per_day = $sequence_per_day;
                $model->code = $model->code ? $model->code : $order->getCode($model->store_id, null, $sequence_per_day);
                // $customer = Customer::find($model->customer_id);
                // $model->deposit_balance_before = $customer->deposit_amount;
            }
        });

        static::saving(function ($model) {
            $model->countDistance();
            $model->countDuration();
        });

        // static::saved(function ($model) {
        //     $helper = new Helper;
        //     $helper->countCalendar($model);
        //     // if ($model->status_id == 4 || $model->status_id == 6) {
        //     //     $helper->countPerformaProducts($model);
        //     //     $helper->countPerformaDrivers($model);
        //     // }
        // });
    }

    // public function products()
    // {
    //     return $this->belongsToMany(Product::class)
    //         ->withPivot('qty', 'price')
    //         ->withTrashed()
    //         ->withTimestamps();
    // }

    // public function orderproducts()
    // {
    //     return $this->hasMany(MarketingProduct::class);
    // }

    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    // public function bank()
    // {
    //     return $this->belongsTo(Bank::class);
    // }

    public function area()
    {
        return $this->belongsTo(Area::class)->withTrashed();
    }

    public function driver()
    {
        return $this->belongsTo(User::class, 'driver_id', 'id')->withTrashed();
    }

    public function cancelby()
    {
        return $this->belongsTo(User::class, 'cancel_by', 'id')->withTrashed();
    }

    // public function confirmedby()
    // {
    //     return $this->belongsTo(User::class, 'confirmed_by', 'id')->withTrashed();
    // }

    public function status()
    {
        return $this->belongsTo(Status::class);
    }

    // public function address()
    // {
    //     return $this->belongsTo(Address::class)->withTrashed();
    // }

    // public function feedback()
    // {
    //     return $this->hasOne(Feedback::class);
    // }

    public function photos()
    {
        return $this->morphMany(Media::class, "model")
            ->where("model_key", "photos")
            ->orderBy("order");
    }

    public function getDefaultAttributesFor($attribute)
    {
        return in_array($attribute, ["photos"])
            ? ["model_key" => $attribute]
            : [];
    }

    public function countDistance($manual = false)
    {
        $helper = new Helper();
        if ($manual) {
            $this->fresh();
        }

        if ($this->store && $this->area && $this->store->latlng && $this->area->latlng) {
            $this->distance_store_area = $helper->getDistance($this->store->latlng, $this->area->latlng);
        }

        if ($this->area && $this->received_latlng && $this->area->latlng) {
            $this->distance_area_received = $helper->getDistance($this->area->latlng, $this->received_latlng);
        }
        // $this->distance_store_customer = $helper->getDistance('-7.8157452,110.3434034', '-7.810886,110.3476412');

        if ($manual) {
            $this->save();
        }
    }

    public function countDuration($manual = false)
    {
        if ($manual) {
            $this->fresh();
        }

        if ($this->received_at) {
            $created = new DateTime($this->created_at);
            $received = new DateTime($this->received_at);
            $this->duration = abs($created->getTimestamp() - $received->getTimestamp());
        }

        if ($manual) {
            $this->save();
        }
    }

    // public function countTotal()
    // {   
    //     $this->fresh();

    //     $this->total = 0;
    //     foreach ($this->products as $product) {
    //         $this->total += $product->pivot->price * $product->pivot->qty;
    //     }

    //     $amount_deposit_used = $this->deposit_balance_before >= $this->total ? $this->total : $this->deposit_balance_before;
    //     $this->amount_deposit_used = $amount_deposit_used;
    //     $this->total_after_deposit = $this->total - $this->amount_deposit_used;
    //     if ($this->payment_method_ask == 'non-cash' && $this->status_id >= 5) {
    //         $this->deposit_balance_after = ($this->deposit_balance_before - $this->amount_deposit_used) + (($this->amount_split_to_cash + $this->amount_pay) - $this->total_after_deposit);
    //     } elseif ($this->payment_method_ask == 'non-cash' && $this->status_id == 4 && $this->deposit_balance_before != 0 && $this->amount_split_to_cash > 0) {
    //         $this->deposit_balance_after = ($this->deposit_balance_before - $this->amount_deposit_used) + (($this->amount_split_to_cash + $this->amount_pay) - $this->total_after_deposit);
    //     } else {
    //         $this->deposit_balance_after = $this->deposit_balance_before - $this->amount_deposit_used;
    //     }

    //     $this->amount_will_pay = ceil($this->total_after_deposit / 100000) * 100000;
    //     $this->amount_return = $this->amount_will_pay - $this->total_after_deposit;
    //     if (!$this->total_deposit) {
    //         $this->total_deposit = $this->amount_will_pay;
    //     }

    //     $this->save();
    // }
}
