<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Sqits\UserStamps\Concerns\HasUserStamps;

// use Illuminate\Database\Eloquent\SoftDeletes;

class City extends Model
{
    use HasFactory, HasUserStamps;
    // use SoftDeletes;

    // protected $table = 'test_products';
    // public $timestamps = false;

    // protected $dates = [
    //     'created_at',
    //     'updated_at',
    //     'deleted_at',
    // ];

    protected $fillable = [
        'slug',
        'name',
        'sort_order',
    ];

    // public $rules = [
    //     'slug' => 'required|string|unique:cities',
    //     'name' => 'required|string|unique:cities',
    // ];

    // Convert Input
    // protected $casts = [
    //     'is_admin' => 'boolean',
    // ];

    // Default Value
    // protected $attributes = [
    //     'sort_order' => 0,
    // ];

    protected static function boot()
    {
        parent::boot();

        City::creating(function ($model) {
            $model->sort_order = City::max('sort_order') + 1;
        });
    }

    public function stores()
    {
        return $this->hasMany(Store::class)->orderBy('sort_order');
        ;
    }
}
