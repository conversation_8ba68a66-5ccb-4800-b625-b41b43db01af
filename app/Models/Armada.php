<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
// use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Sqits\UserStamps\Concerns\HasUserStamps;

class Armada extends Model
{
    use HasFactory, SoftDeletes, HasUserStamps;

    // protected $table = 'armadas';
    // public $timestamps = false;

    // protected $dates = [
    //     'created_at',
    //     'updated_at',
    //     'deleted_at',
    // ];

    protected $fillable = [
        'employee_id',
        'vh_brand_id',
        'vh_model_id',
        'year',
        'licence_number',
        'chassis_number',
        'machine_number',
        'stnk_valid_until',
        'note',
    ];

    // public $rules = [
    //     'code' => 'required|string',
    // ];

    // Convert Input
    // protected $casts = [
    //     'is_admin' => 'boolean',
    // ];

    // Default Value
    // protected $attributes = [
    //     'sort_order' => 0,
    // ];

    public function stnkphoto()
    {
        return $this->morphOne(Media::class, "model")
            ->where("model_key", "stnkphoto");
    }

    public function frontphotos()
    {
        return $this->morphMany(Media::class, "model")
            ->where("model_key", "frontphotos");
    }

    public function sidephotos()
    {
        return $this->morphMany(Media::class, "model")
            ->where("model_key", "sidephotos");
    }

    public function backphotos()
    {
        return $this->morphMany(Media::class, "model")
            ->where("model_key", "backphotos");
    }

    public function operatingcosts()
    {
        return $this->hasMany(OperatingCost::class);
    }

    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }

    public function brand()
    {
        return $this->belongsTo(VhBrand::class, 'vh_brand_id', 'id');
    }

    public function model()
    {
        return $this->belongsTo(VhModel::class, 'vh_model_id', 'id');
    }

    public function getDefaultAttributesFor($attribute)
    {
        return in_array($attribute, ["stnkphoto", "frontphotos", 'sidephotos', "backphotos"])
            ? ["model_key" => $attribute]
            : [];
    }
}