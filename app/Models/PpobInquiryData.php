<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PpobInquiryData extends Model
{
    use HasFactory;
    public $timestamps = false;
    protected $table = 'ppob_inquiry_datas';

    protected $fillable = [
        'product_id',
        'customer_id',
        'ppob_ref_id',
        'ppob_product_code',
        'ppob_key',
        'ppob_tr_id',
        'data',
    ];

    protected $casts = [
        'data' => 'array',
    ];


    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }
}
