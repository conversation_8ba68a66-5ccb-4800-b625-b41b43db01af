<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
// use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Sqits\UserStamps\Concerns\HasUserStamps;
use App\Models\Store;
use App\Models\Customer;
use Carbon\Carbon;
use App\Helper\Helper;
use DateTime;

class Invoice extends Model
{
    use HasFactory, SoftDeletes, HasUserStamps;

    // protected $table = 'test_products';
    // public $timestamps = false;

    // protected $dates = [
    //     'created_at',
    //     'updated_at',
    //     'deleted_at',
    // ];

    protected $fillable = [
        'code',
        'sequence_per_day',
        'store_id',
        'customer_id',
        'receiver_phone',
        'total_bill',
        'discount',
        'total_after_discount',
        'payment_method_confirmed',
        'deposit_balance_before',
        'amount_deposit_used',
        'total_after_deposit',
        'deposit_balance_after',
        'amount_pay',
        'bank_id',
        'confirmed_by',
        'confirmed_at',
        'note',
        'note_confirm',
        'created_at',
    ];

    // public $rules = [
    //     'code' => 'required|string',
    //     'store_id' => 'required|integer',
    //     'customer_id' => 'required|integer',
    //     'address_id' => 'required|integer',
    //     'user_id' => 'required|integer',
    //     'total' => 'required|integer',
    //     'status_id' => 'required|integer',
    //     'distance' => 'integer',
    //     'payment' => 'string',
    //     'note' => 'string',
    //     'created_by' => 'required|integer',
    //     'received_by' => 'string',
    //     'driver_note' => 'string',
    // ];

    // Convert Input
    // protected $casts = [
    //     'is_admin' => 'boolean',
    // ];

    // Default Value
    // protected $attributes = [
    //     'sort_order' => 0,
    // ];

    public function getCode($store_id, $date = null, $sequence_per_day = null)
    {
        $order = new Invoice();
        $store = Store::find($store_id);
        $area = $store->area;
        $store_code = strtoupper($area[0]);
        $date = $date ? new DateTime($date) : new DateTime(Carbon::now());
        $current_date = $date->format('ymd');
        if (!$sequence_per_day) {
            $sequence_per_day = Invoice::where('store_id', $store_id)->whereDate('created_at', $date->format('Y-m-d'))->max('sequence_per_day') + 1;
        }
        // $order_count = $order->withTrashed()->where('store_id', $store->id)->whereDate('created_at', $date)->count();
        // $random_code = substr(md5(uniqid(mt_rand(), true)), 0, $length);
        $random_char = strtoupper(chr(rand(97, 122)));

        return 'INV-'.$store_code.$current_date.'-'.$sequence_per_day.$random_char;
    }

    protected static function booted()
    {
        $order = new Invoice();
        static::creating(function ($model) use ($order) {
            $date = new DateTime($model->created_at);
            $sequence_per_day = Invoice::where('store_id', $model->store_id)->whereDate('created_at', $date->format('Y-m-d'))->max('sequence_per_day') + 1;
            $model->sequence_per_day = $sequence_per_day;
            $model->code = $model->code ? $model->code : $order->getCode($model->store_id, null, $sequence_per_day);
        });

        // static::saving(function ($model) {
        //     $model->countTotal();
        // });

        // static::saved(function ($model) {
        //     $helper = new Helper;
        //     $helper->countCalendar($model);
        //     // if ($model->status_id == 4 || $model->status_id == 6) {
        //     //     $helper->countPerformaProducts($model);
        //     //     $helper->countPerformaDrivers($model);
        //     // }
        // });
    }

    // public function products()
    // {
    //     return $this->belongsToMany(Product::class)
    //         ->withPivot('qty', 'price')
    //         ->withTrashed()
    //         ->withTimestamps();
    // }

    // public function orderproducts()
    // {
    //     return $this->hasMany(InvoiceProduct::class);
    // }

    public function orders()
    {
        return $this->belongsToMany(Order::class)
            // ->withPivot('qty', 'price')
            ->withTrashed()
            ->withTimestamps();
    }

    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    public function bank()
    {
        return $this->belongsTo(Bank::class);
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class)->withTrashed();
    }

    public function confirmedby()
    {
        return $this->belongsTo(User::class, 'confirmed_by', 'id')->withTrashed();
    }

    public function paymentproof()
    {
        return $this->morphOne(Media::class, "model")
            ->where("model_key", "paymentproof");
    }

    public function getDefaultAttributesFor($attribute)
    {
        return in_array($attribute, ["paymentproof"])
            ? ["model_key" => $attribute]
            : [];
    }

    // public function countTotal($manual = false)
    // {
    //     if ($manual) {
    //         $this->fresh();
    //     }

    //     $this->total_after_discount = $this->total_bill - $this->discount;
        
    //     if ($manual) {
    //         $this->save();
    //     }
    // }
}