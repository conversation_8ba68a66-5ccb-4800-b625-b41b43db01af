<?php

namespace App\Models;

// use App\Models\Store;
// use App\Models\Customer;
// use App\Models\ProductStore;
// use App\Models\CustomerProduct;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
// use Illuminate\Database\Eloquent\SoftDeletes;
// use Sqits\UserStamps\Concerns\HasUserStamps;

class Blog extends Model
{
    use HasFactory;

    // protected $table = 'test_products';
    // public $timestamps = false;

    // protected $dates = [
    //     'created_at',
    //     'updated_at',
    //     'deleted_at',
    // ];

    protected $fillable = [
        'title',
        'slug',
        'exceprt',
        'content',
        'author',
        'published_at',
    ];

    // public $rules = [
    //     'slug' => 'required|string|unique:products',
    //     'name' => 'required|string',
    //     'code' => 'required|string|unique:products',
    //     'price' => 'required|integer',
    //     'minimum_order' => 'required|integer',
    //     'unit' => 'required|string',
    //     'link' => 'string',
    //     'categories' => 'array',
    //     'categories.*' => 'integer',
    //     'stores' => 'array',
    //     'stores.*' => 'integer',
    //     'featurephoto' => 'max:3000|image',
    //     // 'sort_order' => 'required|integer',
    // ];

    // Convert Input
    // protected $casts = [
    //     'is_admin' => 'boolean',
    // ];

    // Default Value
    // protected $attributes = [
    //     'sort_order' => 0,
    // ];

    // protected static function boot()
    // {
    //     parent::boot();

    //     Product::creating(function ($model) {
    //         $model->sort_order = Product::max('sort_order') + 1;
    //     });

    //     Product::created(function ($product) {
    //         $stores = Store::all();
    //         foreach ($stores as $store) {
    //             ProductStore::create([
    //                 'store_id' => $store->id,
    //                 'product_id' => $product->id,
    //             ]);
    //         }
    //         $customers = Customer::whereHas('products')->get();
    //         foreach ($customers as $customer) {
    //             CustomerProduct::create([
    //                 'customer_id' => $customer->id,
    //                 'product_id' => $product->id,
    //             ]);
    //         }
    //     });
    // }

    // public function categories()
    // {
    //     return $this->belongsToMany(Category::class)->withTimestamps();
    // }

    // public function stores()
    // {
    //     return $this->belongsToMany(Store::class)->withTimestamps()->wherePivot('is_available', '=', 1);
    // }

    // public function customers()
    // {
    //     return $this->belongsToMany(Customer::class)->withTimestamps();
    // }

    // public function productstores()
    // {
    //     return $this->hasMany(ProductStore::class);
    // }

    // public function customerproducts()
    // {
    //     return $this->hasMany(CustomerProduct::class);
    // }

    // public function ppobpricelist()
    // {
    //     return $this->hasOne(PpobPricelist::class);
    // }

    // public function orders()
    // {
    //     return $this->belongsToMany(Order::class)
    //         // ->using(OrderProduct::class)
    //         ->withPivot([
    //             'id',
    //             'qty',
    //             'price',
    //             'ppob_key',
    //             'ppob_label',
    //             'ppob_ref_id',
    //             'ppob_tr_id',
    //             'ppob_nominal',
    //             'ppob_price',
    //             'ppob_fee',
    //             'ppob_komisi',
    //             'ppob_product_code',
    //             'ppob_status',
    //             'accurate_invoice_item_id',
    //             'deleted_at',
    //             'notif_sent_at',
    //             'ppob_response_data_id'
    //         ])
    //         ->wherePivot('deleted_at', null)
    //         ->withTimestamps();
    // }

    public function featureimage()
    {
        return $this->morphOne(Media::class, "model")
            ->where("model_key", "featureimage");
    }

    // public function photos()
    // {
    //     return $this->morphMany(Media::class, "model")
    //         ->where("model_key", "photos")
    //         ->orderBy("order");
    // }

    public function getDefaultAttributesFor($attribute)
    {
        return in_array($attribute, ["featureimage"])
            ? ["model_key" => $attribute]
            : [];
    }

    // public function totalorders()
    // {
    //     return $this->morphMany(Total::class, 'totalable');
    // }

    // public function getIsPpobAttribute()
    // {
    //     return $this->categories->contains('slug', 'ppob');
    // }
}
