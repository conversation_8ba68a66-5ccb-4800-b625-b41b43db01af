<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PpobResponseData extends Model
{
    use HasFactory;
    // public $timestamps = false;
    protected $table = 'ppob_response_datas';

    protected $fillable = [
        'ppob_ref_id',
        'product_id',
        'customer_id',
        'response_data',
        'error_message',
        // 'order_product_id',
        'updated_at',
    ];

    protected $casts = [
        'response_data' => 'array',
    ];

    public function orderproduct()
    {
        return $this->hasOne(OrderProduct::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }
}