<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

// use Sqits\UserStamps\Concerns\HasUserStamps;
// use Illuminate\Database\Eloquent\SoftDeletes;

class VhModel extends Model
{
    use HasFactory;
    // use SoftDeletes;

    // protected $table = 'test_products';
    public $timestamps = false;

    // protected $dates = [
    //     'created_at',
    //     'updated_at',
    //     'deleted_at',
    // ];

    protected $fillable = [
        'name',
        'vh_brand_id',
    ];

    // public $rules = [
    //     'slug' => 'required|string|unique:cities',
    //     'name' => 'required|string|unique:cities',
    // ];

    // Convert Input
    // protected $casts = [
    //     'is_admin' => 'boolean',
    // ];

    // Default Value
    // protected $attributes = [
    //     'sort_order' => 0,
    // ];

    // protected static function booted()
    // {
    //     static::creating(function ($model) {
    //         $model->sort_order = Self::max('sort_order') + 1;
    //     });
    // }

    public function armada()
    {
        return $this->hasMany(Armada::class, 'vh_model_id', 'id');
    }

    public function brand()
    {
        return $this->belongsTo(VhBrand::class, 'vh_brand_id', 'id');
    }

    public function vhmodelphoto()
    {
        return $this->morphOne(Media::class, "model")
            ->where("model_key", "vhmodelphoto");
    }

    public function getDefaultAttributesFor($attribute)
    {
        return in_array($attribute, ["vhmodelphoto"])
            ? ["model_key" => $attribute]
            : [];
    }
}