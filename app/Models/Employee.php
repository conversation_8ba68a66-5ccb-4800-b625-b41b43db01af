<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
// use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Sqits\UserStamps\Concerns\HasUserStamps;

class Employee extends Model
{
    use HasFactory, SoftDeletes, HasUserStamps;

    // protected $table = 'test_products';
    // public $timestamps = false;

    // protected $dates = [
    //     'created_at',
    //     'updated_at',
    //     'deleted_at',
    // ];

    protected $fillable = [
        'user_id',
        'full_name',
        'dob',
        'address',
        'kk_number',
        'ktp_number',
        'sim_number',
        'sim_valid_until',
        'start_work_at',
        'quit_work_at',
        'quit_note',
        'note',
    ];

    // public $rules = [
    //     'code' => 'required|string',
    // ];

    // Convert Input
    // protected $casts = [
    //     'is_admin' => 'boolean',
    // ];

    // Default Value
    // protected $attributes = [
    //     'sort_order' => 0,
    // ];

    public function profilephoto()
    {
        return $this->morphOne(Media::class, "model")
            ->where("model_key", "profilephoto");
    }

    public function ktpphoto()
    {
        return $this->morphOne(Media::class, "model")
            ->where("model_key", "ktpphoto");
    }

    public function kkphoto()
    {
        return $this->morphOne(Media::class, "model")
            ->where("model_key", "kkphoto");
    }

    public function simphoto()
    {
        return $this->morphOne(Media::class, "model")
            ->where("model_key", "simphoto");
    }

    public function user()
    {
        return $this->belongsTo(User::class)
            ->withTrashed();
    }

    public function armada()
    {
        return $this->hasOne(Armada::class);
        // return $this->hasMany(Armada::class);
    }

    public function operatingcosts()
    {
        return $this->hasMany(OperatingCost::class);
    }

    public function getDefaultAttributesFor($attribute)
    {
        return in_array($attribute, [
            "profilephoto",
            "ktpphoto",
            "kkphoto",
            "simphoto",
            ])
            ? ["model_key" => $attribute]
            : [];
    }
}