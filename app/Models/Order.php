<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
// use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Sqits\UserStamps\Concerns\HasUserStamps;
use App\Models\Store;
use App\Models\Customer;
use Carbon\Carbon;
use App\Helper\Helper;
use DateTime;

class Order extends Model
{
    use HasFactory, SoftDeletes, HasUserStamps;

    // protected $table = 'test_products';
    // public $timestamps = false;

    protected $dates = [
        'created_at',
        'updated_at',
        'deleted_at',
        'received_at',
    ];

    protected $fillable = [
        'is_urgent',
        'code',
        'store_id',
        'customer_id',
        'address_id',
        'driver_id',
        'armada_id',
        'bank_id',
        'cancel_by',
        'confirmed_by',
        'deposit_job',
        'total',
        'amount_deposit_used',
        'deposit_balance_before',
        'deposit_balance_after',
        'total_after_deposit',
        'status_id',
        'duration',
        'distance_store_customer',
        'distance_customer_received',
        'payment',
        'payment_method_ask',
        'payment_method_confirmed',
        'amount_will_pay',
        'amount_return',
        'total_deposit',
        'amount_pay',
        'amount_split_to_cash',
        'note',
        'note_for_driver',
        // 'created_by',
        'receiver_phone',
        'received_at',
        'received_by',
        'received_latlng',
        'received_latlng_accuracy',
        'payment_note',
        'driver_note',
        'confirm_note',
        'is_offline',
        'is_invoiced',
        'created_at',
        'sequence_per_day',
        'options',
        // 'updated_at',
        // 'deleted_at',
    ];

    // public $rules = [
    //     'code' => 'required|string',
    //     'store_id' => 'required|integer',
    //     'customer_id' => 'required|integer',
    //     'address_id' => 'required|integer',
    //     'user_id' => 'required|integer',
    //     'total' => 'required|integer',
    //     'status_id' => 'required|integer',
    //     'distance' => 'integer',
    //     'payment' => 'string',
    //     'note' => 'string',
    //     'created_by' => 'required|integer',
    //     'received_by' => 'string',
    //     'driver_note' => 'string',
    // ];

    // Convert Input
    protected $casts = [
        'options' => 'array',
    ];

    // Default Value
    // protected $attributes = [
    //     'sort_order' => 0,
    // ];

    public function getCode($store_id, $date = null, $sequence_per_day = null)
    {
        $order = new Order();
        $store = Store::find($store_id);
        $area = $store->area;
        $store_code = strtoupper($area[0]);
        $date = $date ? new DateTime($date) : new DateTime(Carbon::now());
        $current_date = $date->format('ymd');
        if (!$sequence_per_day) {
            $sequence_per_day = Order::where('store_id', $store_id)->whereDate('created_at', $date->format('Y-m-d'))->max('sequence_per_day') + 1;
        }
        // $order_count = $order->withTrashed()->where('store_id', $store->id)->whereDate('created_at', $date)->count();
        // $random_code = substr(md5(uniqid(mt_rand(), true)), 0, $length);
        $random_char = strtoupper(chr(rand(97, 122)));

        return $store_code . $current_date . '-' . $sequence_per_day . $random_char;
    }

    protected static function booted()
    {
        $order = new Order();
        static::creating(function ($model) use ($order) {
            $date = new DateTime($model->created_at);
            $sequence_per_day = Order::where('store_id', $model->store_id)->whereDate('created_at', $date->format('Y-m-d'))->max('sequence_per_day') + 1;
            $model->sequence_per_day = $sequence_per_day;
            $model->code = $model->code ? $model->code : $order->getCode($model->store_id, null, $sequence_per_day);
            $customer = Customer::find($model->customer_id);
            $model->deposit_balance_before = $customer->deposit_amount;
        });

        // static::created(function ($model) {
        //     if ($model->address->is_secondfloor) {
        //         $additionalcost = $model->address->additionalcost_secondfloor > 0 ? $model->address->additionalcost_secondfloor : $model->store->additionalcost_secondfloor;
        //         $model->additionalcosts()->create([
        //             'name' => 'Biaya antar lantai atas',
        //             'cost' => $additionalcost,
        //             'min_unit' => 2,
        //             'round' => 'up',
        //         ]);
        //     }
        // });

        static::saving(function ($model) {
            $model->countDistance();
            $model->countDuration();
        });

        // ? Accurate Sync
        static::updated(function ($model) {
            $helper = new Helper;
            $helper->accurateUpsertInvoice($model);
        });
        static::deleted(function ($model) {
            $helper = new Helper;
            $helper->accurateDeleteInvoice($model);
        });

        // static::saved(function ($model) {
        //     $model->countDistance(true);
        //     // $helper = new Helper;
        //     // $helper->countCalendar($model);
        //     // if ($model->status_id == 4 || $model->status_id == 6) {
        //     //     $helper->countPerformaProducts($model);
        //     //     $helper->countPerformaDrivers($model);
        //     // }
        // });
    }

    public function products()
    {
        return $this->belongsToMany(Product::class)
            // ->using(OrderProduct::class)
            ->withPivot([
                'id',
                'qty',
                'price',
                'ppob_key',
                'ppob_label',
                'ppob_ref_id',
                'ppob_tr_id',
                'ppob_nominal',
                'ppob_price',
                'ppob_fee',
                'ppob_komisi',
                'ppob_product_code',
                'ppob_status',
                'accurate_invoice_item_id',
                'deleted_at',
                'notif_sent_at',
                'ppob_response_data_id'
            ])
            ->wherePivot('deleted_at', null)
            // ->withTrashed()
            ->withTimestamps();
    }

    public function orderproducts()
    {
        return $this->hasMany(OrderProduct::class);
    }

    public function accurateInvoice()
    {
        return $this->morphOne(AccurateData::class, 'accuratable')
            ->where("accuratable_key", "sales-invoice");
    }

    public function accurateReceipt()
    {
        return $this->morphOne(AccurateData::class, 'accuratable')
            ->where("accuratable_key", "sales-receipt");
    }

    public function additionalcosts()
    {
        return $this->hasMany(AdditionalCost::class);
    }

    public function invoices()
    {
        return $this->belongsToMany(Invoice::class)->withTimestamps();
    }

    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    public function bank()
    {
        return $this->belongsTo(Bank::class);
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class)->withTrashed();
    }

    public function driver()
    {
        return $this->belongsTo(User::class, 'driver_id', 'id')->withTrashed();
    }

    public function armada()
    {
        return $this->belongsTo(Armada::class, 'armada_id', 'id')->withTrashed();
    }

    public function cancelby()
    {
        return $this->belongsTo(User::class, 'cancel_by', 'id')->withTrashed();
    }

    public function confirmedby()
    {
        return $this->belongsTo(User::class, 'confirmed_by', 'id')->withTrashed();
    }

    public function status()
    {
        return $this->belongsTo(Status::class);
    }

    public function address()
    {
        return $this->belongsTo(Address::class)->withTrashed();
    }

    public function feedback()
    {
        return $this->hasOne(Feedback::class);
    }

    public function notifications()
    {
        return $this->morphMany(Notification::class, 'notifable');
    }

    public function receivephoto()
    {
        return $this->morphOne(Media::class, "model")
            ->where("model_key", "receivephoto");
    }

    public function paymentproof()
    {
        return $this->morphOne(Media::class, "model")
            ->where("model_key", "paymentproof");
    }

    public function getDefaultAttributesFor($attribute)
    {
        return in_array($attribute, ["receivephoto", 'paymentproof'])
            ? ["model_key" => $attribute]
            : [];
    }

    public function countDistance($manual = false)
    {
        $helper = new Helper();

        if ($manual) {
            $this->fresh();
        }

        if ($this->store && $this->address && $this->store->latlng && $this->address->latlng) {
            $distance_by_route = $helper->getDirectionMapbox($this->store->latlng, $this->address->latlng);
            $this->distance_store_customer = $distance_by_route > 0 ? $distance_by_route : $helper->getDistance($this->store->latlng, $this->address->latlng);
        }

        if ($this->address && $this->received_latlng && $this->address->latlng) {
            $this->distance_customer_received = $helper->getDistance($this->address->latlng, $this->received_latlng);
        }
        // $this->distance_store_customer = $helper->getDistance('-7.8157452,110.3434034', '-7.810886,110.3476412');

        if ($manual) {
            $this->save();
        }
    }

    public function countDuration($manual = false)
    {
        if ($manual) {
            $this->fresh();
        }

        if ($this->received_at) {
            $createdAt = new DateTime($this->created_at);
            $openAt = new DateTime($createdAt->format('Y-m-d') . ' ' . $this->store->open_hour);
            $created = $createdAt < $openAt ? $openAt : $createdAt;
            $received = new DateTime($this->received_at);
            $this->duration = abs($created->getTimestamp() - $received->getTimestamp());
        }

        if ($manual) {
            $this->save();
        }
    }

    public function setAdditionalCost($force_add = false)
    {
        // $this->fresh();

        if ($this->address->is_secondfloor || $force_add) {
            $total_qty_products_secondfloor = 0;
            foreach ($this->products as $product) {
                // if ($product->categories()->whereIn('slug', ['lpg', 'galon'])->exists()) {
                $total_qty_products_secondfloor += $product->pivot->qty;
                // }
            }
            if ($total_qty_products_secondfloor > 0) {
                $min_unit = 2;
                $round = 'up';
                $additionalcost = $this->address->additionalcost_secondfloor > 0 ? $this->address->additionalcost_secondfloor : $this->store->additionalcost_secondfloor;
                $total_cost = ceil($total_qty_products_secondfloor / $min_unit) * $additionalcost;
                if ($this->additionalcosts->count() > 0) {
                    $this->additionalcosts->first()->update([
                        'name' => 'Biaya antar lantai atas',
                        'cost' => $additionalcost,
                        'min_unit' => $min_unit,
                        'round' => $round,
                        'total_cost' => $total_cost,
                    ]);
                } else {
                    $this->additionalcosts()->create([
                        'name' => 'Biaya antar lantai atas',
                        'cost' => $additionalcost,
                        'min_unit' => $min_unit,
                        'round' => $round,
                        'total_cost' => $total_cost,
                    ]);
                }
                // $this->total += $total_cost;
            }
        }
        $this->load(['additionalcosts']);
    }

    public function countTotal()
    {
        $this->fresh();

        $this->total = 0;
        foreach ($this->products as $product) {
            $this->total += $product->pivot->price * $product->pivot->qty;
        }
        foreach ($this->additionalcosts as $ac) {
            $this->total += $ac->total_cost;
        }

        $amount_deposit_used = $this->deposit_balance_before >= $this->total ? $this->total : $this->deposit_balance_before;
        $this->amount_deposit_used = $amount_deposit_used;
        $this->total_after_deposit = $this->total - $this->amount_deposit_used;
        if ($this->payment_method_ask == 'non-cash' && $this->status_id >= 5) {
            $this->deposit_balance_after = ($this->deposit_balance_before - $this->amount_deposit_used) + (($this->amount_split_to_cash + $this->amount_pay) - $this->total_after_deposit);
        } elseif ($this->payment_method_ask == 'non-cash' && $this->status_id == 4 && $this->deposit_balance_before != 0 && $this->amount_split_to_cash > 0) {
            $this->deposit_balance_after = ($this->deposit_balance_before - $this->amount_deposit_used) + (($this->amount_split_to_cash + $this->amount_pay) - $this->total_after_deposit);
        } else {
            $this->deposit_balance_after = $this->deposit_balance_before - $this->amount_deposit_used;
        }
        // $this->deposit_balance_after = ($this->deposit_balance_before - $this->amount_deposit_used) + (($this->amount_split_to_cash + $this->amount_pay) - $this->total_after_deposit);

        $this->amount_will_pay = ceil($this->total_after_deposit / 100000) * 100000;
        $this->amount_return = $this->amount_will_pay - $this->total_after_deposit;
        if (!$this->total_deposit) {
            $this->total_deposit = $this->amount_will_pay;
        }

        $this->save();
    }
}
