<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Sqits\UserStamps\Concerns\HasUserStamps;

// use Illuminate\Database\Eloquent\SoftDeletes;

class Status extends Model
{
    use HasFactory, HasUserStamps;
    // use SoftDeletes;

    protected $table = 'statuses';
    // public $timestamps = false;

    // protected $dates = [
    //     'created_at',
    //     'updated_at',
    //     'deleted_at',
    // ];

    protected $fillable = [
        'code',
        'name',
        'color',
        'icon',
    ];

    // public $rules = [
    //     'code' => 'required|string|unique:statuses',
    //     'name' => 'required|string|unique:statuses',
    // ];

    // Convert Input
    // protected $casts = [
    //     'is_admin' => 'boolean',
    // ];

    // Default Value
    // protected $attributes = [
    //     'sort_order' => 0,
    // ];

    // protected static function boot()
    // {
    //     parent::boot();

    //     Category::creating(function ($model) {
    //         $model->sort_order = Category::max('sort_order') + 1;
    //     });
    // }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function totalorders()
    {
        return $this->morphMany(Total::class, 'totalable');
    }
}