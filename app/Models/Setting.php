<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
// use Illuminate\Database\Eloquent\SoftDeletes;
use Sqits\UserStamps\Concerns\HasUserStamps;
// use App\Models\Store;
// use App\Helper\Helper;

class Setting extends Model
{
    use HasFactory;
    use HasUserStamps;

    // protected $table = 'test_products';
    // public $timestamps = false;

    // protected $dates = [
    //     'created_at',
    //     'updated_at',
    //     // 'deleted_at',
    // ];

    protected $fillable = [
        'name',
        'value',
        'updated_at'
    ];

    public $rules = [
        'name' => 'required',
    ];

    // Convert Input
    protected $casts = [
        'value' => 'array',
    ];

    public function notifreminderimage01()
    {
        return $this->morphOne(Media::class, "model")
            ->where("model_key", "notifreminderimage01");
    }

    public function notifreminderimage02()
    {
        return $this->morphOne(Media::class, "model")
            ->where("model_key", "notifreminderimage02");
    }

    public function notifreminderimage03()
    {
        return $this->morphOne(Media::class, "model")
            ->where("model_key", "notifreminderimage03");
    }

    public function getDefaultAttributesFor($attribute)
    {
        return in_array($attribute, [
            "notifreminderimage01",
            "notifreminderimage02",
            "notifreminderimage03",
        ])
            ? ["model_key" => $attribute]
            : [];
    }

    // Default Value
    // protected $attributes = [
    //     'sort_order' => 0,
    // ];
}
