<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
// use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
// use Illuminate\Database\Eloquent\SoftDeletes;
// use Sqits\UserStamps\Concerns\HasUserStamps;

class StoreDistance extends Model
{
    use HasFactory;

    protected $table = 'address_store_distance';
    // public $timestamps = false;

    // protected $dates = [
    //     'created_at',
    //     'updated_at',
    //     'deleted_at',
    // ];

    protected $fillable = [
        'address_id',
        'store_id',
        'distance_meter',
        'distance_meter_by_route',
    ];

    // public $rules = [
    //     'code' => 'required|string',
    // ];

    // Convert Input
    // protected $casts = [
    //     'is_root' => 'boolean',
    // ];

    // Default Value
    // protected $attributes = [
    //     'sort_order' => 0,
    // ];

    public function address()
    {
        return $this->belongsTo(Address::class);
    }

    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    // public function children()
    // {
    //     return $this->hasMany(StoreDistance::class, 'parent_id', 'id');
    // }

    // public function operatingcosts()
    // {
    //     return $this->hasMany(OperatingCost::class);
    // }

    // public function operatingcostitems()
    // {
    //     return $this->hasMany(OperatingCostItem::class);
    // }
}
