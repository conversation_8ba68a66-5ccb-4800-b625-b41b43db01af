!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).mdtoast=e()}(this,function(){"use strict";function a(t,e){for(var o=0;o<e.length;o++){var n=e[o];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}var i={defaults:{init:!1,duration:5e3,type:"default",modal:!1,interaction:!1,interactionTimeout:null,actionText:"OK",action:function(){this.hide()},callbacks:{}},toastOpenClass:"mdtoast--open",toastModalClass:"mdtoast--modal"};function s(t,e,o,n){var a=document.createElement(t);return a.className=e,void 0!==o&&(a[n?"innerHTML":"innerText"]=o),a}function c(){function t(t){t.target.matches(".mdt-action")&&("click"===t.type||"keypress"===t.type&&13===t.keyCode)&&a.action&&a.action.call(n,t)}var e,o,n=this,a=n.options;n.docFrag=document.createDocumentFragment(),n.toast=s("div","mdtoast mdt--load"),n.toast.tabIndex=0,n.docFrag.appendChild(n.toast),"default"!==a.type&&n.toast.classList.add("mdt--"+a.type),e=s("div","mdt-message",n.message,!0),n.toast.appendChild(e),o=s("span","mdt-action"),a.interaction&&(o.innerText=a.actionText,o.tabIndex=0,n.toast.classList.add("mdt--interactive"),n.toast.appendChild(o)),n.toast.addEventListener("click",t,!1),n.toast.addEventListener("keypress",t,!1),(n.toast.mdtoast=n).options.init||n.show()}function l(t){var e=this,o=document.body,n=e.options.callbacks;o.appendChild(e.docFrag),setTimeout(function(){e.toast.classList.remove("mdt--load"),setTimeout(function(){n&&n.shown&&n.shown.call(e),t&&"function"==typeof t&&t.call(e)},e.animateTime),e.options.interaction?e.options.interactionTimeout&&(e.timeout=setTimeout(function(){e.hide()},e.options.interactionTimeout)):e.options.duration&&(e.timeout=setTimeout(function(){e.hide()},e.options.duration)),o.classList.add(i.toastOpenClass),e.options.modal&&o.classList.add(i.toastModalClass)},15)}var o=function(){function n(t,e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,n);var o=arguments;this.animateTime=230,this.message=o[0],this.options=function o(t){var n={},a=!1,e=0,i=arguments.length;"[object Boolean]"===Object.prototype.toString.call(t)&&(a=t,e++);for(;e<i;e++)!function(t){for(var e in t)Object.prototype.hasOwnProperty.call(t,e)&&(a&&"[object Object]"===Object.prototype.toString.call(t[e])?n[e]=o(!0,n[e],t[e]):n[e]=t[e])}(arguments[e]);return n}(!0,i.defaults,o[1]),this.timeout=null,this.options.init||c.call(this)}var t,e,o;return t=n,(e=[{key:"show",value:function(t){var e=this,o=document.getElementsByClassName("mdtoast");if(!document.body.contains(e.toast))if(e.options.init&&c.apply(e),0<o.length)for(var n=o.length-1;0<=n;n--)o[n].mdtoast.hide(function(){n<0&&l.call(e,t)});else l.call(e,t)}},{key:"hide",value:function(t){var e=this,o=e.options.callbacks,n=document.body;clearTimeout(e.timeout),e.toast.classList.add("mdt--load"),n.classList.remove(i.toastOpenClass),n.classList.remove(i.toastModalClass),setTimeout(function(){n.removeChild(e.toast),o&&o.hidden&&o.hidden.call(e),t&&"function"==typeof t&&t.call(e)},e.animateTime)}}])&&a(t.prototype,e),o&&a(t,o),n}();function t(t,e){return new o(t,e=e||{})}return Object.defineProperties(t,{INFO:{value:"info"},ERROR:{value:"error"},WARNING:{value:"warning"},SUCCESS:{value:"success"}}),Element.prototype.matches||(Element.prototype.matches=Element.prototype.matchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector||Element.prototype.webkitMatchesSelector||function(t){for(var e=(this.document||this.ownerDocument).querySelectorAll(t),o=e.length;0<=--o&&e.item(o)!==this;);return-1<o}),t});