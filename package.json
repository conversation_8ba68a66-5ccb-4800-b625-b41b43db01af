{"private": true, "scripts": {"s": "php artisan schedule:work", "dev": "npm run development", "development": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --progress --config=node_modules/laravel-mix/setup/webpack.config.js", "w": "npm run development -- --watch", "watch-poll": "npm run watch -- --watch-poll", "hot": "cross-env NODE_ENV=development node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --hot --disable-host-check --config=node_modules/laravel-mix/setup/webpack.config.js", "p": "npm run production", "production": "cross-env NODE_ENV=production node_modules/webpack/bin/webpack.js --no-progress --config=node_modules/laravel-mix/setup/webpack.config.js", "deploy-dev": "git config git-ftp.url 'ftp://************' && git config git-ftp.user 'test-ftp' && git config git-ftp.password '05OHZJoAWtp01QQ2nlPN' && git ftp push -v", "deploy-production": "git config git-ftp.url 'ftp://************' && git config git-ftp.user 'csvps-ftp' && git config git-ftp.password 'p71EjOSZCVGjcOQWr35O' && git ftp push -v", "c": "php artisan cache:clear & php artisan config:cache & php artisan route:cache & php artisan view:cache & php artisan queue:restart & composer dump-autoload & composer install --optimize-autoloader --no-dev"}, "devDependencies": {"@tailwindcss/forms": "^0.2.1", "@tailwindcss/typography": "^0.4.1", "alpinejs": "^2.7.3", "axios": "^0.19", "babel-plugin-transform-runtime": "^6.23.0", "babel-runtime": "^6.26.0", "bootstrap-sass": "^3.4.1", "cross-env": "^7.0.2", "jquery": "^3.5.1", "laravel-mix": "^5.0.1", "lodash": "^4.17.19", "postcss-import": "^12.0.1", "prettier": "^2.8.4", "resolve-url-loader": "^3.1.0", "sass": "^1.30.0", "sass-loader": "^8.0.2", "sharp-plugin": "0.0.2", "tailwind-scrollbar-hide": "^1.1.7", "tailwindcss-textshadow": "^2.1.3", "vue": "^2.6.12", "vue-template-compiler": "^2.6.12"}, "dependencies": {"@tailwindcss/line-clamp": "^0.4.2", "autoprefixer": "^9.8.6", "postcss": "^7.0.35", "tailwindcss": "npm:@tailwindcss/postcss7-compat@^2.1.4", "v-money": "^0.8.1", "vue-select": "^3.20.3", "vue-underscore": "^0.1.4"}}