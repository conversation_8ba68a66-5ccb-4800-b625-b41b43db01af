var staticCacheName = "pwa-v" + new Date().getTime();
var filesToCache = [
    "/offline",
    "/public/css/mdtoast.min.css",
    "/public/css/animate.min.css",
    "/public/css/selectize.bootstrap3.css",
    "/public/css/leaflet.awesome-markers.css",
    "/public/css/manager.css",
    "/public/css/app.css",
    "/public/js/webcam-easy.min.js",
    "/public/js/jquery.min.js",
    "/public/js/moment.min.js",
    "/public/js/mdtoast.min.js",
    "/public/js/axios.min.js",
    "/public/js/selectize.min.js",
    "/public/js/medium-zoom.min.js",
    "/public/js/leaflet.awesome-markers.js",
    "/public/js/popper.min.js",
    "/public/js/tippy-bundle.umd.min.js",
    "/public/js/sweetalert2.all.min.js",
    "/public/js/app.js",
    "/images/icons/icon-72x72.png",
    "/images/icons/icon-96x96.png",
    "/images/icons/icon-128x128.png",
    "/images/icons/icon-144x144.png",
    "/images/icons/icon-152x152.png",
    "/images/icons/icon-192x192.png",
    "/images/icons/icon-384x384.png",
    "/images/icons/icon-512x512.png",
];

// Cache on install
self.addEventListener("install", (event) => {
    this.skipWaiting();
    event.waitUntil(
        caches.open(staticCacheName).then((cache) => {
            return cache.addAll(filesToCache);
        })
    );
});

// Clear cache on activate
self.addEventListener("activate", (event) => {
    event.waitUntil(
        caches.keys().then((cacheNames) => {
            return Promise.all(
                cacheNames
                    .filter((cacheName) => cacheName.startsWith("pwa-"))
                    .filter((cacheName) => cacheName !== staticCacheName)
                    .map((cacheName) => caches.delete(cacheName))
            );
        })
    );
});

// Serve from Cache
self.addEventListener("fetch", (event) => {
    event.respondWith(
        caches
            .match(event.request)
            .then((response) => {
                return response || fetch(event.request);
            })
            .catch(() => {
                return caches.match("offline");
            })
    );
});
